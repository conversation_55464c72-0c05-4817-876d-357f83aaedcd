import {
  userStorage,
  link,
  api,
  global,
} from '../../common/index.js'
import config from '../const/config.js'

const Store = require('../../lib/store.js')

let store = new Store({
  state: {
    //以下为自定义的全局状态，用法和页面中的data: {...} 一致。
    user: undefined,
    //全局UI属性
    config,
    // 全局色值
    themeColor: '#E8340Fff',
    // logo
    logo: '',
    //牛投邦统一授权unionId
    unionId: undefined,
    //展业中心微信信息
    wechatInfo: {
      id: undefined
    },
  },
  methods: {
    //全局方法。 page中调用 this.xxx() wxml中bindtap="xxx" 非页面js文件getCurrentPages().pop().xxx()
    goAnyWhere(e) {
      console.log('===== goAnyWhere e >>', e)
      wx.navigateTo({
        url: e.currentTarget.dataset.url
      })
    },

    updateUser(user) {
      userStorage.saveUser(user)
      store.setState({
        user
      })
    },

    getUser() {
      let user = getApp().store.$state.user
      //当前全局中不存在 分别从storage中取
      if (!user){
        user = userStorage.getUser()
        user && store.setState({
          user
        })
      }
      return user
    },

    wxmlTagATap(e) {
      link.linkUrl(e.detail.src || e.currentTarget.dataset.src || "")
    },

    async uploadFormIds() {
      let formIds = getApp().formIds
      if (formIds && formIds.length > 0){
        try {
          let unionId = await getApp().getUnionId()
          let params = {sourceCode: global.SOURCE_CODE, formIds, unionId}
          if (getApp().store.$state.user){
            params.userId = getApp().store.$state.user.id
          }
          const {code} = await api.addFormId(params)
          if (code == 0){
            //清空formIds数组
            getApp().formIds = []
          }
        } catch (e) {
          // console.log('nbDebugger:uploadFormIds', e)
        }
      }
    },

    goBack(param) {
      let pages = getCurrentPages()
      if (pages.length < 2){

      } else {
        wx.navigateBack({
          delta: param ? 2 : 1,
        })
      }
    }
  },

  pageListener: {
    async onLoad(options = {}) {
      console.log('store onload options >>>>', options)
    }
  }
})
module.exports = store
