import {global, interaction, util, storage, eventName, enums, md5, log} from "../index";
import {checkLogin, getAdvStaffCardInfo, getUnionId} from "../nb/home";
import {
  getBaseParams,
  getUser,
  getUserRole,
  saveUser,
  setBaseParams,
  setOrgId,
  setToken,
  setUserLoginStatus,
  setUserRole,
  getToken
} from "../utils/userStorage";

const {LoginState, BREAK_FUNC_NAME} = enums;
const {isEmptyObject} = util;
const {BREAK_IN_INIT_SUCCESS} = eventName;
const FILE_TYPES = [".pdf"];

const breakContent = {
  /**
   * 微信登录 + 获取unionId
   */
  [BREAK_FUNC_NAME.initWechatLogin]: {
    subscription: async (params) => {
      const baseParams = getBaseParams();
      const sensors = params.params
      // console.log('sensorssensorssensors', baseParams)
      if (!isEmptyObject(baseParams)){
        sensors.login(baseParams.unionid)
        // console.log('sensors.login(baseParams.unionid)', sensors.login)
        return breakIn({name: BREAK_FUNC_NAME.checkLogin, param: baseParams});
      }
      await wx.login({
        success(res = {}) {
          const {code = ""} = res;
          if (code){
            getUnionId({code, wechatCode: global.SOURCE_CODE})
              .then((result = {}) => {
                console.log("BREAK_IN onGetUnionId getUnionId result >>>>", result);
                const {success, param = {}, msg = ""} = result;
                if (!success){
                  interaction.showToast(msg);
                }
                setBaseParams(param);
                sensors.login(param.unionid)
                if (!isEmptyObject(param)){
                  return breakIn({name: BREAK_FUNC_NAME.checkLogin, param});
                }
              })
              .catch(err => {
                console.log("BREAK_IN onGetUnionId getUnionId err >>>>", err);
              });
          }
        },
        fail(err) {
          console.log("BREAK_IN wx.login fail err >>>>", err);
        }
      });
    }
  },
  /**
   * 检查登录状态
   */
  [BREAK_FUNC_NAME.checkLogin]: {
    subscription: async (params = {}) => {
      const {param = {}} = params;
      console.log("BREAK_IN doCheckLogin param >>>>", param);
      const {msg, success, data, code} = await checkLogin(param);
      log.info("BREAKIN>>checkLogin",code,data)
      console.log("BREAK_IN doCheckLogin msg, success, data, code >>>>", msg, success, data, code);
      storage.setStorage(global.STORAGE_GLOBAL_USER_ROLE, code);
      setUserRole(code);
      console.log("BREAK_IN doCheckLogin msg, success, data, code >>>>>", msg, success, data, code);
      let targetPath = "/pages/home/<USER>";
      if (success){
        const {token = "", userStatus = "", expiration = ''} = data;
        setToken(token);
        wx.setStorageSync("storage_data_expiration",Date.parse(expiration) / 1000)
        // 审核不通过
        if (userStatus === "D"){
          setUserLoginStatus(false);
          storage.setStorage(global.STORAGE_GLOBAL_USER_ROLE, LoginState.BEREJUST);
          // return wx.reLaunch({
          //   url: `/package-surely/pages/approval/applyStatus/index`
          // });
        }else{
          setUserLoginStatus(true);
        }
        getApp()?.event?.emit(BREAK_IN_INIT_SUCCESS);
        return breakIn({name: BREAK_FUNC_NAME.getUserInfo});
      }
      setUserLoginStatus(false);
      switch (code) {
        // 审核不通过
        case LoginState.BEREJUST:
        // 账号审核中，请稍后登录
        case LoginState.INREVIEW:
          storage.setStorage(global.STORAGE_GLOBAL_START_UP_STATUS, true);
          // targetPath = `/package-surely/pages/approval/applyStatus/index`;
          // break;
        // 账号禁用
        case LoginState.FORBIDDEN:
          storage.setStorage(global.STORAGE_GLOBAL_START_UP_STATUS, true);
          // targetPath = "/package-activity/pages/loginAndRegist/applyFail/applyFail";
          // break;
        // 未登录/游客
        case LoginState.NO_BANDING:
        case LoginState.ABSENCE:
          return getApp()?.event?.emit(BREAK_IN_INIT_SUCCESS);
        default:
          break;
      }
      return wx.reLaunch({
        url: targetPath
      });
    }
  },
  /**
   * 获取用户信息
   */
  [BREAK_FUNC_NAME.getUserInfo]: {
    subscription: async (params = {}) => {
      console.log("BREAK_IN getUserInfo params >>>>", params);
      let userInfo = getUser();
      const {success, data = {}, msg = "", code} = await getAdvStaffCardInfo({timeStamp: +new Date()});
      console.log("BREAK_IN getUserInfo success, data, msg, code >>>>", success, data, msg, code);
      if (success){
        const {orgId = ""} = data;
        setOrgId(orgId);
        userInfo = {
          ...userInfo,
          ...data
        };
        if (userInfo && Object.keys(userInfo).length) {
          for (let [key, value] of Object.entries(userInfo)) {
            storage.setStorage(key, value);
          }
        }
        saveUser(userInfo);
      }
      return userInfo;
    }
  },
  /**
   * 跳转登录页
   */
  [BREAK_FUNC_NAME.goToLoginPage]: {
    subscription: (params = {}) => {
      console.log("BREAK_IN goToLoginPage params >>>>", params);
      const userRole = getUserRole();
      // 审核中 审核等待页
      if (userRole === LoginState.INREVIEW){
        return wx.reLaunch({
          url: `/package-surely/pages/approval/applyStatus/index`
        });
      }
      return wx.reLaunch({
        url: `/pages/loginAndRegist/login/login?loginCode=${userRole}`
      });
    }
  },
  // 清除缓存文件
  [BREAK_FUNC_NAME.doClearSaveFiles]: {
    subscription: () => {
      const FM = wx.getFileSystemManager()
      FM.stat({
        path: `${wx.env.USER_DATA_PATH}/`,
        recursive: true,
        success: (res) => {
          console.log('BREAK_IN doClearSaveFiles res >>>',res)
          const stats = res?.stats
 
          console.time('DO_CLEAR_SAVE_FILES')
          for (const item of stats) {
            let filePath = item.path || ''
            console.log('BREAK_IN doClearSaveFiles FM.unlink filePath >>>', filePath)
            FILE_TYPES.forEach((fType) => {
              if (filePath.indexOf(`${fType}`) !== -1){
                if (!`${filePath}`.startsWith('/')){
                  filePath = `/${filePath}`
                }
                FM.unlink({
                  filePath: `${wx.env.USER_DATA_PATH + filePath}`,
                  success: (res) => {
                    // log.info('BREAK_IN FM.unlink doClearSaveFiles res >>>', res)
                  },
                  fail: (err) => {
                    log.error('BREAK_IN FM.unlink doClearSaveFiles err >>>', err)
                  }
                })
              }
            })
          }
          console.timeEnd('DO_CLEAR_SAVE_FILES')
        },
        fail: (err) => {
          log.error('BREAK_IN doClearSaveFiles err >>>', err)
        },
        complete: () => {
          getApp().globalData.filePath = ''
        }
      })
    }
  },

  [BREAK_FUNC_NAME.checkUserTokenState]:{
    subscription: async (params = {}) => {
      const {param = {}} = params;
      const {next: storageToken = '',aging = ''} = param
      if(!storageToken ||  !aging)
        return
      log.info('token确认更新,旧token为：',getToken())
      const baseParams = getBaseParams();
      let checkParams  = {}
      if(!isEmptyObject(baseParams)){
        checkParams = baseParams
      }else{
        checkParams = {
          openid: getOpenId(),
          unionid: geStorageUnionId(),
        }
      }
      const {msg, success, data, code} = await checkLogin(checkParams);
      log.info("接口返回是否成功：",success,'token更新时checkLogin返回值为：',data)
      if(success){
        setToken(data.token);
      }
    }
  }
};
export default function breakIn(params = {}) {
  const {name = ""} = params;
  return breakContent[`${name}`].subscription(params);
}
