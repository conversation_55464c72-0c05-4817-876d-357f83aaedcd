//wbs/opensaas/ac 统一处理转换文件
import regeneratorRuntime from '../../lib/regenerator-runtime/runtime.js'
import reViewConfig from '../const/review'
import {getPageTemplateInfo} from "../nb/home";
import {global, storage} from "../index";
import tabBars_JSON from '../../json/tabbar.json'

const convertObj = {
  'tabBar': {
    subscription: async () => {
      const {code, data, msg, success} = await getPageTemplateInfo({})
      console.log('==== CONVERT tabBar getPageTemplateInfo code, data, msg, success >>>>', code, data, msg, success)
      // if (!success){
      // console.log('==== CONVERT success #### >>>>', success)
      // console.log('==== CONVERT tabBars_JSON.list #### >>>>', tabBars_JSON.tabbars.list)
      //}

      let _tBars = []
      if (data && data instanceof Array) {
        // 排序
        _tBars = data.sort(function (a, b) {
          return a.ordinal - b.ordinal
        })

        // 过滤
        _tBars = _tBars.filter(item => item && item.status === 'ENABLE')

        // 解析
        _tBars = _tBars.map((item, index) => {
          return {
            ...item,
            pageShow: true,
            icon: JSON.parse(item.icon),
            pageTabBarConf: JSON.parse(item.pageTabBarConf)
          }
        })

        // 赋值type
        _tBars = _tBars.map((vItem, vIndex) => {
          const {pageTabBarConf} = vItem || {}
          return {
            ...vItem,
            type: pageTabBarConf && pageTabBarConf.type || vIndex
          }
        })
        // console.log('======= CONVERT _tBars >>>>', _tBars)
        const tabBarInfo = {
          backgroundColor: "#FFFFFF",
          color: "#CCCCCC",
          selectorColor: '#f04b28ff',
          list: _tBars
        }
        // console.log('======= CONVERT tabBarInfo >>>>', tabBarInfo)
        storage.setStorage(global.STORAGE_GLOBAL_TABBAR, tabBarInfo)
      }
      return _tBars.length && _tBars || reViewConfig.tabbar
    }
  },
}

export default function convert(params) {
  const {name = ''} = params || {}
  return convertObj[name].subscription(params)
}
