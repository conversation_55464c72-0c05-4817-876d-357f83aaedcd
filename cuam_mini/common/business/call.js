//咨询逻辑封装（打电话）
import {
  api,
  eventName,
  interaction
} from '../index.js'

import * as enums from '../const/enum.js'
import * as validator from '../utils/validator.js'
const {
  IndependentAdviserStatus: {
    SUCCESS,
    DISMISSION
  }
} = enums

import {
  throttle
} from "../utils/util.js"

//咨询
export function consult(useFaOutId = true) {
  const user = getCurrentPages().pop().getUser()
  // console.log('consult方法的user信息：',user )
  const { faOutId: shareFaOutId } = getApp().store.$state.shareData
  // console.log('shareData的shareFaOutId信息：',shareFaOutId )

  if (!validator.isEmpty(shareFaOutId)) {
    callAdvisorPhone(shareFaOutId)
  } else {
    if (useFaOutId && user) {
      if (user.faOutId) {
        callAdvisorPhone(user.faOutId)
      } else {
        callCompayPhone()
      }
    } else {
      callCompayPhone()
    }
  }
}

function callAdvisorPhone(faOutId) {
  // console.log('调用理财师电话')

  const that = this
  interaction.showLoading()
  api.shareAdvisor({ value: faOutId }).then((res) => {
    interaction.hideLoading()
    const { data: { independent, mobile, status } } = res
    //非有效理财顾问拨打公司电话
    let advisorLegal = (independent != 1 && status != DISMISSION) || (independent == 1 && status == SUCCESS)
    if (advisorLegal) {
      wx.makePhoneCall({
        phoneNumber: mobile
      })
    } else {
      callCompayPhone()
    }
  }).catch((e) => {
    interaction.hideLoading()
    // console.error('shareAdvisor Promise', e)
  })
}

function callCompayPhone() {
  // console.log('调用公司电话')
  interaction.showLoading()
  api.getCompanyDetail({}).then(res => {
    interaction.hideLoading()
    const { data: { phone } } = res
    // console.log('api.getCompanyDetail', phone, res)
    if (validator.isEmpty(phone)) {
      return interaction.showToast('号码为空')
    }
    wx.makePhoneCall({
      phoneNumber: phone
    })
  }).catch(e => {
    interaction.hideLoading()
    // console.error('getCompanyDetail Promise', e)
  })
}

//请TA联系我 业务逻辑封装
export function contactMe(data, callback) {
  const user = getCurrentPages().pop().getUser()
  const { params, isShare: share, isIMChat=false, origin} = data
  const { faOutId: shareFaOutId, enterSource } = getApp().store.$state.shareData
  // console.log(shareFaOutId, user, '--shareFaOutId--')
  let isShare = share || !!enterSource
  /**
   * params 是预约接口所需的参数
   * isShare ture 外部分享/ false 小程序内部打开
   * shareFaOutId 外部分享进来的理财师id
   * callback 切换小程序名片信息回调用
   */
  if (user) {
    if (user.faOutId) {
      // 已登录且当前用户有理财顾问
      // console.log('已登陆且当前用户有理财顾问')
      interaction.showLoading()
      const userOutId = user.faOutId
      api.shareAdvisor({value: userOutId}).then(res => {
        interaction.hideLoading()
        const currentUserAdvisor = res.data
        const { independent, status } = currentUserAdvisor
        currentUserAdvisor.outId = userOutId
        if (!isAdvisorLegal(currentUserAdvisor)) { //公司名片
          updateAdavisor(null, callback)//更新理财师名片信息
          goToChatDetail(origin)
          return
        }
        if (validator.isEmpty(shareFaOutId)) {
          sendMsg({ params, currentUserAdvisor, isShare, origin, callback, isIMChat })
          return
        }
        if (userOutId === shareFaOutId) {//小程序当前的理财顾问和分享进来的理财顾问一致
          // console.log('小程序当前的理财顾问和分享进来的理财顾问一致')
          sendMsg({ params, currentUserAdvisor, isShare, origin, callback, isIMChat })
        } else {
          // console.log('小程序当前的理财顾问和分享进来的理财顾问不一致')
          if (!isShare) {//从小程序自身打开
            // console.log('从小程序自身打开')
            sendMsg({ params, currentUserAdvisor, isShare, origin, callback, isIMChat })
          } else { //从外部分享打开 弹窗提示
            // console.log('从外部分享打开 弹窗提示')
            wx.showModal({
              title: '确认',
              content: `您的专属咨询顾问${currentUserAdvisor.advisorName}，将会为您提供服务。`,
              cancelText: '取消',
              confirmText: '继续预约',
              success(res) {
                // on confirm
                if (res.confirm) {
                  sendMsg({ params, currentUserAdvisor, isShare, origin, callback, isIMChat })
                }
              }
            })
          }
        }
      }).catch((e) => {
        interaction.hideLoading()
        // console.log('error', e)
      })
    } else {
      //用户没有理财师
      if (isIMChat) {
        updateAdavisor(null, callback)
        goToChatDetail(origin)
        return
      }
      interaction.showLoading()
      api.authContactAdvisor(params).then(res => {
        interaction.hideLoading()
        wx.showToast({
          title: '预约成功，您的专属咨询顾问会联系您',
          icon: 'none',
        })
        if (isShare) { //从外部分享打开 且当前小程序未携带理财师信息 更新理财师名片信息
          // console.log('从外部分享打开 且当前小程序未携带理财师信息 更新理财师名片信息', callback)
          updateAdavisor(null, callback)
        }
      }).catch((e) => {
        interaction.hideLoading()
        // console.log('error', e)
      })
    }
  } else {
    // 未登录
    // console.log('isIMChat状态:', isIMChat)

    if (isIMChat) {
      interaction.showLoading()
      api.shareAdvisor({ value: shareFaOutId }).then(res => {
        interaction.hideLoading()
        const shareAdvisorData = res.data
        if (isAdvisorLegal(shareAdvisorData)){
          goToChatDetail({
            faOutId: shareFaOutId,
            ...origin
          })
        }else{
          updateAdavisor(null, callback)
          goToChatDetail(origin)
        }
      })
      return
    }
    let page = getCurrentPages().pop();
    if (page.route == 'pages/loginAndRegist/login/login'){
      return
    }
    wx.navigateTo({
      url: '/pages/loginAndRegist/login/login',
    })
  }
}

function isAdvisorLegal(advisor){
  if(!advisor){
    return false
  }
  const { independent, status } = advisor
  let isLegal = (independent != 1 && status != DISMISSION) || (independent == 1 && status == SUCCESS)
  return isLegal
}

//内部理财顾问需要往app发送预约信息，外部理财顾问不发送消息 toast提示
function sendMsg(data) {
  const { params, currentUserAdvisor, isShare, origin, callback, isIMChat = false} = data
  const { independent, advisorName, outId} = currentUserAdvisor
  // console.log('sendMsg接受的参数',data)
  if(isIMChat){
    if (isShare) {
      updateAdavisor(currentUserAdvisor, callback)
    }
    goToChatDetail({
      faOutId: outId,
      ...origin
    })
    return
  }
  if (independent === 0) { //内部理财顾问 发送消息

    // console.log('内部理财顾问 发送消息')

    api.authContactAdvisor(params).then(res => {
      wx.showToast({
        title: '预约成功，您的专属咨询顾问会联系您',
        icon: 'none',
      })
      if (isShare) {
        // console.log('更新理财师名片信息:', currentUserAdvisor, callback )
        updateAdavisor(currentUserAdvisor, callback)//更新理财师名片信息
      }
    })
  } else {// 外部理财顾问不发送消息 toast提示

    // console.log('外部理财顾问不发送消息 toast提示')

    wx.showToast({
      title: '预约成功，您的专属咨询顾问会联系您',
      icon: 'none',
    })
    if (isShare) {
      // console.log('更新理财师名片信息:', currentUserAdvisor, callback )
      updateAdavisor(currentUserAdvisor, callback)//更新理财师名片信息
    }
  }
}

/**
 * 替换成用户自己的理财师信息
 */
function updateAdavisor(currentUserAdvisor, callback) {
  getApp().store.setState({
    shareData: {}
  }, () => {
    getApp().event.emit(eventName.UPDATE_ADVISOR, currentUserAdvisor)
  })
  callback && callback(currentUserAdvisor)

}

let goToChatDetail = throttle(function goToChatDetail(params){
  let url = `/pages/mine/chat/list/index?into=1`
  for (let i in params) {
    url += `&${i}=${params[i]}`
  }
  wx.navigateTo({
    url
  })
},2000)
