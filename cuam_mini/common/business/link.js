//咨询逻辑封装（打电话）
import {
  config,
} from '../index.js'

export function linkUrl(url) {
  const { legalDomain } = config
  // console.log('legalDomain', legalDomain)
  if (!url) return wx.showToast({
    title: '链接为空',
    icon: 'none',
  })
  let isLegalDomain
  try {
    legalDomain.forEach((item, index) => {
      if (url.startsWith(item)) {
        isLegalDomain = true
        throw new Error("EndIterative")
      }
    })
  } catch (e) {
    // console.log('EndIterative')
  }
  if (!isLegalDomain) {
    wx.showModal({
      title: '这是一个外部链接',
      content: '小程序暂不支持打开外部链接，可点击复制链接后在浏览器中粘贴查看',
      showCancel: true,
      confirmText: '复制链接',
      success(res) {
        if (res.confirm) {
          wx.setClipboardData({
            data: url,
            success(res) {
              // console.log('复制剪切板'+ url)
            }
          })
          // console.log('用户点击确定')
        } else if (res.cancel) {
          // console.log('用户点击取消')
        }
      }
    })
  } else {
    wx.navigateTo({
      url: '/pages/common/webview/webPage?url=' + encodeURIComponent(url),
    })
  }

}
