import {
    qs,
    wbs,
    userStorage,
    enums,
    storage,
    global
} from '../index'

import { getPermission } from  '../nb/home'

const {
    geStorageUnionId,
    getToken,
    getWechatInfoId,
    getOpenId,
    setWebPageShowWay
} = userStorage

const {
    DETAIL_URL,
    LoginState,
    JUMP_TARGET,
    PAGE_INFO_REALM,
    LOGIN_VISITOR,
    actionType,
} = enums

const wxTextPerfix = 'mp.weixin.qq'

const jumpTargetPage = {
  [JUMP_TARGET.QR_LIVE_DETAIL]: {
    subscription: async (option = {}) => {
      const { params = {}} = option
      let targetParams = {
        id: params.id,
        isFromChannel: true,
        shareUserId: params.faId,
        isSelectedFloat: params.isSelectedFloat
      }
      let targetPath = `/package-activity/pages/live/live?${qs.stringify(targetParams,{encode:false})}`
      let url = `${wbs.gfH5}/share/advAuthorizeLoading?targetPath=${encodeURIComponent(targetPath)}&unionid=${geStorageUnionId()}`
      let fullUrl = `/pages/common/webview/webPage?url=${encodeURIComponent(url)}`
      return navigator(params.isSelectedFloat,fullUrl,params)
    }
  },
  [JUMP_TARGET.QR_LIVE_LIST]: {
    subscription: async (option = {}) => {
      const { params = {}} = option
      let targetParams = {
        categoryIds: params.categoryIds,
        name: params.name,
        shareUserId: params.faId,
        isFromChannel: true,
        type: 'LIVE',
        isSelectedFloat: params.isSelectedFloat,
        selectedFloat: !params?.isSelectedFloat&&params?.selectedFloat || '',
        conf:params.selectedFloatConf,
        cardId:params.contentConfigId
      }
      let fullUrl = `/pages/home/<USER>/list?${qs.stringify(targetParams, { encode: false })}`
      return navigator(params.isSelectedFloat,fullUrl,params)
    }
  },
  [JUMP_TARGET.MARKETNEWS_LIST]: {
    subscription: async (option = {}) => {
      const { params = {}} = option
      let targetParams = {
        id: params.categoryIds,
        name: params.name,
        isFromChannel: true,
        shareUserId: params.faId,
        conf: params.conf,
        queryDate: params.queryDate,
        isSelectedFloat: params.isSelectedFloat
      }
      let fullUrl = `/package-activity/pages/marketnewsList/index?${qs.stringify(targetParams, { encode: false })}`
      return navigator(params.isSelectedFloat,fullUrl,params)
    }
  },
  [JUMP_TARGET.TEMP_LIST]: {
    subscription: async (option = {}) => {
      const { params = {}} = option
      let targetParams = {
        ...params
      }
      targetParams.selectedFloat = !params.isSelectedFloat&&params.selectedFloat || ''
      let fullUrl = `/pages/home/<USER>/index?${qs.stringify(targetParams)}`
      return navigator(params.isSelectedFloat,fullUrl,params)
    }
  },
  [JUMP_TARGET.NET_WORTH_EXAMINE]: {
    subscription: async (option = {}) => {
      let param = {
        unionid:geStorageUnionId(),
        openid: getOpenId(),
        token: getToken(),
        wechatInfoId: getWechatInfoId(),
      }
      let url = `${wbs.gfH5}/channel_h5/netWorthExamine/#/list?${qs.stringify(param)}`
      let fullUrl = `/pages/common/webview/webPage?url=${encodeURIComponent(url)}`
      return navigator(false,fullUrl,params)
    }
  },
  [JUMP_TARGET.BROKER_RESEARCH_REPORT]: {
    subscription: async (option = {}) => {
      setWebPageShowWay(1)
      const { params = {}} = option
      let param = {
        unionid:geStorageUnionId(),
        openid: getOpenId(),
        token: getToken(),
        wechatInfoId: getWechatInfoId(),
      }
      let url = `${wbs.gfH5}/channel_h5/brokerslist/#/list?conf=${encodeURIComponent(params.conf)}&pageName=${encodeURIComponent(params?.pageName.replace(/%/g, '%25')) || ''}&${qs.stringify(param,{encode:false})}`
      let fullUrl = `/pages/common/webview/webPage?url=${encodeURIComponent(url)}&selectedFloat=${!params?.isSelectedFloat&&params?.selectedFloat || ''}&pageType=${actionType.brokerResearchReport}&conf=${params.selectedFloatConf}&cardId=${params.contentConfigId}&isFromChannel=true`
      return navigator(params.isSelectedFloat,fullUrl,params)
    }
  },
  [JUMP_TARGET.SELF_BUILT_LIST]: {
    subscription: async (option = {}) => {
      setWebPageShowWay(1)
      const { params = {}} = option
      let param = {
        ...params,
        unionid:geStorageUnionId(),
        openid: getOpenId(),
        token: getToken(),
        wechatInfoId: getWechatInfoId(),
      }
      let url = `${wbs.gfH5}/channel_h5/selfBuildList/#/list?${qs.stringify(param,{encode:false})}`
      let fullUrl = `/pages/common/webview/webPage?url=${encodeURIComponent(url)}&selectedFloat=${!params?.isSelectedFloat&&params?.selectedFloat || ''}&pageType=${actionType.selfBuiltList}`
      return navigator(params.isSelectedFloat,fullUrl,params)
    }
  },
  [JUMP_TARGET.TEMP_MSG]: {
    subscription: async (option = {}) => {
      const { params = {}} = option
      let pageType = params.pageType;
      if(pageType == PAGE_INFO_REALM.advSpecialTopic || pageType == PAGE_INFO_REALM.reportLoading || params.contentLink){
        return showReportDetail(params)
      }else{
        return showCommonDeatil(params)
      }
    }
  },
  [JUMP_TARGET.TEMP_OUTLINK]: {
    subscription: async (option = {}) => {
      const { params = {}} = option
      setWebPageShowWay(1)
      let fullUrl = `/pages/common/webview/webPage?url=${encodeURIComponent(params.path)}`
      return navigator(params.isSelectedFloat,fullUrl,params)
    }
  },
  [JUMP_TARGET.Open_Mini]: {
    subscription: async (option = {}) => {
      const { params = {}} = option
      return wx.navigateToMiniProgram({
        appId: params.appId,
        path: params.path,
        success(res) {
          // console.log('====== AppAdvOpenMini navigateToMiniProgram success >>>>', res)
        },
        fail(err) {
          // console.log('====== AppAdvOpenMini navigateToMiniProgram fail  >>>>', err)
        },
        complete() {
          storage.setStorage(global.STORAGE_GLOBAL_APP_HOLD, true);
        },
      })
    }
  },

}


//咨询，产品，经理详情
function showCommonDeatil(options){
    let pageType = options.pageType;
    let pageUrl = "";
    let lastPageUrl = "";
    let userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);
    let hasLogin = !LOGIN_VISITOR.includes(userRole * 1)
    let detailUrl = DETAIL_URL[pageType]
    pageUrl = `${wbs.gfH5 + detailUrl[0]}`
    lastPageUrl = `${wbs.gfH5 + detailUrl[1]}?faId=${options.faId}&parentId=${options.parentId}`
    if(pageType == PAGE_INFO_REALM.meet_essence){
      pageUrl += `?time=${options.timePublished || ''}`
      lastPageUrl += `&time=${options.timePublished || ''}`
    }else if(pageType == PAGE_INFO_REALM.depth_report_module){
      pageUrl += `?depthReportId=${options.value}`
      lastPageUrl += `&depthReportId=${options.value}`
    } else if(pageType == PAGE_INFO_REALM.pruduct && options?.fundCareId){
      pageUrl += `?value=${options.value}&fundCareId=${options.fundCareId}`
      lastPageUrl += `&value=${options.value}&fundCareId=${options.fundCareId}`
    }else{
      pageUrl += `?value=${options.value}`
      lastPageUrl += `&value=${options.value}`
    }
    let sharePageUrl = `${wbs.gfH5}/marketing-api/api/v1/h5/redirect/wechat?url=${encodeURIComponent(lastPageUrl)}`;
    let isEtfFloatView = options.isRedirect ? '&isRedirect=true':''
    let url = `${wbs.gfH5}/share/advHomePage?pageUrl=${encodeURIComponent(
      pageUrl
    )}&sharePageUrl=${encodeURIComponent(
      sharePageUrl
    )}&pushType=1`+isEtfFloatView;
    let params = {
      unionid:geStorageUnionId(),
      openid: getOpenId()
    }
    const allParams = {
      ...params,
      token: getToken(),
      wechatInfoId: getWechatInfoId(),
    }
    if (userRole !== LoginState.BEREJUST) {
      url += `&${qs.stringify(allParams,{encode:false})}` 
    }else{
      url += `&${qs.stringify(params,{encode:false})}` 
    }
    if(options.isSelectedFloat){
      url = hasLogin ? `${pageUrl}&${qs.stringify(allParams,{encode:false})}`:sharePageUrl
    }
    setWebPageShowWay(1)
    let fullUrl = `/pages/common/webview/webPage?url=${encodeURIComponent(url)}`
    return navigator(options.isSelectedFloat,fullUrl,options)

}

function navigator(isNavigateTo,url,params){
  // console.log("--打开小程序0000000---:",url,params)
  if (params && params.isRedirect) {
      if(!url.indexOf('isSelectedFloat=true')>-1) {
        url = url + '&isSelectedFloat=true'
      }
      return wx.redirectTo({
        url,
      })
  } else if(isNavigateTo){
    return wx.navigateTo({
      url,
    })
  }else{
    return wx.reLaunch({
      url,
    });
  }
}



//研报,专题，外链详情
async function showReportDetail(options){
    let _url = options.perfix;
    let pageType = options.pageType;
    let cardTitle = options.cardTitle;
    let userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);
    const hasLogin = !LOGIN_VISITOR.includes(userRole * 1);
    console.log('pagetype is',pageType);
    if(pageType == PAGE_INFO_REALM.reportLoading){
      if(!hasLogin){
        let fullUrl = `/pages/loginAndRegist/login/login?shareParams=${encodeURIComponent(qs.stringify(options,{encode:false}))}`
        return navigator(options.isSelectedFloat,fullUrl,options)
      }
    }
    if (pageType == PAGE_INFO_REALM.reportLoading) {
      getApp().initWInfo();
      const { code, success, data, msg } = await getPermission({
        unionid: geStorageUnionId(),
      });
      if (success) {
        if (data.QSSDBG != "Y") {
          return wx.navigateTo({
            url: `/package-activity/pages/loginAndRegist/uploadImg/uploadImg`,
          });
        }
      } else {
        return interaction.showToast(msg || "");
      }
      //研报触达授权
      if(!options.isSelectedFloat){
        options.isShareCome = true
      }
    }
    options["token"] = getToken() || "";
    options["openid"] = getOpenId() || "";
    options["wechatInfoId"] = getWechatInfoId() || "";
    options["unionid"] = geStorageUnionId() || "";

    let contentLink = options.contentLink;
    console.log("options====", options);
    let fullUrl = ''
    setWebPageShowWay(1);
    if (contentLink) {
      if (hasLogin) {
        getApp().initWInfo();
      }
      const ontLinkParams = {
        articleType: "OUT_LINK",
        articleId: options.value,
        realm: "ARTICLE",
        title: options.title || "",
      };
      // ontLinkParams.title = ontLinkParams.title.replace(/%/g, '%25')
      // ontLinkParams.title = encodeURIComponent(ontLinkParams.title)
      let params = {
        url: encodeURIComponent(contentLink),
        ontLinkParams: JSON.stringify(ontLinkParams),
        cardTitle: encodeURIComponent(cardTitle || "内部投资策略"),
        faId: options.faId || "",
        parentId: options.parentId || ""
      }

      //资讯外链触达授权
      if(contentLink.includes(wxTextPerfix)){
        let _url = `${wbs.gfH5}/marketing-api/openApi/redirectUser?url=${contentLink}`
        params.url =  encodeURIComponent(_url)
      }
      fullUrl = `/pages/common/webview/webPage?${qs.stringify(params, { encode: false })}`
      return navigator(options.isSelectedFloat,fullUrl,options)
    } else {
      fullUrl = `/pages/common/webview/webPage?url=${_url}&${qs.stringify(
        options
      )}&cardTitle=${cardTitle}`
      return navigator(options.isSelectedFloat,fullUrl,options)
    }
}
export default function jumpTarget(params = {}) {
  const {name = ""} = params;
  return jumpTargetPage[`${name}`].subscription(params);
}



