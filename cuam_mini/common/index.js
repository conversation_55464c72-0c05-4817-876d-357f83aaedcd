//common
import regeneratorRuntime from '../lib/regenerator-runtime/runtime.js'
import Toast from '../miniprogram_npm/vant-weapp/toast/toast.js';

//const
import config from './const/config.js'
import {
  openApiConfig,
  application,
  postV2Api,
  wbs,
} from './const/env.js'
import * as enums from './const/enum.js'
import * as global from './const/global.js'
import * as systemtInfo from './const/systeminfo.js'
import * as reviewConfig from "./const/review";

// network
import * as api from './network/api.js'
import * as requests from './network/requests.js'

// event
import Event from './event/index.js'
import * as eventName from './event/name.js'
import Emitter from './event/emitter'

import storage from './utils/storage.js'
import * as userStorage from './utils/userStorage.js'
import * as util from './utils/util.js'
import * as validator from './utils/validator.js'
import * as countDown from './utils/countDownUtil.js'
import verifyIDCard from './utils/verify-identity-card.js'
import * as clue from './utils/clue.js'
import * as action from './utils/action.js'
import * as qrCode from './utils/qrCode'

import * as nbHome from './nb/home.js'

// 页面交互
import interaction from './interaction/index.js'

// 产品业务逻辑
import * as callLogic from './business/call.js'
import * as link from './business/link.js'
import * as log from './business/log.js'

import parseUrl from './utils/parseUrl.js'

import xConfig from '../config/index.js'
import convert from './business/convert.js'
import jumpTarget from './business/jumpTarget.js'
import breakIn from "./business/breakin";

// store
const store = require('./store/index.js')

// utils
const base64 = require('./utils/base64.js')
const format = require('./utils/format.js')
const md5 = require('./utils/md5.js')
//获取应用实例
const qs  = require('qs')

export {
  regeneratorRuntime,
  Toast,
  config,
  enums,
  openApiConfig,
  application,
  postV2Api,
  wbs,
  systemtInfo,
  reviewConfig,
  global,
  api,
  requests,
  Event,
  Emitter,
  eventName,
  store,
  base64,
  format,
  md5,
  storage,
  userStorage,
  util,
  validator,
  countDown,
  verifyIDCard,
  interaction,
  callLogic,
  link,
  qs,
  parseUrl,
  xConfig,
  convert,
  clue,
  nbHome,
  action,
  qrCode,
  log,
  jumpTarget,
  breakIn
}
