const LOGIN_SUCCESS = 'login_success'

const SHARE_DATA_UPDATE = 'SHARE_DATA_UPDATE'
//请他联系我切换理财顾问
const UPDATE_ADVISOR = 'UPDATE_ADVISOR'
//tabbar更新
const UPDATE_TABBAR = 'UPDATE_TAB_BAR'

// 消除tabbar
const DISMISS_TABBAR = 'DISMISS_TAB_BAR'
const SHOW_TABBAR =  'SHOW_TABBAR'

// tabBar触发监听
const SET_LISTENER_TAB_BAR = 'SET_LISTENER_TAB_BAR'

const SEND_WEBVIEW_OPTIONS = 'SEND_WEBVIEW_OPTIONS'
const SEND_REGISTER_OPTIONS = 'SEND_REGISTER_OPTIONS'

const REFRESH_PAGE_DATA = 'REFRESH_PAGE_DATA'

const SEND_EVENT_TO_POLYMERS = 'SEND_EVENT_TO_POLYMERS'
const SEND_EVENT_TO_SHARE_LIST = 'SEND_EVENT_TO_SHARE_LIST'

const SEND_REFRESH_TO_POLYMERS = 'SEND_REFRESH_TO_POLYMERS'

const FETCH_NET_DATA_ERROR = 'FETCH_NET_DATA_ERROR'

const SET_SCROLL_TO_TARGET = 'SET_SCROLL_TO_TARGET'

const SET_PULL_BAR = 'SET_PULL_BAR'

const FULL_PAGE_SCROLL_LISTENER = 'FULL_PAGE_SCROLL_LISTENER'

const SET_REFRESH_PAGE = 'SET_REFRESH_PAGE'

const SET_PAGE_BACK = 'SET_PAGE_BACK'

const DO_READY_LINE_REFRESH = 'DO_READY_LINE_REFRESH'

const DO_OVER_WAIT_LINE = 'DO_OVER_WAIT_LINE'

const SET_FULL_OPACITY = 'SET_FULL_OPACITY'

const BREAK_IN_INIT_SUCCESS = 'BREAK_IN_INIT_SUCCESS'

const CHANGE_POCUDT_CARE = 'CHANGE_POCUDT_CARE'

const CHANGE_PRODUCT_START = "CHANGE_PRODUCT_START"

const CANCEL_ATTENTION_PRODUCT = 'CANCEL_ATTENTION_PRODUCT'


export {
  LOGIN_SUCCESS,
  SHARE_DATA_UPDATE,
  UPDATE_ADVISOR,
  UPDATE_TABBAR,
  DISMISS_TABBAR,
  SEND_WEBVIEW_OPTIONS,
  SEND_REGISTER_OPTIONS,
  REFRESH_PAGE_DATA,
  SEND_EVENT_TO_POLYMERS,
  FETCH_NET_DATA_ERROR,
  SEND_REFRESH_TO_POLYMERS,
  SEND_EVENT_TO_SHARE_LIST,
  SET_SCROLL_TO_TARGET,
  SET_REFRESH_PAGE,
  SET_PAGE_BACK,
  DO_READY_LINE_REFRESH,
  DO_OVER_WAIT_LINE,
  SET_FULL_OPACITY,
  SET_LISTENER_TAB_BAR,
  SET_PULL_BAR,
  FULL_PAGE_SCROLL_LISTENER,
  BREAK_IN_INIT_SUCCESS,
  CHANGE_POCUDT_CARE,
  CHANGE_PRODUCT_START,
  CANCEL_ATTENTION_PRODUCT
}
