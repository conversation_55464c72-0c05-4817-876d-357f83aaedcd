import * as enums from '../const/enum.js'
import {
  global,
  parseUrl,
  qs,
  requests
} from "../index.js"

const {
  Currency,
  Unit
} = enums

const {
  pixelRatio,
  windowWidth
} = wx.getSystemInfoSync()

function getDomain(url) {
  //https://service-wbs240.newtamp.cn/http/
  if (url){
    var arr1 = url.split('//')
    var domain = arr1[1] && arr1[1].split('/')[0]
    return domain
  }
  return ''
}

/**
 * 隐藏中间 str：字符串，frontLen：前面保留位数，endLen：后面保留位数。
 */
function plusXing(str, frontLen, endLen) {
  var len = str.length - frontLen - endLen;
  var xing = '';
  for (var i = 0; i < len; i++) {
    xing += '*';
  }
  return str.substring(0, frontLen) + xing + str.substring(str.length - endLen);
}

const toPromise = (wx) => {
  return (method) => {
    return (option) => {
      return new Promise((resolve, reject) => {
        wx[method]({
          ...option,
          success: (res) => {
            resolve(res)
          },
          fail: (err) => {
            reject(err)
          }
        })
      })
    }
  }
}

// 将rpx转为px
const rpx2px = params => {
  return windowWidth / 750 * params
}

const px2rpx = params => {
  return 750 / windowWidth * params
}

const fileNameSuffix = fileName => {
  var index1 = fileName.lastIndexOf(".");
  var index2 = fileName.length;
  var suffix = fileName.substring(index1, index2);//后缀名
  return suffix.toLowerCase()
}

// 根据unit和currency拼接单位
const getCurrency = (unit, currency) => {
  let currencyStr = '元'
  switch (unit) {
    case Unit.YUAN:
      currencyStr = '元'
      break;
    case Unit.WAN_YUAN:
      currencyStr = '万元'
      break;
    case Unit.WAN_CURRENCY:
      currencyStr = `万${Currency.currency}`
      break;
    default:
      currencyStr = '元'
      break;
  }
  return currencyStr
}

// 解析url query
const parseQuery = function(query) {
  var reg = /([^=&\s]+)[=\s]*([^&\s]*)/g;
  var obj = {};
  while (reg.exec(query)) {
    obj[RegExp.$1] = RegExp.$2;
  }
  return obj;
}

const openFile = (url) => {
  wx.showLoading({
    title: '正在加载...',
  })
  requests.download({url})
    .then(filePath => {
      wx.hideLoading();
      wx.openDocument({
        filePath: filePath,
        success: function(res) {
          console.log('打开文档成功')
        },
        fail: function(res) {
          wx.showToast({
            title: '打开文件失败',
          })
        }
      })
    }).catch(res => {
    wx.hideLoading();
    wx.showToast({
      title: '文件下载失败',
      icon: 'none',
    })
  })
}

/**
 * 小程序图片下载并预览
 * @param url 图片地址
 * @returns
 */
const conmmonPreviewIOS = async (url = '') => {
  if (!url){
    wx.showToast({
      title: '文件地址错误',
      icon: 'none',
      duration: 1000
    })
  }

  wx.showLoading({
    title: '正在加载...',
  })

  const FileSystemManager = wx.getFileSystemManager()
  return (
    await wx.downloadFile({   // 先下载
      url: url,
      header: {
        'content-type': 'application/json',
      },
      success: (res) => {
        const Path = res.tempFilePath  // 拿到临时文件路径
        let suffixIndex = Path.lastIndexOf('.');
        let suffix = Path.slice(suffixIndex,);
        FileSystemManager.saveFile({ // 用系统方法保存临时文件
          tempFilePath: Path,
          filePath: `${wx.env.USER_DATA_PATH}/${new Date().getTime()}${suffix}`,  // 文件名用时间戳
          success: (res) => {
            let savedFilePath = res.savedFilePath;  // 拿到保存成功后的地址
            wx.openDocument({   // 打开文档 * （再分享的时候就会把保存在本地的文件 分享出去 即上一步操作的 文件 文件名以时间戳的形式展示）
              filePath: savedFilePath,
              showMenu: true,
              fileType: 'pdf',
              success: () => {
                console.log('打开文档成功')
              }
            })
          }
        })
      },
      fail: (res) => {
        // console.log(res, wx.env.USER_DATA_PATH, '0')
        // return wx.showModal({
        //   title: '失败',
        //   content: JSON.stringify(res)
        // })
        wx.showToast({
          title: '文件下载失败',
          icon: 'none',
          duration: 1000
        })
      }
    })
  )
}

function doUrlDeCode(str = '') {
  let _str = decodeURIComponent(str)
  if (`${_str}`.startsWith('%')){
    return doUrlDeCode(_str)
  }
  return _str
}

function convertMobile(mobile) {
  return mobile ? mobile.replace(/^(.{3})(.*)(.{4})$/, '$1-$2-$3') : '-'
}

function deParamsUrl(str = '') {
  let _str = decodeURIComponent(str)
  if (`${_str}`.startsWith('%') || `${_str}`.startsWith('params%')){
    return doUrlDeCode(_str)
  }
  return _str
}

function getQueryParams(url = '') {
  const paramsObj = {};
  const reg = /[?&][^?&]+=[^&]+/g;
  const found = url.match(reg)

  found.forEach(item => {
    let temp = item.substring(1).split('=')
    let [key, value = ''] = temp
    paramsObj[key] = value
  })

  return paramsObj
}

function throttle(fn, gapTime) {
  if (gapTime == null || gapTime == undefined){
    gapTime = 1500
  }

  let _lastTime = null

  // 返回新的函数
  return function() {
    let _nowTime = +new Date()
    if (_nowTime - _lastTime > gapTime || !_lastTime){
      fn.apply(this, arguments)   //将this和参数传给原函数
      _lastTime = _nowTime
    }
  }
}

// 将一个大数组分割成几个小数组
const sliceArray = (array, size) => {
  if (!array || !array.length) return []
  let result = []
  for (let i = 0; i < Math.ceil(array.length / size); i++) {
    let start = i * size
    let end = start + size
    result.push(array.slice(start, end))
  }
  return result
}

/**
 * 货币金额加逗号处理
 */
const dotCurrency = (num, digit = 2) => {
  if (num){
    num = num.toString().replace(/\$|\,/g, '')
    if ('' == num || isNaN(num)){
      return 'ERROR'
    }
    let sign = num.indexOf('-') > 0 ? '-' : ''
    let cents = num.indexOf('.') > 0 ? num.substr(num.indexOf('.')) : ''
    cents = cents.length > 1 ? cents : ''
    num = num.indexOf('.') > 0 ? num.substring(0, (num.indexOf('.'))) : num
    if (num.length > 1 && '0' == num.substr(0, 1)) {
      return 'ERROR'
    }
    for (let i = 0; i < Math.floor((num.length - (1 + i)) / 3); i++) {
      num = num.substring(0, num.length - (4 * i + 3)) + ',' + num.substring(num.length - (4 * i + 3))
    }
    return (sign + num + cents)
  }
  return 0
}

/**
 * 四舍五入
 */
var round = function(val, digit = 1) {
  let offset = Math.pow(10, digit)
  return Math.round(val * offset) / offset
}

/**
 * 元、万元格式化处理
 * 超万显示万元，保留4位小数
 * 不过万显示元，保留2位小数
 * 金额加逗号
 */
var currency = function(val) {
  return val >= 10000
    ? dotCurrency(round(val / 10000, 4)) + '万元'
    : dotCurrency(round(val, 2)) + '元'
}

// 需要前端手动拼上单位的集合
const needAddUnitKey = (key) => {
  return {
    // minBuyMoney: '元',
    establish: '元',
    publishScale: '元'
  }[key]
}

const formatAmount = (amount, unit = '元') => {
  if (!amount) return ''
  if (amount <= 9999) return `${amount}${unit}`
  if (amount > 9999) return `${parseFloat((amount / 10000).toFixed(4) + '')}万${unit}`
}

const tel400 = function(tel) {
  tel = tel.toString();
  if (tel.substr(0, 3) === '400'){
    if (tel.indexOf('-') === -1){
      return tel.substr(0, 4) + '-' + tel.substr(4, 3) + '-' + tel.substr(7, 3);
    }
  }
  return tel;
}

const getUrlQuery = (url = '') => {
  // 用JS拿到URL，如果函数接收了URL，那就用函数的参数。如果没传参，就使用当前页面的URL
  let queryString = url ? url.split('?')[1] : window.location.search.slice(1);
  // 用来存储我们所有的参数
  let obj = {};
  // 如果没有传参，返回一个空对象
  if (!queryString){
    return obj;
  }
  // stuff after # is not part of query string, so get rid of it
  queryString = queryString.split('#')[0];
  // 将参数分成数组
  let arr = queryString.split('&');
  for (let i = 0; i < arr.length; i++) {
    // 分离成key:value的形式
    let a = arr[i].split('=');
    // 将undefined标记为true
    let paramName = a[0];
    let paramValue = typeof (a[1]) === 'undefined' ? true : a[1];
    // 如果调用对象时要求大小写区分，可删除这两行代码
    paramName = paramName.toLowerCase();
    if (typeof paramValue === 'string') paramValue = paramValue.toLowerCase();
    // 如果paramName以方括号结束, e.g. colors[] or colors[2]
    if (paramName.match(/\[(\d+)?\]$/)){
      // 如果paramName不存在，则创建key
      let key = paramName.replace(/\[(\d+)?\]/, '');
      if (!obj[key]) obj[key] = [];
      // 如果是索引数组 e.g. colors[2]
      if (paramName.match(/\[\d+\]$/)){
        // 获取索引值并在对应的位置添加值
        let index = /\[(\d+)\]/.exec(paramName)[1];
        obj[key][index] = paramValue;
      } else {
        // 如果是其它的类型，也放到数组中
        obj[key].push(paramValue);
      }
    } else {
      // 处理字符串类型
      if (!obj[paramName]){
        // 如果如果paramName不存在，则创建对象的属性
        obj[paramName] = paramValue;
      } else if (obj[paramName] && typeof obj[paramName] === 'string'){
        // 如果属性存在，并且是个字符串，那么就转换为数组
        obj[paramName] = [obj[paramName]];
        obj[paramName].push(paramValue);
      } else {
        // 如果是其它的类型，还是往数组里丢
        obj[paramName].push(paramValue);
      }
    }
  }
  return obj;
};

const isEmptyObject = (obj = {}) => {
  return Object.keys(obj).length === 0;
}

const analyzeAction = (act = '') => {
  let res = {}
  if (!act){
    return res
  }

  let [temp, ..._res] = act.split('action://share/')
  if (temp){
    return res
  }

  // console.log('======= analyzeAction res >>>>', res)
  const [type, ...value] = `${_res.join('')}`.split('?')
  let _value = value.join('')
  // console.log('===== analyzeAction destUrl >>>> ', qs.parse(_value))
  res = {
    type,
    analyzeRes: qs.parse(_value)
  }
  return res
}

const timeFormat = (value, linker) => {
  if (!value) return ''
  var time = parseInt(value)
  time = time + ''
  if (time.length > 11){
    time = parseInt(value)
  } else {
    time = parseInt(value) * 1000
  }
  var date = new Date(time)
  var month = date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1
  var currentDate = date.getDate() < 10 ? '0' + date.getDate() : date.getDate()
  var hh = date.getHours() < 10 ? '0' + date.getHours() : date.getHours()
  var mm = date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()
  var ss = date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds()
  return date.getFullYear() + '-' + month + '-' + currentDate + (linker || " ") + hh + ":" + mm + ":" + ss
}

const isJSON = (jsonContent) => {
  if (typeof jsonContent == 'string'){
    try {
      let obj = JSON.parse(jsonContent);
      if (jsonContent.indexOf('{') > -1){
        return {
          "msg": "",
          "data": jsonContent,
          "success": true
        };
      } else {
        return {
          "msg": "数据配置错误",
          "data": null,
          "success": false,
        };
      }
    } catch (e) {
      console.log('=== isJSON >>>', e);
      return {
        "msg": "数据配置错误",
        "data": null,
        "success": false,
      };
    }
  }
  return {
    "msg": "数据配置错误",
    "data": null,
    "success": false,
  };
};

function getTextLength(str, full) {
  let len = 0;
  for (let i = 0; i < str.length; i++) {
    let c = str.charCodeAt(i);
    //单字节加1
    if ((c >= 0x0001 && c <= 0x007e) || (0xff60 <= c && c <= 0xff9f)){
      len++;
    } else {
      len += (full ? 2 : 1);
    }
  }
  return len;
}

function getSelectList(data) {
  if (data) {
    return [{ ...data, name: '近六月', number: data.last6MRange }, { ...data, name: '近一年', number: data.last1YRange }, { ...data, name: '近两年', number: data.last2YRange }, { ...data, name: '近三年', number: data.last3YRange }, { ...data, name: '近五年', number: data.last5YRange }, { ...data, name: '成立以来', number: data.sinceRange }, { ...data, name: '今年以来', number: data.yearRange }];
  }
  return []
}

export function percent2percent25 (url) {
  if(url.indexOf('%') > -1) {
    return url.replace(/%/g,'%25')
  } else {
    return url;
  }
}

//正则切割
export function GetQueryString (name, url) {
  var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)")
  var r = ''
  if (url) {
    let index = url.indexOf('?')
    if (index !== -1) {
      r = url.substr(index + 1).match(reg)
    } else {
      r = url.match(reg)
    }
  } else {
    r = window.location.search.substr(1).match(reg)
  }
  if (r != null) return decodeURI(percent2percent25(r[2]))
  return null
}

function parseBool(value) {
  let valueBoolean  = false
  if(typeof value === "boolean"){
    valueBoolean = value
  } else if(typeof value === "string"){
    valueBoolean = value.toLowerCase() === 'true' || value === "1"
  } else if( typeof value === "number") {
    valueBoolean = value === 1
  }

  return valueBoolean
}

function GetQueryType(url){
  let queryString =  url.split('?')[0]  
  let lastIndex = queryString.lastIndexOf('/')
  let type = queryString.slice(lastIndex+1)
  return type;
}

module.exports = {
  plusXing,
  getDomain,
  toPromise,
  rpx2px,
  px2rpx,
  isJSON,
  fileNameSuffix,
  getCurrency,
  parseQuery,
  openFile,
  convertMobile,
  throttle,
  sliceArray,
  dotCurrency,
  round,
  currency,
  needAddUnitKey,
  formatAmount,
  tel400,
  conmmonPreviewIOS,
  getUrlQuery,
  analyzeAction,
  isEmptyObject,
  timeFormat,
  getTextLength,
  deParamsUrl,
  getQueryParams,
  doUrlDeCode,
  getSelectList,
  GetQueryString,
  parseBool,
  GetQueryType,
}
