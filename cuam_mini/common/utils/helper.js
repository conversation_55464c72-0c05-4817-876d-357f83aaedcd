import Toast from '../../miniprogram_npm/vant-weapp/toast/toast'
import { getToken, getNbToken, getCompanyInfo, getUser, saveCompanyInfo, getOpenId } from './userStorage'
import { getCompany } from '../nb/article'
import { wbs, postV2Api, enums, store, storage, interaction, eventName, global, log } from '../index'

const {
  FETCH_NET_DATA_ERROR
} = eventName

const gfPerFix = wbs.gfApi
const gfMimiFix = wbs.gfMini
//  'https://gf-admin.newtamp.cn',

const nbDecludeUrl = ['/marketing-api/api/v1/mini/token/local/replace']

const contentType = {
  json: 'application/json;charset=utf-8',
  form: 'application/x-www-form-urlencoded;charset=utf-8'
}

const ERROR_CODE = [400, 404]

export function doFetch(
  url,
  data,
  method = "POST",
  header,
  dataType = 'json',
  domain = postV2Api.mns,
  isMarketing = false,
  isStaff = false,
  isGfMini = false,
  isEmployees = false,
  hideToken = false
  ) {

  let _url = `${gfPerFix}${domain}${url}`
  if (isGfMini){
    _url = `${gfMimiFix}${domain}${url}`
  }

  let headers = header || {
    'Accept': 'application/json;charset=UTF-8',
    'Content-Type': dataType === 'json' ? contentType.json : contentType.form,
    'X-Requested-With': 'XMLHttpRequest',
    "token": '',
  }

  if (getToken() && !hideToken){
    headers.token = getToken()
  }

  if(isEmployees){
    let _wInfo = storage.getStorage(global.STORAGE_GLOBAL_EMPLOYEE_INFO) || {}
    headers.token = _wInfo.token
  }

  if (isStaff && getOpenId()){
    headers.cookie = `openid=${getOpenId()}`
  }

  if (isMarketing){
    headers = {
      ...headers,
      'serviceChannel': 'PRI'
    }
  }


  console.log('@@ _url >>>', _url)
  console.log('@@ _data >>>', data)
  // console.log('@@ headers >>>', headers)
  // log.info(`@@http-${url} _url >>>`, _url)
  // log.info(`@@http-${url} _data >>>`, data)
  return new Promise((resolve, reject) => {
    wx.request({
      url: _url,
      method,
      header: headers,
      data: {...data},
      success(response) {
        console.log('@@ response >>> ', response)
        // log.info(`@@http-${url} response >>>`, response)
        const {data, statusCode} = response || {}
        if (statusCode === 200){
          resolve(data)
          if (data.errorCode === '40002'){
            let page = getCurrentPages().pop();
            console.log('>>> doFetch page >>>', page)
          }
        } else if (statusCode === 401){
          let page = getCurrentPages().pop();

          if (page.route == 'pages/loginAndRegist/login/login'){
            return interaction.showToast(data.msg || data.error || '')
          }
          const {code, path} = data || {}
          if (code === 401 && path === '/uc-system/mobile/api/v1/staff/card/get'){
            resolve(data)
          } else {
            reject(response)
          }
        } else {
          if (ERROR_CODE.includes(statusCode * 1)){
            getApp().event.emit(FETCH_NET_DATA_ERROR)
          }
          log.warn(`@@http-${url} response >>>`, response)
          reject(response)
        }
      },
      fail(err) {
        log.error(`@@http-${url} error >>>`, err.errMsg)
        console.log('>> fail err >>>', err)
      },
      complete(response) {
        wx.hideLoading()
        log.info({
          url: _url,
          statusCode: response.statusCode,
          msg: response?.data?.message || response?.data?.msg || response?.data?.error || '',
        })
      }
    })
  })
}

// HTTP
export function http(url, data, method = "post", header, isGfMini = false) {
  // wx.showLoading({
  //   title: '数据加载中..',
  //   mask: true
  // })

  let _url = gfPerFix + url
  if (isGfMini){
    _url = gfMimiFix + url
  }
  let headers = header || {}

  // 区分nb、鸿坤接口
  if (url.startsWith('/nb')){
    headers.token = getToken()
    // 移除前缀 '/nb'
    _url = gfPerFix + url.slice(3)
    // 移除不需要加载token接口的token
    for (let i = 0; i < nbDecludeUrl.length; i++) {
      if (_url.includes(nbDecludeUrl[i])){
        delete headers.token
      }
    }
  } else {
    if (getToken()){
      headers.token = getToken()
    }
  }

  console.log('@@=== http _url >>>', _url)
  console.log('@@=== http _data >>>', data)
  // log.info(`@@http-${url} _url >>>`, _url)
  // log.info(`@@http-${url} _data >>>`, data)
  return new Promise((res, rej) => {
    wx.request({
      url: _url,
      method,
      header: headers,
      data: {
        ...data
      },
      success(response) {
        console.log("response数据", response);
        // log.info(`@@http-${url} response >>>`, response)
        if (response.statusCode === 200){
          res(response.data)
        } else {
          log.warn(`@@http-${url} response >>>`, response)
          rej(response)
        }

      },
      fail(err) {
        log.error(`@@http-${url} error >>>`, err.errMsg)
        console.log('>> fail err >>>', err)
      },
      complete(response) {
        wx.hideLoading()
        log.info({
          url: _url,
          statusCode: response.statusCode,
          msg: response?.data?.message || response?.data?.msg || response?.data?.error || '',
        })
      }
    })
  })
}

// 通过日期获取当前周几
export const getDay = () => {
  const weekObj = {
    0: '周日',
    1: '周一',
    2: '周二',
    3: '周三',
    4: '周四',
    5: '周五',
    6: '周六',
  }
  return date => {
    try {
      return weekObj[new Date(date).getUTCDay()]
    } catch (err) {
      return '-'
    }
  }
}

export const getData = (date, format = 'yyyy-mm-dd hh:mm:ss') => {
  const _date = new Date(date)
  const y = _date.getFullYear()
  const m = _date.getMonth() + 1
  const d = _date.getDate()
  const h = _date.getHours()
  const mm = _date.getMinutes()
  const s = _date.getSeconds()
  if (format === 'yyyy-mm-dd hh:mm:ss'){
    return `${y}-${m}-${d} ${h}:${mm}:${s}`
  }
}

// 图片宽度自适应
export const imgWidthAuto = (imgStr) => imgStr.replace(/\<img/gi, '<img style="max-width:100%;height:auto"')

/**
 *
 * @param {原始字符串}} str
 * @param {开始几位敏感数据} pre
 * @param {最后显示几位敏感数据} next
 * @returns 脱敏数据
 */
export const notSensitive = (str, pre, next) => {
  const preStr = str.substring(0, pre)
  const nextStr = str.substring(str.length - next)
  const countLth = str.length - (pre + next)
  let sensitiveStr = ''
  if (countLth > 0){
    for (let i = 0; i < countLth; i++) {
      sensitiveStr += '*'
    }
  }
  return preStr + sensitiveStr + nextStr
}

// 电话沟通
export const callFn = async () => {
  let phoneNumber = ''
  if (getUser()?.faMobile){
    phoneNumber = getUser()?.faMobile
  } else if (getCompanyInfo()){
    phoneNumber = getCompanyInfo()?.telephone
  } else {
    const {success, param} = await getCompany()
    if (success){
      saveCompanyInfo(param.config.entity)
      phoneNumber = getCompanyInfo()?.telephone
    }
  }

  if (phoneNumber){
    wx.makePhoneCall({
      phoneNumber
    })
  } else {
    Toast("暂无手机号")
  }

}
