import {global} from '../../common/index.js'

const filterKeys = [
  // global.STORAGE_GLOBAL_APP_HOLD,
  global.STORAGE_GLOBAL_START_UP_STATUS,
  global.STORAGE_GLOBAL_CURR_CHANNEL_IMG_URL,
  // global.STORAGE_GLOBAL_SCREEN_QUERY,
  global.STORAGE_GLOBAL_EMPLOYEE_INFO,
  global.STORAGE_GLOBAL_USER_ROLE,
  global.STORAGE_GLOBAL_RESET_SHARE_PATH_DATA,
  global.STORAGE_GLOBAL_CURR_TAB_PATH,
  global.STORAGE_GLOBAL_SYSTEM_INFO,
  // global.STORAGE_GLOBAL_WECHAT_INFO,
  global.STORAGE_GLOBAL_START_UP_IMG_URl,
  global.STORAGE_GLOBAL_SYSTEM_INFO_CACHE,
  global.STORAGE_GLOBAL_INVITE_BY_CODE_WAY,
  global.STORAGE_GLOBAL_REROUTE_BY_CODE,
]

export default class storage {
  static setStorage(key, value) {
    if (filterKeys.indexOf(key) !== -1 && getApp().globalStatus) {
      return getApp().globalStatus[key] = value
    }

    try {
      wx.setStorageSync(key, value)
    } catch (e) {}
  }

  static getStorage(key) {
    if (filterKeys.indexOf(key) !== -1 && getApp().globalStatus) {
      return getApp().globalStatus[key];
    }

    try {
      const value = wx.getStorageSync(key)
      return value
    } catch (e) {
      return null
    }
  }

  static removeStorage(key) {
    try {
      wx.removeStorageSync(key)
    } catch (e) {}
  }
  /*
    检查是否过期
  */

  static aging() {
    // const arrStorageName = ['storage_data_expiration','setStorage_advisorPage', 'setStorage_servicePage', 'setStorage_websitePage', 'setStorage_learnPage']
    this.expiration=wx.getStorageSync('storage_data_expiration'); //缓存时间
    this.timestamp=Date.parse(new Date()) / 1000; //当前时间
    // console.log(this.expiration, this.timestamp,'--url--')

    if(this.expiration) {
      if(this.expiration <= this.timestamp) {
        // console.log('开始更新缓存')
        //缓存已过期，重新添加数据
        return true
      }else {
        //未过期，保留缓存数据
        return false   
      }
    }else {
      //过期时间为空，重新缓存数据
      return true
    }
  }
}