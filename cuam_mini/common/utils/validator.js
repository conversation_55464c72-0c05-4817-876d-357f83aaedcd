// 是否为手机号
export const isMobile = mobileNumber => /^0?(10|11|12|13|14|15|16|17|18|19)[0-9]{9}$/.test(mobileNumber)

// 是否为邮箱
export const isEmail = emailString => /^\w[-\w.+]*@([A-Za-z0-9][-A-Za-z0-9]+\.)+[A-Za-z]{2,14}$/.test(emailString)

// 是否为整数
export const isInteger = number => /^[1-9]\d*$/.test(number)

// 是否为浮点数
export const isFloat = number => /^\-?\d+\.\d+$/.test(number)

export const isUrl = uri => /^(https|http|ftp|rtsp|mms)?:\/\//.test(uri)

export function isEmpty(obj) {
  // null and undefined are "empty"
  if (obj === null || obj === undefined || obj === 'null' || obj ==='undefined') return true
  // Assume if it has a length property with a non-zero value
  // that that property is correct.
  if (typeof obj === 'number' && isNaN(obj)) return true

  if (typeof obj === 'string' && obj.length <= 0) return true

  if (typeof obj === 'object') return false

  if (typeof obj === 'boolean') return obj

  return false
}

/**
 * 判断密码是否规范 6-18位字母数字组合
 */
export function checkPassword(password) {
  var reg = /^(?=.*[A-Za-z])(?=.*\d)[A-Za-z\d]{6,18}$/;
  return reg.test(password);
}
