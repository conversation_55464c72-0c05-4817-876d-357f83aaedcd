import {
  global,
  interaction,
  qs,
  storage,
  wbs,
  enums,
  eventName,
  util,
  api,
  userStorage,
  breakIn
} from "../../common/index";
import { getMarketingPlanInfo, employeeCheck, getPermission } from "../nb/home";
const {
  geStorageUnionId,
  getOpenId,
  getToken,
  getWechatInfoId,
  setWebPageShowWay,
  getUserLoginStatus,
} = userStorage ;
const {
  RealmType,
  TabBarType,
  TabBarType: { properties },
  PAGE_LINK_TYPE,
  SEARCH_CHANNEL_EDF,
  MARKET_CLUE_DEF,
  LOGIN_VISITOR,
  CHANNEL_REALM,
  EMPLOYEE_LOGIN_STATUS,
  actionType,
  DETAIL_URL,
  PAGE_INFO_REALM,
  BREAK_FUNC_NAME
} = enums;
const { isHtfPerson } = api
const { analyzeAction, isEmptyObject } = util;
const { SEND_WEBVIEW_OPTIONS } = eventName;
const PRODUCT_PAGE_TYPE = {
  SCROLL_TAB_VIEW: "advProductDataDetail", // 分页切换
  SCROLL_ANCHOR_VIEW: "advProductDataNewDetail", // 瀑布流
};

let actionToken = getToken();

export function toBussinessCard(phone){
  let url = `${wbs.gfH5}/channel_h5/businesscard/?personal_phone=${phone}`
  setWebPageShowWay(1);
  return wx.navigateTo({
    url: `/pages/common/webview/webPage?url=${encodeURIComponent(url)}`,
  });
}

export async function checkIsHtfPerson(phone){
  const res = await isHtfPerson(phone)
  return res;
}

export async function onHandlePolymersAction(e) {
  console.log("************** 公共跳转polymers  >>>", e);

  const { formTab = "" } = this.properties || {};

  const {
    detail: {
      cardName = "",
      cardType = "",
      title = "",
      id = "",
      action = "",
      actionObject = {},
      actionValue = "",
      relation = {},
      courseId = "",
      categoryId: _categoryId = "",
      categoryIds: _categoryIds = "",
      uri = "",
      style: pageStyle = "SCROLL_ANCHOR_VIEW",
      pageType = "",
      pic = "",
      name = "",
      cId = "",
      cName = "",
      mName = "",
      fundname = "",
      fundcode = "",
      dateValue = "",
      fundCode = "",
      articleType = "",
      contentLink = "",
      contentUrl = "",
      linkType = "",
      type: newsType = "",
      realm = "",
      content = "",
      cardTitle = "",
      sensorsdata = {},
      shareToken = "", // 分享的token
      isShareCome = false, // 默认未分享
      shareUserId = "", // 分享userId
      showTime = '',
      timeCreated = '',
      kIndex = ''
    }
  } = e || {};

  const hasLogin = getUserLoginStatus();
  if(!hasLogin && !isShareCome){
    return breakIn({name:BREAK_FUNC_NAME.goToLoginPage})
  }

  actionToken = getToken() || shareToken; // 分享token

  let _categoryType = "";
  let _planId = "";
  let _pageStyle = pageStyle;

  // 跳转外部链接
  if (!isEmptyObject(actionObject)) {
    const {
      url = "",
      linkType = 0,
      categoryType = "",
      channel = [],
      pageName = "",
      gatherId = "",
      style: aPageStyle = "",
      contentName: planId = "",
    } = actionObject || {};

    _categoryType = categoryType;
    if (aPageStyle) {
      _pageStyle = aPageStyle;
    }
    if (planId) {
      if (planId.includes("ANCHOR")) {
        _planId = planId.split("_")[0];
      } else {
        _planId = planId;
      }
    }
    // DONE ✅
    // 普通外链
    if (linkType === PAGE_LINK_TYPE.WEB_PAGE) {
      if (url.startsWith("http") || url.startsWith("https")) {
        return wx.navigateTo({
          url: "/pages/common/webview/webPage?url=" + encodeURIComponent(url),
        });
      }
    } else if (linkType === PAGE_LINK_TYPE.POLYMERS_LIST) {
      // DONE ✅
      // 聚合列表
    }
  }

  const { type, analyzeRes } = analyzeAction(action);
  console.log("type: ", type, "analyzeRes: ", analyzeRes);
  const wInfo = storage.getStorage(global.STORAGE_GLOBAL_WECHAT_INFO) || {};

  let params = {
    token: getToken() || shareToken,
    openid: getOpenId(),
    unionid: geStorageUnionId(),
    wechatInfoId: getWechatInfoId(),
    pageType: type,
  };

  switch (type) {
    //基金经理列表
    case "advFundManagerList":
    //基金产品列表
    case "advFundProductList":
    // 海报列表
    case "AppAdvPosterList":
    // 直播列表
    case "AppAdvLiveList":
    // 媒体列表
    case "AppAdvTrainListCom":
    //营销列表
    case "AppAdvProductData":
    // 资讯列表
    case "AppAdvNewsList": {
      let _params = {
        type:  pageType && RealmType[pageType] || _categoryType && CHANNEL_REALM[_categoryType] || CHANNEL_REALM[analyzeRes.categoryType],
        categoryIds: analyzeRes.categoryIds || analyzeRes.categoryId || "",
        name: actionObject.pageName || title || actionValue,
        formTab,
        cardType,
        cardName:cName,
        isShareCome,
        parentId:analyzeRes&&analyzeRes.parentId || "",
        faId:analyzeRes&&analyzeRes.faId || '',
        selectedFloat:actionObject?.selectedFloat?.id,
        cardId:cId || sensorsdata.id || e?.target?.dataset?.message?.id,
        kIndex,
      }
      wx.navigateTo({
        url: `/pages/home/<USER>/list?${qs.stringify(_params,{encode:false})}` ,
      });
      break;
    }
    //跳转首页
    case "tabBarType": {
      let _path = properties[Number(analyzeRes.tabbarType) * 1].path;
      wx.switchTab({
        url: `${_path}`,
      });
      break;
    }

    //基金产品详情
    case "advFundProduct": {
      params.perfix = `${wbs.gfH5}/share/advFundProduct`;
      params.value = fundCode || analyzeRes.value;

      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    //全部基金产品列表
    case "allFundProductList": {
      wx.navigateTo({
        url: "/package-activity/pages/allProduct/productList/index",
      });
      break;
    }
    //我的关注产品列表
    case "myCareList": {
      wx.navigateTo({
        url: "/package-activity/pages/allProduct/productList/index?active=1",
      });
      break;
    }
    //专题详情
    case "specialDetail": {
      params.perfix = `${wbs.gfH5}/share/advSpecialTopic`;
      params.value = analyzeRes.value || id;

      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    //快讯分享
    case "flashShare": {
      params.perfix = `${wbs.gfH5}/share/advNewsShare`;
      params.value = analyzeRes.value || id;
      params.title = actionValue;

      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    //早报
    case "AppAdvMorningPaper": {
      params.perfix = `${wbs.gfH5}/share/advMorningPaper`;
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    //市场机会列表
    case "MraketNewsList": {
      wx.navigateTo({
        url: `/package-activity/pages/marketnewsList/index?id=${sensorsdata.id}&name=${cName}`,
      });
      break;
    }
    //基金经理详情
    case "advFundManager": {
      params.perfix = `${wbs.gfH5}/share/advFundManager`;
      params.value = id || analyzeRes.value;

      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    // 资讯详情
    case "AppAdvNewsDetail": {
      params.cardName = cName;
      //资讯外链
      if (articleType == "OUT_LINK" || analyzeRes.contentLink) {
        //研报
        if (linkType == "DEPTH_REPORT") {
          let isHasPermission = await checkHTFPermission("QSSDBG");
          if (!isHasPermission) {
            return goHTFPermission();
          }
        }
        if (
          contentLink.startsWith("http") ||
          contentLink.startsWith("https") ||
          contentUrl.startsWith("http") ||
          contentUrl.startsWith("https") ||
          analyzeRes.contentLink.startsWith("https")
        ) {
          if (linkType == "DEPTH_REPORT") {
            params.perfix = `${wbs.gfH5}/share/advReportLoading`;
            params.value = encodeURIComponent(contentLink || contentUrl);
            params.id = id;
            params.title = encodeURIComponent(title);
            params.pageType = "advReportLoading";
            params.cardTitle = encodeURIComponent(
              cardTitle || cName || "内部投资策略"
            );
            return wx.navigateTo({
              url: "/pages/common/webview/webPage",
              success(res) {
                res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
                setWebPageShowWay(1);
              },
            });
          }

          const ontLinkParams = {
            articleType: "OUT_LINK",
            articleId: id || analyzeRes.value,
            realm: "ARTICLE",
            title: actionValue ? actionValue : title ? title : content,
          };
          ontLinkParams.title = encodeURIComponent(ontLinkParams.title);
          return wx.navigateTo({
            url:
              "/pages/common/webview/webPage?url=" +
              encodeURIComponent(
                contentLink || contentUrl || analyzeRes.contentLink
              ) +
              `&ontLinkParams=${JSON.stringify(
                ontLinkParams
              )}&cardTitle=${
                cardTitle || analyzeRes.cardTitle || "内部投资策略"
              }&pageType=OUT_LINK`,
            success(res) {
              setWebPageShowWay(1);
            },
          });
        }
      } else {
        //早报
        if (realm == "broker_week_finesse") {
          params.perfix = `${wbs.gfH5}/share/advWeeklyStrategy`;
          params.pageType = "advWeeklyStrategy";
        } else if (newsType == 2) {
          params.perfix = `${wbs.gfH5}/share/advMorningPaper`;
        } else {
          params.perfix = `${wbs.gfH5}/share/advNewsDetail`;
        }
        if (id) {
          params.value = id;
        }
        const { value = "" } = analyzeRes || {};
        if (value) {
          params.value = value;
        }
        return wx.navigateTo({
          url: "/pages/common/webview/webPage",
          success(res) {
            res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
            setWebPageShowWay(1);
          },
        });
      }
    }
     //券商周策略
     case "advWeeklyStrategy":{
      params.perfix = `${wbs.gfH5}/share/advWeeklyStrategy`;
      if (id || analyzeRes.id) {
        params.value = id || analyzeRes.id;
      }
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }

    //精选研报/通联研报
    case PAGE_INFO_REALM.depth_report:{
      let isHasPermission = await checkHTFPermission("QSSDBG");
      if (!isHasPermission) {
        return goHTFPermission();
      }
      params.perfix = `${wbs.gfH5}/share/advReportLoading`;
      params.value = encodeURIComponent(contentLink || contentUrl || analyzeRes.contentLink);
      params.id = id || analyzeRes.id;
      params.title = title || analyzeRes.title;
      params.title = encodeURIComponent(params.title);
      params.pageType = "advReportLoading";
      params.cardTitle = encodeURIComponent(
        cardTitle || cName || analyzeRes.cardTitle || "内部投资策略"
      );
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }

    //券商研报速达/晨会精华
    case PAGE_INFO_REALM.meet_essence:{
      params.perfix = `${wbs.gfH5}/brokerms`;
      params.time = showTime || analyzeRes.timePublished || timeCreated
      params.time = params.time.length>10 ? params.time.slice(0,10) : params.time
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }

      //专题研报/深度报告
      case PAGE_INFO_REALM.depth_report_module:{
        params.perfix = `${wbs.gfH5}/channel_h5/depthreport/#/depthreport`;
        params.id = id || analyzeRes.id
        wx.navigateTo({
          url: "/pages/common/webview/webPage",
          success(res) {
            res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
            setWebPageShowWay(1);
          },
        });
        break;
      }

      //券商研报列表
      case actionType.brokerResearchReport:{
        params.perfix = `${wbs.gfH5}/channel_h5/brokerslist/#/list`
        params.value = actionObject.channel + ''
        params.pageName = encodeURIComponent(actionObject.pageName.replace(/%/g, "%25"))
        params.selectedFloat = actionObject?.selectedFloat?.id || ''
        params.cardId = cId || sensorsdata.id || e?.target?.dataset?.message?.id,
        params.kIndex = kIndex,
        params.cardType = cardType,

        wx.navigateTo({
          url: "/pages/common/webview/webPage",
          success(res) {
            res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
            setWebPageShowWay(1);
          },
        });
        break;
      }


     //本地域名类型外链
     case 'outInLink':
     case actionType.outLink:{
        let pageUrl = analyzeRes.pageUrl
        if(analyzeRes?.needToken){
          let union  = pageUrl.includes("?") ? '&' : '?'
          pageUrl += `${union}${qs.stringify(params,{encode:false})}`
        }

        if(analyzeRes?.showWechatShare==0){
            let union  = pageUrl.includes("?") ? '&' : '?'
            pageUrl += `${union}`+'showWechatShare=0'
          }
        wx.navigateTo({
          url: `/pages/common/webview/webPage?url=${encodeURIComponent(pageUrl)}&pageType=${actionType.outLink}&needToken=${analyzeRes?.needToken || false}`,
          success(res) {
            setWebPageShowWay(1);
          },
        });
        break;
      }

       //自建列表
      case actionType.selfBuiltList:{
        params.perfix = `${wbs.gfH5}/channel_h5/selfBuildList/#/list`;
        params.value = analyzeRes.value
        wx.navigateTo({
          url: "/pages/common/webview/webPage",
          success(res) {
            res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
            setWebPageShowWay(1);
          },
        });
        break;
      }

    //研报列表
    case "REPORTLIST": {
      let isHasPermission = await checkHTFPermission("QSSDBG");
      if (!isHasPermission) {
        return goHTFPermission();
      }
      params.perfix = `${wbs.gfH5}/share/advReportLoading`;
      params.value = encodeURIComponent(analyzeRes.uri);
      params.id = "";
      params.title = "研报列表";
      params.pageType = "advReportLoading";
      params.cardTitle = encodeURIComponent("内部投资策略");
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    // 营销资料
    case "advProductDataNewDetail":
    case "advProductDataDetail": {
      const { value = "" } = analyzeRes || {};
      if (value || id) {
        params.value = value || id;
      }
      if (_planId) {
        const {
          success,
          param = {},
          msg = "",
        } = await getMarketingPlanInfo({ planId: _planId });
        if (success && !isEmptyObject(param)) {
          const { style = "" } = param || {};
          _pageStyle = style;
        }
      }
      params.perfix = `${wbs.gfH5}/share/${PRODUCT_PAGE_TYPE[_pageStyle]}`;
      params.title = "营销资料";
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    //TEST TODO
    case "UnionList": {
      // const {value = ''} = analyzeRes || {}
      console.log("===== UnionList analyzeRes >>>>", analyzeRes);
      wx.navigateTo({
        url: `/pages/home/<USER>/index?${qs.stringify(analyzeRes)}`,
      });
      break;
    }
    // TEST TODO
    case "tempMsg": {
      wx.reLaunch({
        url: `/pages/loginAndRegist/startUp/index?${qs.stringify(analyzeRes)}`,
      });
      break;
    }
    // 直播详情
    case "AppAdvLive": {
      console.log("****** 进入直播详情页 ******", id);
      const { value = "",faId = '',parentId = '', } = analyzeRes || {};
      if (id) {
        params.id = id;
      } else if (value) {
        params.id = value;
      }
      params.name = title;
      params.shareUserId = shareUserId || faId ;
      params.isShareCome = isShareCome
      console.log(shareUserId, "shareUserID");
      // return wx.navigateTo({
      //   url: `/package-activity/pages/live/live?params=${qs.stringify(params)}`,
      // });
      let targetPath = `/package-activity/pages/live/live?${qs.stringify(params,{encode:false})}`
      let url = `${wbs.gfH5}/share/advAuthorizeLoading?targetPath=${encodeURIComponent(targetPath)}&unionid=${geStorageUnionId()}`
      wx.navigateTo({
        url: `/pages/common/webview/webPage?url=${encodeURIComponent(url)}`,
      });
      break;
    }
    // 邀请注册
    case "AppAdvRegister": {
      wx.navigateTo({
        url: `/package-activity/pages/loginAndRegist/invite/index`,
      });
      break;
    }
    // 邀请注册
    case "AppStaffInviteRegister": {
      wx.navigateTo({
        url: `/package-activity/pages/loginAndRegist/staffInvite/staffInvite`,
      });
      break;
    }
    // 营销线索
    case "AppAdvMarketingClue": {
      params.perfix = `${wbs.gfH5}/share/AppAdvMarketingClue`;
      params.title = "营销线索";
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    // 智能名片
    case "AppAdvCard": {
      let isHtfPerson = await checkIsHtfPerson(wInfo.phone)
      if(isHtfPerson.returnMsg == 'yes'){
        return toBussinessCard(wInfo.phone)
      }
      params.perfix = `${wbs.gfH5}/share/AppAdvCard`;
      params.title = "智能名片";

      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    //权限审核
    case "audit": {
      interaction.showLoading("加载中...");
      const { success, data } = await employeeCheck({
        channelStaffToken: getToken(),
      });
      interaction.hideLoading();
      if (!success) {
        return wx.navigateTo({
          url: `/package-activity/pages/loginAndRegist/employees/index?redirect=audit-invite`,
        });
      } else {
        storage.setStorage(global.STORAGE_GLOBAL_EMPLOYEE_INFO, data);
        // return wx.reLaunch({
        //   url: `/package-surely/pages/approval/applyStatus/index`
        // })
        return wx.navigateTo({
          url: "/package-surely/pages/approval/list/index?type=0",
        });
      }
    }
    // 交易式定投
    case "AppAip": {
      wx.navigateTo({
        url: `/package-aip/pages/index/index`,
      });
      break;
    }

     // 上传图片
     case "uploadImg": {
        wx.navigateTo({
          url: `/package-activity/pages/loginAndRegist/uploadImg/uploadImg`,
        });
        break;
    }

    //养老专区
    case "provideAged": {
      params.perfix = `${wbs.gfH5}/share/advLoading`;
      params.title = analyzeRes.title;
      params.toUrl = analyzeRes.url;
      params.pageType = "provideAged";

      console.log("provideAged  params===== ", params);
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }
    //营销工具
    case "workBox": {
      params.perfix = `${wbs.gfH5}/share/advMiddlePage`;
      params.title = analyzeRes.title;
      params.toUrl = analyzeRes.pageUrl;
      params.pageType = "workBox";
      params.realm = analyzeRes.realm;
      wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
      break;
    }

    // 半屏小程序
    case "AppAdvOpenMini": {
      const {
        appId = "",
        path = "",
        envVersion = "",
        openWay = "",
        id = "",
        HTFType = "",
        extraData = "",
      } = analyzeRes || {};
      console.log("======= AppAdvOpenMini analyzeRes >>>", analyzeRes);
      let extraObj = JSON.parse(extraData) || {};
      if (HTFType == "QSSDBG") {
        let isHasPermission = await checkHTFPermission(HTFType);
        if (!isHasPermission) {
          return goHTFPermission();
        }
      }
      if (openWay === "jump") {
        return wx.navigateToMiniProgram({
          appId,
          shortLink: `${path}`,
          extraData: { unionId: geStorageUnionId() },
          envVersion,
          success(res) {
            // console.log('====== AppAdvOpenMini navigateToMiniProgram success >>>>', res)
          },
          fail(err) {
            // console.log('====== AppAdvOpenMini navigateToMiniProgram fail  >>>>', err)
          },
          complete() {
            storage.setStorage(global.STORAGE_GLOBAL_APP_HOLD, true);
          },
        });
      }

      if (openWay === "half") {
        return wx.openEmbeddedMiniProgram({
          appId,
          shortLink: `${path}`,
          envVersion,
          extraData: { unionId: geStorageUnionId() },
          success(res) {
            // console.log('====== AppAdvOpenMini openEmbeddedMiniProgram success res >>>>', res)
          },
          fail(err) {
            // console.log('====== AppAdvOpenMini openEmbeddedMiniProgram fail err >>>>', err)
          },
          complete(e) {
            storage.setStorage(global.STORAGE_GLOBAL_APP_HOLD, true);
          },
        });
      }

      if (openWay === "jumpOthers") {
        return wx.navigateToMiniProgram({
          appId,
          path,
          extraData: { ...extraObj },
          envVersion,
          success(res) {
            // console.log('====== AppAdvOpenMini navigateToMiniProgram success >>>>', res)
          },
          fail(err) {
            // console.log('====== AppAdvOpenMini navigateToMiniProgram fail  >>>>', err)
          },
          complete() {
            storage.setStorage(global.STORAGE_GLOBAL_APP_HOLD, true);
          },
        });
      }

      break;
    }

    // 营销数据通报
    case "DataBoard": {
      interaction.showLoading("加载中...");
      const { code, msg, data, success } = await employeeCheck({});
      interaction.hideLoading();
      console.log(
        "===== AppAdvBiBlock code, msg, data, success >>",
        code,
        msg,
        data,
        success
      );
      if (!success) {
        // interaction.showToast(msg || '')
        switch (code * 1) {
          case EMPLOYEE_LOGIN_STATUS.EMPLOYEE_NOT_EXIST: {
            let pagePath = getCurrentPages().pop();
            console.log(" ======= POLYMERS pagePath >>>", pagePath?.route);
            // storage.setStorage(global.STORAGE_GLOBAL_PREVIOUS_PATH, pagePath?.route)

            return wx.navigateTo({
              url: `/package-activity/pages/loginAndRegist/employees/index?currPagePath=${encodeURIComponent(
                pagePath?.route
              )}&redirect=data-board`,
            });
          }

          case EMPLOYEE_LOGIN_STATUS.EMPLOYEE_HAS_DEACTIVATED: {
            return wx.navigateTo({
              url: "/package-activity/pages/loginAndRegist/applyFail/applyFail",
            });
          }

          default:
            break;
        }
      }

      if (success && code == 0) {
        if (Object.keys(data).length) {
          storage.setStorage(global.STORAGE_GLOBAL_EMPLOYEE_INFO, data);
          const { token } = data || {};
          const { userId = "" } = wInfo || {};

          let params = {
            perfix: `${wbs.gfH5}/channel_h5/dataBoard`,
            title: "营销数据通报",
            token: token || shareToken,
            pageType: type,
            banShare: 0,
            userId,
          };

          return wx.navigateTo({
            url: "/pages/common/webview/webPage",
            success(res) {
              res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
              setWebPageShowWay(1);
            },
          });
        }
      }
      break;
    }
    // Bi看板
    case "AppAdvBiBlock": {
      interaction.showLoading("加载中...");
      const { code, msg, data, success } = await employeeCheck({});
      interaction.hideLoading();
      console.log(
        "===== AppAdvBiBlock code, msg, data, success >>",
        code,
        msg,
        data,
        success
      );
      if (!success) {
        // interaction.showToast(msg || '')
        switch (code * 1) {
          case EMPLOYEE_LOGIN_STATUS.EMPLOYEE_NOT_EXIST: {
            let pagePath = getCurrentPages().pop();
            console.log(" ======= POLYMERS pagePath >>>", pagePath?.route);
            // storage.setStorage(global.STORAGE_GLOBAL_PREVIOUS_PATH, pagePath?.route)

            return wx.navigateTo({
              url: `/package-activity/pages/loginAndRegist/employees/index?currPagePath=${encodeURIComponent(
                pagePath?.route
              )}`,
            });
          }

          case EMPLOYEE_LOGIN_STATUS.EMPLOYEE_HAS_DEACTIVATED: {
            return wx.navigateTo({
              url: "/package-activity/pages/loginAndRegist/applyFail/applyFail",
            });
          }

          default:
            break;
        }
      }

      if (success && code == 0) {
        if (Object.keys(data).length) {
          storage.setStorage(global.STORAGE_GLOBAL_EMPLOYEE_INFO, data);
          const { token } = data || {};
          const { userId = "" } = wInfo || {};

          let params = {
            perfix: `${wbs.gfH5}/share/wxStaffData`,
            title: "Bi看板",
            token: token || shareToken,
            pageType: type,
            banShare: 0,
            userId,
          };

          return wx.navigateTo({
            url: "/pages/common/webview/webPage",
            success(res) {
              res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
              setWebPageShowWay(1);
            },
          });
        }
      }
      break;
    }



    // 搜索列表(基金解读，运作报告，股票，也可传多种类型)
    case "SearchList": {
      const {
        channelIds = "",
        placeholder = "",
        keyword = "",
      } = analyzeRes || {};
      const channelArray = JSON.parse(channelIds) || [];
      let channelList = [];
      if (channelArray && channelArray.length) {
        channelArray.forEach((name) => {
          if (SEARCH_CHANNEL_EDF.hasOwnProperty(name)) {
            const props = {
              value: name,
              ...SEARCH_CHANNEL_EDF[name],
            };
            channelList.push(props);
          }
        });
        // todo
        // todo 尝试增加全部，临时处理方式
        if (channelList.length > 1) {
          channelList.unshift({
            value: "all_channel",
            label: "全部",
            type: "ALL",
            shortLink: "",
          });
        }
      }
      console.log("======== channelList >>>", channelList);
      const searchType = channelList[0]?.type || "STOCK";

      wx.navigateTo({
        url: `/package-activity/pages/common/search/search?type=${searchType}&channelList=${JSON.stringify(
          channelList
        )}&placeholder=${placeholder}&keyword=${keyword}&formTab=${formTab}`,
      });
      break;
    }

    default:
      break;
  }
}

async function checkHTFPermission(HTFType) {
  const { code, success, data, msg } = await getPermission({
    unionid: geStorageUnionId(),
  });
  if (success) {
    if (HTFType === "QSSDBG") {
      if (data.QSSDBG == "Y") {
        return true;
      } else {
        return false;
      }
    }
  } else {
    return interaction.showToast(msg || "");
  }
}

function goHTFPermission() {
  return wx.navigateTo({
    url: `/package-activity/pages/loginAndRegist/uploadImg/uploadImg`,
  });
}

// 端外跳转
export async function onHandleOutAction(e) {
  const userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);

  const {
    detail: {
      action = "",
      outAction = "",
      actionObject = {},
      actionValue = "",
      shareToken = "",
      isShareCome = false,
      timeCreated = ''
    },
  } = e || {};
  actionToken = getToken() || shareToken; // 分享token
  const { type, analyzeRes } = analyzeAction(action);
  console.log("**端内type: ", type, "analyzeRes 222222: ", analyzeRes);
  if(type == PAGE_INFO_REALM.depth_report){
    return wx.reLaunch({
      url: `/pages/loginAndRegist/login/login`
    }) 
  }

  let detailUrl = DETAIL_URL[type]
  let lastPageUrl = `${wbs.gfH5 + detailUrl[1]}?value=${analyzeRes.value}&faId=${analyzeRes.faId}&parentId=${analyzeRes.parentId}`
  if(type == PAGE_INFO_REALM.meet_essence){
    let time = analyzeRes.timePublished || timeCreated || ''
    time =  time?.length>10 ? time.slice(0,10) : time
    lastPageUrl = `${wbs.gfH5 + detailUrl[1]}?time=${time}&faId=${analyzeRes.faId}&parentId=${analyzeRes.parentId}`
  }else if(type == PAGE_INFO_REALM.depth_report_module){
    lastPageUrl = `${wbs.gfH5 + detailUrl[1]}?depthReportId=${analyzeRes.value || id}&faId=${analyzeRes.faId}&parentId=${analyzeRes.parentId}`
  }

  let sharePageUrl = `${
    wbs.gfH5
  }/marketing-api/api/v1/h5/redirect/wechat?url=${encodeURIComponent(
    lastPageUrl
  )}`;
  return wx.navigateTo({
    url: `/pages/common/webview/webPage?url=${encodeURIComponent(
      sharePageUrl
    )}`,
    success: function (res) {
      storage.setStorage(global.STORAGE_GLOBAL_REROUTE_BY_CODE, true);
    },
  });
}
