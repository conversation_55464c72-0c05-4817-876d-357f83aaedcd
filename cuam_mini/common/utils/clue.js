import { activityClue } from "../nb/home";
import {
    marketWechat,
    wechatDuration,
    getMINIShareUuid,
  } from "../network/api";
  import {
    geStorageUnionId,
  } from "../utils/userStorage";
  
  // 记录已登陆用户浏览记录
export async function clueActivity(data) {
    const { title = '', realm = '', entityId = '', type = '' , id = ''} = data
    let params = {
      title,
      "respondent.realm": realm,
      "respondent.entityId": entityId,
      type,
    };
    if(id){
      params.id = id
    }

    const { param } = await activityClue(params);
    return param?.activityId;
  }

  //获取分享UUID
  export async function getClueUUID() {
    const params = {
      timeStamp: +new Date(),
      shareType: "SHARE_CHAT",
    };
    const { param } = await getMINIShareUuid(params);
    return param;
  }

// 分享后浏览记录以及未注册用户浏览记录
export async function clueMarketWechat(data) {
  const {realm = '',entityId = '',title = '',type  = '',userId = '',parentId = '',shareId = '',} = data
  const params = {
    unionId: geStorageUnionId(),
    respondent: {
      realm: realm,
      entityId: entityId,
    },
    title,
    type,
    userId,
    parentId,
  };
  if(shareId){
    params.shareId = shareId
  }

  const { param } = await marketWechat(params);
  return param?.activityId;
}

//记录浏览时间
export async function clueWechatactivityDuration(activityId,duration) {
  const params = {
    activityId,
    duration: duration,
  };
  await wechatDuration(params); 
}