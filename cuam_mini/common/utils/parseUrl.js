/**
 * www.nb.cn?aa&bb&cc
 * 处理成 www.nb.cn?aa=11&bb=22&cc=33
 * 取值是用户的myInfo信息中取，取不到不返回
 */
module.exports = (tpUrl, info) => {
  if (tpUrl && info) {
    let sps = tpUrl.split('?').filter(o => o && o !== '')
    if (sps.length > 1) {
      let vals = sps[1].split('&').filter(o => o && o !== '')
      let arr = []
      vals.forEach(key => {
        let val = info[key]
        // console.log('key, val %s', key, val)
        if (val) {
          arr.push(`${key}=${val}`)
        }
      })
      tpUrl = `${sps[0]}?${arr.join('&')}`
      // console.log('tpUrl', tpUrl)
    }
  }
  return tpUrl
}
