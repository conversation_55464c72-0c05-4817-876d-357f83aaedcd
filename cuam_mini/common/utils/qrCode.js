import { getVersionInfo, getUserId } from "./userStorage";
import { global, storage, interaction } from "../index";
import { addSceneParam, getWXACode } from "../nb/home";
import Wxml2Canvas from "wxml2canvas";

export async function getWXACodeImg(channelName, codeParams) {
  const _versionInfo = getVersionInfo();
  const { envVersion = "" } = _versionInfo || {};
  // 没有找到小程序码则请求获取
  let userId = getUserId() || codeParams.userId;
  let codeStr = JSON.stringify(codeParams);
  const channelParams = {
    params: `${userId}&${channelName}&${codeStr}`,
    serviceChannel: "WBS",
  };
  let _scene = `${userId}`;
  const {
    success: pSucc,
    param: pData,
    msg: pMsg,
  } = await addSceneParam(channelParams);
  if (!pSucc) {
    return interaction.showToast(pMsg || "");
  }
  _scene = pData;

  const params = {
    sourceCode: global.SOURCE_CODE,
    scene: `${_scene}`,
    // page: "pages/loginAndRegist/startUp/index",
    page: "pages/common/loading/index",
    env_version: envVersion,
  };
  const { param, success, msg } = await getWXACode(params);

  return param;
}

export async function drawQrCodeImage(canvasId, self, className, limitName) {
  let drawCanvas = new Wxml2Canvas(
    {
      element: canvasId,
      background: "rgba(255, 255, 255, 0)",
      progress(percent) {},
      finish(url) {
        console.log(url, 123);
        return url;
      },
      error(err) {
        console.log("err", err);
      },
    },
    self
  );

  let data = {
    // class: draw_canvas指定待绘制的元素, limit: 限定绘制元素的范围，取指定元素与它的相对位置
    list: [{ type: "wxml", class: className, limit: limitName, x: 0, y: 0 }],
  };
  drawCanvas.draw(data);
}
