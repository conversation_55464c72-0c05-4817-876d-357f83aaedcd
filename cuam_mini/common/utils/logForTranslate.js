/* 判断取得当前浏览器的版本

 * */

function dateFormat(date, format, defaultText) {
  format = format || 'yyyy-MM-dd hh:mm'
  if (!date) {
    return defaultText
  }

  if (!isNaN(date)) {
    date = new Date(date - 0)
  }

  if (date == 'Invalid Date') {
    return defaultText
  }

  var o = {
    'M+': date.getMonth() + 1, //month
    'd+': date.getDate(), //day
    'h+': date.getHours(), //hour
    'm+': date.getMinutes(), //minute
    's+': date.getSeconds(), //second
    'q+': Math.floor((date.getMonth() + 3) / 3), //quarter
    S: date.getMilliseconds(), //millisecond
  }
  if (/(y+)/.test(format)) {
    format = format.replace(
      RegExp.$1,
      (date.getFullYear() + '').substr(4 - RegExp.$1.length)
    )
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(format)) {
      format = format.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length)
      )
    }
  }
  return format
}

// function uaMatch() {
//   // 正则表达式方式来判定user-agent字符串，得到当前访问浏览器（http代理）的版本

//   var userAgent = navigator.userAgent

//   var rMsie = /(msie\s|trident.*rv:)([\w.]+)/

//   var rFirefox = /(firefox)\/([\w.]+)/

//   var rOpera = /(opera).+version\/([\w.]+)/

//   var rChrome = /(chrome)\/([\w.]+)/

//   var rSafari = /version\/([\w.]+).*(safari)/

//   var ua = userAgent.toLowerCase()

//   var match = rMsie.exec(ua)

//   if (match != null) {
//     return { browser: 'IE', version: match[2] || '0' }
//   }

//   match = rFirefox.exec(ua)

//   if (match != null) {
//     return { browser: match[1] || '', version: match[2] || '0' }
//   }

//   match = rOpera.exec(ua)

//   if (match != null) {
//     return { browser: match[1] || '', version: match[2] || '0' }
//   }

//   match = rChrome.exec(ua)

//   if (match != null) {
//     return { browser: match[1] || '', version: match[2] || '0' }
//   }

//   match = rSafari.exec(ua)

//   if (match != null) {
//     return { browser: match[2] || '', version: match[1] || '0' }
//   }

//   if (match == null) {
//     return { browser: '', version: '0' }
//   }
// }

//   export default {

/* 在浏览器本地记录日志 */

export function logtoLocal(loginfo) {
  try {
    // if (typeof localStorage !== 'undefined') {
      var now = new Date()

      var yy = now.getFullYear() // 年

      var mm = now.getMonth() + 1 // 月

      var dd = now.getDate() // 日

      var hh = now.getHours() // 时

      var logClock = yy + '-'

      if (mm < 10) logClock += ''

      logClock += mm + '-'

      if (dd < 10) logClock += ''

      logClock += dd + ''

      //   if (hh < 10) logClock += ''

      //   logClock += hh

      // 日志持久化寻址编号，采取日期来定位 yyyy-mm-dd-hh

      var agentlogId = logClock // 设置当前日志所在区间的名称编号，避免只写一个item过大失败

      var logstr = wx.getStorageSync(agentlogId)

      if (typeof logstr === 'undefined' || !logstr) {
        logstr = ''
      }

      var str =
        '[' +
        dateFormat(now, 'yyyy-MM-dd hh:mm:ss') +
        ']' +
        loginfo +
        '<br>\r\n' // 将日志内容格式化，加入日期

      logstr = logstr + str

      if (logstr.length * 2 < 1024 * 1024 * 5) {
        wx.setStorageSync(agentlogId, logstr)

        // 将日志写入localStorage中,单个item不能超过5MB，不过一般一个小时的日志不可能超过这么多
      } else {
        console.log(
          '当前时段' +
            agentlogId +
            '记录的日志太多已无法再写日志到localStorage中'
        )
      }

      console.log('记录的日志id是' + agentlogId)

      console.log('记录的日志内容是' + str)

      // 日志写入完毕后，注意只有同域名同端口的服务端的页面才能读取，否则依然是不同的
    // } else {
    //   console.log(
    //     '抱歉！您的浏览器不支持记录本地日志。IE11浏览器则可能是因为在本地打开的网页'
    //   )
    // }
  } catch (e) {
    console.log('记录日志失败' + e)
  }
}

/* 生成和取得指定日志文件 yyyy-MM-dd-hh24
  
      */

// function downloadLog(agentlogId) {
//   var browserMatch = uaMatch()

//   var broserVer = browserMatch.browser + browserMatch.version

//   if (broserVer.indexOf('IE11') >= 0) {
//     // alert('版本浏览器是IE11');

//     var downloadFileName = agentlogId + '-cc-agent.log'

//     try {
//       var file = 'data:text/plain;charset=utf-8,'

//       var logstr = wx.getStorageSync(agentlogId)

//       file += logstr

      

//       var blobObject = new Blob([file])

//       // window.navigator.msSaveBlob(blobObject, downloadFileName)
//     } catch (e) {
//       alert('下载文件时出现错误' + e)
//     }
//   } else {
//     // alert('其他浏览器');

//     console.log('file', agentlogId)
//     try {
//       downloadFileName = agentlogId + '-cc-agent.log'

//       file = 'data:text/plain;charset=utf-8,'

//       logstr = localStorage.getItem(agentlogId)

//       var encoded = encodeURIComponent(logstr) // 这一步为了下载是必须的

//       file += encoded

//       console.log('file', logstr)
//       var downloadevent = document.createElement('a')

//       downloadevent.href = file

//       downloadevent.target = '_blank'

//       downloadevent.download = downloadFileName

//       document.body.appendChild(downloadevent)

//       downloadevent.click()

//       downloadevent.remove()
//     } catch (e) {
//       alert(
//         '您的浏览器不支持记录本地日志。IE11浏览器则可能是因为在本地打开的网页' +
//           e
//       )
//     }
//   }

//   logtoLocal('完成一次日志下载，下载日志名：' + agentlogId)
// }

/* 这里用来查看日志的 */

// function getLog(agentlogId) {
//   try {
//     logtoLocal('查询一次日志，查询日志名：' + agentlogId)

//     return wx.getStorageSync(agentlogId)
//   } catch (e) {
//     alert(
//       '您的浏览器不支持记录本地日志。IE11浏览器则可能是因为在本地打开的网页' + e
//     )
//   }
// }

/* 定期清除日志，释放空间可以用到 注意：以下清理操作是异步的，所以快速操作可能感觉起来没有清理完毕 */

// function deleteLog(agentlogId) {
//   try {
//     console.log('开始清除日志：' + agentlogId)

//     localStorage.removeItem(agentlogId)

//     console.log('完成清除日志：' + agentlogId)

//     logtoLocal('完成一次日志删除，删除日志名：' + agentlogId)
//   } catch (e) {
//     alert(
//       '您的浏览器不支持记录本地日志。IE11浏览器则可能是因为在本地打开的网页' + e
//     )
//   }
// }
