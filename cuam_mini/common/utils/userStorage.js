import storage from "./storage";

var base64 = require("./base64");

import { global,qs,log } from "../index.js";
import interaction from "../interaction/index";

const ENUMS_TRUE = ["true", "ture"];

export function setUserLoginStatus(loginStatus = false) {
  if (typeof loginStatus === "string" && ENUMS_TRUE.includes(loginStatus)){
    loginStatus = true;
  } else if (typeof loginStatus !== "boolean"){
    loginStatus = false;
  }
  return wx.setStorageSync(global.STORAGE_GLOBAL_HAS_LOGIN, loginStatus);
}

export function getUserLoginStatus() {
  return !!(wx.getStorageSync(global.STORAGE_GLOBAL_HAS_LOGIN));
}

export function getBaseParams() {
  const baseParams = wx.getStorageSync(global.STORAGE_GLOBAL_BASE_PARAMS);
  return baseParams || {};
}

export function setBaseParams(params) {
  for (const [key, value] of Object.entries(params)) {
    wx.setStorageSync(`${key}`, value);
  }
  return wx.setStorageSync(global.STORAGE_GLOBAL_BASE_PARAMS, params);
}

export function setUserRole(role) {
  return wx.setStorageSync(global.STORAGE_GLOBAL_USER_ROLE, role);
}

export function getUserRole() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_USER_ROLE);
}

export function saveUser(user) {
  wx.setStorageSync(global.STORAGE_GLOBAL_WECHAT_INFO, user);
}

export function setOrgId(id) {
  wx.setStorageSync("orgId", id);
}

export function getOrgId() {
  return wx.getStorageSync("orgId");
}

export function saveCompanyInfo(info) {
  wx.setStorageSync("companyInfo", info);
}

export function saveVersionInfo(info) {
  wx.setStorageSync("WX_VERSION_INFO", info);
}

export function setWebPageShowWay(key) {
  wx.setStorageSync("WEB_PAGE_SHOW_WAY", key);
}

export function getWebPageShowWay() {
  return wx.getStorageSync("WEB_PAGE_SHOW_WAY");
}

export function getNbToken() {
  return wx.getStorageSync("nbtoken");
}

export function setToken(token) {
  try {
    wx.setStorageSync("token", token);
  } catch (e) {
    log.info('setToken error===',e)
  }
}

export function getToken() {
  return wx.getStorageSync("token");
}

export function getOpenId() {
  return wx.getStorageSync("openid");
}

export function geStorageUnionId() {
  return wx.getStorageSync("unionid");
}

export function getUserId() {
  return wx.getStorageSync("userId");
}

export function getWechatInfoId() {
  return wx.getStorageSync("wechatInfoId");
}

export function getUser() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_WECHAT_INFO);
}

export function getCardRefreshMoment() {
  return wx.getStorageSync(global.STORAGE_GLOBAL_LOADING_MOMENT);
}

export function getCompanyInfo() {
  return wx.getStorageSync("companyInfo");
}

export function getVersionInfo() {
  return wx.getStorageSync("WX_VERSION_INFO");
}

export function getUserToken() {
  const _token = getToken();
  return _token;
}

export function getUserAppKey() {
  const user = getUser();
  if (!user || !user.name1) return null;
  return user.name1;
}

export function deleteUser() {
  storage.removeStorage(global.STORAGE_GLOBAL_WECHAT_INFO);
  storage.removeStorage(global.STORAGE_GLOBAL_USER);
  return true;
}

export function clear() {
  wx.clearStorageSync();
}

export function isLogin(cb, url, isToken = false) {
  if (isToken) {
    if (!getNbToken()) {
      let _url = "/pages/loginAndRegist/login/login";
      if (url) {
        url += "&token=true";
        _url += "?url=" + encodeURIComponent(url);
      }
      const user = getCurrentPages().pop().getUser();
      if (!user) {
        // 未登录
        interaction.showAlert(
          "尊敬的用户",
          "您还没有登录或登录已过期，请登录",
          () => {
            wx.navigateTo({
              url: _url,
            });
          },
          "取消",
          "去登录"
        );
      }
    } else {
      wx.reLaunch({
        url,
      });
    }
  } else {
    cb();
  }
}

// 保存和获取用户名片是否显示状态
export function setVisitingCard(status) {
  wx.setStorageSync("visitingCard", status);
}
export function getVisitingCard() {
  return wx.getStorageSync("visitingCard");
}
