/* 毫秒级倒计时 */

import {eventName} from "../index";

const moment = require("../../lib/monment/index")
moment.locale('en', {
  longDateFormat: {
    l: "YYYY-MM-DD",
    L: "YYYY-MM-DD HH:mm"
  }
});

const {
  DO_READY_LINE_REFRESH,
  DO_OVER_WAIT_LINE
} = eventName

var timer = null;
var waitMs = null;

export function count_down(that, total_micro_second) {
  if (total_micro_second <= 0){
    that.setData({
      verifyCode: "重新发送",
      sendLoading: false
    });
    // timeout则跳出递归
    return;
  }
  // 渲染倒计时时钟
  that.setData({
    verifyCode: total_micro_second + " 秒后重试"
  });
  timer = setTimeout(function() {
    // 放在最后--
    total_micro_second -= 1;
    count_down(that, total_micro_second);
  }, 1000)
}

export function clear_count(that) {
  if (timer != null){
    clearTimeout(timer);
    that.setData({
      verifyCode: "重新发送",
      sendLoading: false
    });
  }
}

// 时间格式化输出，如03:25:19 86。每10ms都会调用一次
function date_format(micro_second) {
  // 秒数
  var second = Math.floor(micro_second / 1000);
  // 小时位
  var hr = Math.floor(second / 3600);
  // 分钟位
  var min = fill_zero_prefix(Math.floor((second - hr * 3600) / 60));
  // 秒位
  var sec = fill_zero_prefix((second - hr * 3600 - min * 60)); // equal to => var sec = second % 60;
  // 毫秒位，保留2位
  var micro_sec = fill_zero_prefix(Math.floor((micro_second % 1000) / 10));
  return sec;
}

// 位数不足补零
function fill_zero_prefix(num) {
  return num < 10 ? "0" + num : num
}

/**
 * 倒计时
 * @param that
 * @param total_micro_second
 */
export function count_dieLine(that, total_micro_second = 0) {
  if (total_micro_second <= 0){
    that.setData({
      deathLineTime: [0, 0, 0, 0],
    })

    return
  }

  if (total_micro_second === 1){
    console.log('=======count_dieLine  DO_READY_LINE_REFRESH')
    getApp().event.emit(DO_READY_LINE_REFRESH)
  }

  let duration = moment.duration(total_micro_second, 'seconds')
  // console.log('====== diffTime duration >>>', duration)
  let {
    _data: {
      months = 0,
      days = 0,
      hours = 0,
      minutes = 0,
      seconds = 0
    }
  } = duration || {}
  days = days * 1

  if (months > 0){
    let start = moment().format('YYYY-MM-DD')
    let [year, month, day] = `${start}`.split('-')

    let month2 = (month * 1) + 1
    let year2 = year
    if (month2 === 13){
      month2 = 1;
      year2 = year + 1;
    }

    let nextMonth = moment([year2, month2, '01']).endOf('month');
    const {_d = '',} = nextMonth || {}
    let _tMonth = `${_d}`.split(' ')
    let _diffDays = _tMonth[2] * 1 || 0
    // console.log('=========== _diffDays >>>> ', _diffDays);

    if (_diffDays > 0){
      days = days + _diffDays
    }

    if (days > 99){
      days = 99
    }
  }

  that.setData({
    deathLineTime: [days, hours, minutes, seconds],
  })

  timer = setTimeout(function() {
    total_micro_second -= 1;
    count_dieLine(that, total_micro_second);
  }, 1000)
}

export function count_wait_begin(ms) {
  // console.log('count_wait_begin ms >>>>', ms)
  if (ms <= 0){
    return
  }

  if (ms === 1){
    console.log('sendMessage do starting')
    getApp().event.emit(DO_OVER_WAIT_LINE)
  }

  waitMs = setTimeout(() => {
    ms -= 1
    count_wait_begin(ms)
  }, 1000)
}


export function clear_dieLine(that) {
  if (timer !== null){
    clearTimeout(timer);
    that.setData({
      deathLineTime: [0, 0, 0, 0],
    })
  }
}

export function clear_wait() {
  if (waitMs !== null){
    clearTimeout(waitMs);
  }
}
