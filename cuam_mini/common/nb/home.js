import {http, doFetch} from '../utils/helper'
import {enums, postV2Api} from "../index.js";

// // 添加弱网缓存管理，好像并未生效
import newConfig from '../../config/index.js'

const {
  env,
} = newConfig

// // 创建缓存管理器
// const cacheManager = wx.createCacheManager({
//   origin: env.service,
// })

// // 添加请求规则
// cacheManager.addRule({
//   id: 'cache-findPageInfo-home',
//   method: 'GET',
//   url: '/v1/page/byId/findPageInfo',
//   dataSchema: [
//     {
//       name: 'pageId',
//       schema: {
//         type: 'string',
//         value: '3149722965773313'
//       }
//     }
//   ],
// })


// console.log('创建缓存管理器');

// // 监听符合规则的 wx.request 请求，默认在弱网时调用 wx.request 即会触发
// cacheManager.on('request', evt => {
//   console.log('创建缓存管理器11');
//   return new Promise((resolve, reject) => {
//     // 匹配是否存在缓存
//     const matchRes = cacheManager.match(evt)

//     if (matchRes && matchRes.data) {
//       // 使用缓存返回
//       resolve(matchRes.data)
//     } else {
//       // 没有匹配到缓存
//       reject({errMsg: `catch not found: ${evt.url}`})
//     }
//   })
// })

/**
 * 获取系统UC配置
 */
export const getUCSystemApiConfig = (data) => http('/nb/uc-system/mobile/api/v1/config/findByType', data, 'get')


/**
 * 获取汇添富权限
 */
 export const getPermission = (data) => doFetch('/v1/huitianfu/getPermission', data, 'GET', '', 'json', postV2Api.uc_system, true)

 /**
 * 请求研报权限
 */
  export const requestPermission = (data) => doFetch('/v1/huitianfu/requestPermission', data, 'POST', '', 'json', postV2Api.uc_system, true)

 /**
 * 催一催
 */
export const pushPermission = (data) => doFetch('/v1/huitianfu/pushPermission', data, 'POST', '', 'json', postV2Api.uc_system, true)


/**
 * 邀请记录
 */
export const getStaffInviteList = (data) => http('/nb/uc-system/mobile/api/v1/org/staff/invite/list', data, 'get')

/**
 * 获取UnionId
 */
export const getUnionId = (data) => doFetch('/v1/mini/wechat/getUnionid', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 检查用户是否添加企业微信并激活
 */
export const checkActiveUser = (data) => doFetch('/v1/org/staff/checkActiveUser', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 检查本地用户是否添加企业微信
 */
export const checkLocalActiveUser = (data) => doFetch('/v1/org/staff/checkIsFollowd', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 检查用户状态
 */
export const checkLogin = (data) => doFetch('/v1/org/staff/checkLogin', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 获取配置信息
 */
export const findByConfigType = (data) => doFetch('/v1/config/findByConfigType', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 获取配置信息
 */
export const findByType = (data) => doFetch('/v1/config/findByType', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 重新绑定
 */
export const reBinding = (data) => doFetch('/v1/org/staff/reBinding', data, 'POST', '', 'json', postV2Api.uc_system, true)


/**
 * 重新绑定微信
 */
 export const reBindingRequest = (data) => doFetch('/v1/org/staff/reBindingUnionId', data, 'POST', '', 'json', postV2Api.uc_system, true)


/**
 * 重新绑定手机号
 */
 export const reBindingPhone = (data) => doFetch('/v1/org/staff/reBindingPhone', data, 'POST', '', 'json', postV2Api.uc_system, true)


/**
 * 理财师卡片查询
 */
export const getAdvStaffCardInfo = (data) => doFetch('/v1/staff/card/get', data, 'GET', '', 'json', postV2Api.uc_system, true, true)
/**
 * 
 * 用户触达埋点
 */
 export const mnsMiniReach = (data) => doFetch('/api/v1/reach', data, 'POST', '', 'form', postV2Api.mns_mini, true, true)


/**
 * 
 * 用户触达埋点
 */
 export const activityClue = (data) => doFetch('/v1/activity', data, 'POST', '', 'form', postV2Api.marketing, false)


/**
 * 添加小程序二维码动态参数
 */
export const addSceneParam = (data) => doFetch('/v1/wechat/addString', data, 'GET', '', 'json', postV2Api.marketing, false, false, false)

/**
 * 获取二维码添加的动态参数
 */
export const getSceneParam = (data) => doFetch('/v1/wechat/getParams', data, 'GET', '', 'json', postV2Api.marketing, false, false, false,false,true)

/**
 * 获取小程序二维码
 */
export const getWXACode = (data) => http('/nb/marketing-api/api/v1/wechat/wxacode', data, 'POST',)


/**
 * 
 * 获取渠道二维码信息
 */
 export const getEmployeeCode = (data) => doFetch('/v1/QRCode/get', data, 'GET', '', 'json', postV2Api.uc_system, true)


export const getPageTemplateInfo = (data) => doFetch(
  '/v1/page/custom/find',
  data,
  'GET',
  '',
  'json',
  postV2Api.mns,
  false)

export const getTemplateById = (data) => doFetch('/v1/page/byId/findPageInfo', data, 'GET')

export const getContentCategoryFilter = (data) => doFetch('/v1/page/searchCategoryList', data)

/**
 * 获取聚合列表数据
 */
export const getFindById = (data) => doFetch('/v1/agg/cate/findById', data, 'GET')

/**
 * 获取资讯列表
 */
export const getNewsList = (data) => doFetch('/v1/article/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取市场机会列表
 */
 export const getMarketNewsList = (data) => doFetch('/v1/dataBuild/marketNewsList', data, 'POST', '', 'json', postV2Api.marketing, true)


/**
 * 
 * 获取基金经理列表
 */
 export const getMangerList = (data) => doFetch('/v1/fundManager/findAllByFundManagerChannel', data, 'POST', '', 'json', postV2Api.marketing_product, true)

 /**
 * 
 * 获取基金产品列表
 */
export const getProductList = (data) => doFetch('/v1/fund/findByFundChannel', data, 'POST', '', 'json', postV2Api.marketing_product, true)


 /**
 * 
 * 查询全部展示频道
 */
  export const getFindAllDisplay = (data) => doFetch('/v1/fund/findAllDisplay', data, 'GET', '', 'json', postV2Api.marketing_product, true)

 /**
 * 
 * 查询全部标签
 */
  export const getFindAll = (data) => doFetch('/v1/labelGroup/findAll', data, 'GET', '', 'json', postV2Api.marketing_product, true)


/**
 * 
 * 查询过滤条件
 */
export const getFindAllFilters = (data) => doFetch('/v1/fund/findAllFilters', data, 'GET', '', 'json', postV2Api.marketing_product, true)

/**
 * 
 * 根据过滤条件查询产品数量
 */
export const getFindCountByFilter = (data) => doFetch('/v1/fund/findCountByFilter', data, 'POST', '', 'json', postV2Api.marketing_product, true)

/**
 * 
 * 查询基金产品列表
 */
 export const getFindByFundChannel = (data) => doFetch('/v1/fund/findListFilterPage', data, 'POST', '', 'json', postV2Api.marketing_product, true)

 /**
 * 
 * 基金产品设置关注状态
 */
  export const setStatus = (data) => doFetch('/v1/fundCare/setStatus', data, 'POST', '', 'json', postV2Api.marketing_product, true)


 /**
 * 
 * 基金产品设置关注状态
 */
  export const careList = (data) => doFetch('/v1/fundCare/careList', data, 'POST', '', 'json', postV2Api.marketing_product, true)


/**
 * 
 * 获取快讯列表
 */
 export const getFlashList = (data) => doFetch('/v1/quickMessage/listBySearch', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 
 * 获取券商研报列表
 */
 export const getBrokerResearchReportList = (data) => doFetch('/v1/dataBuild/brokerResearchReportList', data, 'POST', '', 'json', postV2Api.marketing, true)


 /**
  * 
  * 获取专题列表
  */
export const getSpecialList = (data) => doFetch('/v1/h5/specialTopic/list', data, 'POST', '', 'json', postV2Api.marketing, true)


/**
 * 获取海报列表 (登录用户)
 */
export const getPosterList = (data) => doFetch('/v1/poster/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取海报列表 (游客模式)
 */
export const getH5PosterList = (data) => doFetch('/v1/h5/poster/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取音频列表
 */
export const getMulitList = (data) => doFetch('/v1/h5/course/list', data, 'POST', '', 'json', postV2Api.marketing_course, true)

/**
 * 获取营销方案列表
 */
export const getMarketPlanList = (data) => doFetch('/v1/orgMarketPlan/listByCategoryAndOrg', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 首页获取营销方案列表
 */
export const getHomeMarketPlanList = (data) => doFetch('/v1/h5/marketPlan/lib/list', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 上报线索
 */
export const setShareActivityAction = (data) => doFetch('/v1/activity', data, 'POST', '', 'form', postV2Api.marketing, true)

/**
 * 上报用户信息
 */
export const setShareActivityUInfo = (data) => doFetch('/v1/h5/wechat/action', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 上报用户信息
 */
export const setShareWechat = (data) => doFetch('/v1/h5/wechat', data, 'POST', '', '', postV2Api.marketing, true)

/**
 * 获取资讯分类
 */
export const getArticleClassify = (data) => doFetch('/v1/article/categoryById', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取营销资料分类
 */
export const getMarketPlanClassify = (data) => doFetch('/v1/h5/marketPlanCategory/categoryList', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取多媒体分类
 */
export const getMulitClassify = (data) => doFetch('/v1/h5/course/category/list', data, 'GET', '', 'json', postV2Api.marketing_course, true)

/**
 * 获取海报分类
 */
export const getPosterClassify = (data) => doFetch('/v1/poster/category/ids', data, 'GET', '', 'json', postV2Api.marketing, true)

/**
 * 获取推荐营销资料
 */
export const getRecommendList = (data) => doFetch('/v1/orgMarketPlan/listByCategoryAndOrg', data, 'POST', '', 'json', postV2Api.marketing, true)

/**
 * 
 * 获取基金经理分类
 */
 export const getMangerClassify = (data) => doFetch('/v1/fundManager/findAllDisplay', data, 'GET', '', 'json', postV2Api.marketing_product, true)

 /**
  * 
  * 获取基金产品分类
  */
  export const getProductClassify = (data) => doFetch('/v1/fund/findAllDisplay', data, 'GET', '', 'json', postV2Api.marketing_product, true)


/**
 * 注册服务协议
 */
export const getAgreement = () => doFetch('/v1/config/findByType', data, 'POST', '', 'json', '/uc-system', true)

/**
 * 登录获取验证码
 */
export const logincode = (data) => doFetch('/admin/login/sendCode', data, 'POST', '', 'json', '/uc-system', true)

/**
 * 登录
 */
export const newLogin = (data) => doFetch('/v1/org/staff/login', data, 'POST', '', 'json', postV2Api.uc_system, true)

/**
 * 员工校验
 */
export const employeeCheck = (data) => doFetch('/login/checkEmployeeLogin', data, 'POST', '', 'json', postV2Api.uc_login)

/**
 * 员工获取验证码
 */
export const employeeSMSCode = (data) => doFetch('/login/sendCode', data, 'POST', '', 'form', postV2Api.uc_login)

/**
 * 员工登录
 */
export const employeeLogin = (data) => doFetch('/login', data, 'POST', '', 'json', postV2Api.uc_login)

/**
 * 获取验证码
 */
export const gflogincode = (data) => doFetch('/v1/org/staff/sendCode', data, 'POST', '', 'form-data', postV2Api.uc_system, true)

export const getMarketingPlanInfo = (data) => doFetch('/v1/h5/marketPlan/get', data, 'GET', '', 'json', postV2Api.marketing)

export const getMarketPosterList = (data) => doFetch(
  '/v1/poster/list',
  data,
  'GET',
  '',
  'json',
  postV2Api.marketing,
  true
)

/**
 * 获取用户关注公众号状态
 */
export const getAttentionStatus = (data) => doFetch('/v1/orgStaff/queryAttentionStatusByUnionId', data, 'GET', '', 'json', postV2Api.uc_admin)

/**
 * 获取活动拉新数据
 */
export const getPromotionalData = (data) => doFetch('/v1/miniapp/promotional/queryPromotionalData', data, 'GET', '', 'json', postV2Api.marketing)

/**
 * 获取活动剩余名额
 */
export const getInvitationData = (data) => doFetch('/v1/miniapp/promotional/queryInvitationData', data, 'GET', '', 'json', postV2Api.marketing)

/**
 * 搜索->基金解读
 */
export const searchFunds = (data) => doFetch('/fund_search', data, 'GET', '', 'json', '', false, false, true)

/**
 * 搜索->产品运作回顾
 */
export const searchFundReport = (data) => doFetch('/fund_report_search', data, 'GET', '', 'json', '', false, false, true)

/**
 * 日期列表返回接口（股票查询专用）
 */
export const getFundDateList = (data) => doFetch('/getFundUnscrambleDateList', data, 'GET', '', 'json', '', false, false, true)

/**
 * 搜索->股票列表
 */
export const searchByStock = (data) => doFetch('/getholdStockData', data, 'GET', '', 'json', '', false, false, true)

/**
 * 获取浮层弹窗
 */
export const getMobileDTOById = (data) => doFetch('/v1/floating/getMobileDTOById', data, 'GET', '', 'json', postV2Api.mns, false, false, false)

/**
 * 获取详情浮层弹窗
 */
 export const findByContentIdAndType = (data) => doFetch('/v1/floating/findByContentIdAndType' , data, 'GET', '', 'json', postV2Api.mns, false, false, false)

 /**
 * 获取列表浮层弹窗
 */
  export const findByIdAndType = (data) => doFetch('/v1/floating/findByIdAndType' , data, 'POST', '', 'json', postV2Api.mns, false, false, false)


/**
 * 生效二维码
 */
export const countByUserAndOrgUsingGet = (data) => doFetch('/v1/QRCode/list/countByUserAndOrg', data, 'GET', '', 'json', postV2Api.uc_system, true,false,false,true)

/**
 * 历史二维码列表
 */
export const qrCodeListUsingGet = (data) => doFetch('/v1/QRCode/list', data, 'GET', '', 'json', postV2Api.uc_system, true,false,false,true)

/**
 * 二维码邀请列表
 */
export const listByQRCodeUsingGet = (data) => doFetch('/v1/org/staff/invite/listByQRCode', data, 'GET', '', 'json', postV2Api.uc_system, true,false,false,true)
/**
 * 获取二维码id
 */
export const qrCodeIdUsingGet = (data) => doFetch('/v1/QRCode/getQRCodeId', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 历史二维码详情
 */
export const qrCodeDetailUsingGet = (data) => doFetch('/QRCode/get', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 保存二维码
 */
export const qrCodeSaveUsingPost = (data) => doFetch('/v1/QRCode/save', data, 'POST', '', 'json', postV2Api.uc_system, true,false,false,true)

/**
 * 失效二维码状态变更
 */
export const qrCodeUpdateStatusUsingGet = (data) => doFetch('/v1/QRCode/updateStatus', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 查询渠道子节点
 */
export const getQueryOrgNode = (data) => doFetch('/v1/org/queryOrgNode', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 查询渠道子节点 权限
 */
export const queryOrgNodeByPermissionUsingGet = (data) => doFetch('/v1/org/queryOrgNodeByPermission', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 查询所有渠道节点
 */
export const findAllDisplayNode = (data) => doFetch('/v1/org/orgCategory/findAllDisplay', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 查询所有渠道节点 权限
 */
export const allDisplayNodePermissionUsingGet = (data) => doFetch('/v1/org/orgCategory/findByPermission', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 获取渠道节点
 */
export const findLastNode = (data) => doFetch('/v1/org/isLastNode', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 获取渠道节点
 */
export const findLastNodeByPermissonUsingGet = (data) => doFetch('/v1/org/isLastNodeByPermission', data, 'GET', '', 'json', postV2Api.uc_system, true)

/**
 * 分享设置查询接口
 */
 export const checkShareConfig = (data) => doFetch('/v1/shareConfig/findByUser', data, 'GET', '', 'json', postV2Api.uc_system, true)

 /**
  * 分享设置记录编辑接口
  */
 export const setShareConfig = (data) => doFetch('/v1/shareConfig/setShareConfig', data, 'POST', '', 'json', postV2Api.uc_system, true)
 

/**
 * 获取普通链接内容
 */
export const getUrl2HtmlInfo = (data) => {
  return new Promise((resolve, reject) => {
    wx.request({
      url: data.url,
      method: 'GET',
      header: {
        'Content-Type': 'text/json;charset=UTF-8',
        'X-Requested-With': 'XMLHttpRequest',
      },
      data: {},
      success(response) {
        const {data, statusCode} = response || {}
        if (statusCode === 200){
          resolve(data)
        } else {
          reject(response)
        }
      }
    })
  })
}

/**
 * 获取指能添富指数指标接口
 */
 export const getAllIndexList = (data) => {
  const HTFPARTNER = {
    testPerfix:"https://openuat.htfpartner.com",  //小工具测试环境
    perfix:"https://open.htfpartner.com",     //小工具生产
  }
  let perfix = env.serviceTag?HTFPARTNER.perfix:HTFPARTNER.testPerfix
  let url = perfix + '/indexarea-api/api/star/getStarAllIndexList'
  return new Promise((resolve, reject) => {
    wx.request({
      url,
      method: 'GET',
      header: {
        'Content-Type': 'text/json;charset=UTF-8',
        'X-Requested-With': 'XMLHttpRequest',
      },
      data,
      success(response) {
        const {data, statusCode} = response || {}
        if (statusCode === 200){
          resolve(data)
        } else {
          reject(response)
        }
      }
    })
  })
}
