import * as request from "./requests";

import { md5, application, postV2Api, wbs } from "../index.js";
import { SOURCE_CODE } from "../const/global.js";

/**
 * 未登录状态获取entId
 */
export function getEntId(param) {
  return postV2("passport.common.getentid", { ...param }, postV2Api.passport);
}

export function getEntOtherParams(param) {
  return postV2("passport.otherparams", param, postV2Api.passport);
}

/**
 * 登录
 */
export function login(data) {
  const { mobile, password, captcha } = data;
  const param = {
    mobile,
    password: md5.hexMD5(password),
    captcha,
  };
  return post("investor/login.json", param, false, true);
}

/**
 * 底下企业简码访问这个接口 https://jira.newbanker.cn/browse/WBS-14823
 */
export function enterpriseDetail(data) {
  return post("/common/getEnterprise.json", data);
}

/**
 *  名片相关访问这个接口
 */
export function getWechatCompanyDetail(data) {
  return post("/wechat/company/detail.json", data);
}

export function getBelongsArea(data) {
  return post("common/getBelongsArea.json", data);
}

export function regist(data) {
  data.password = md5.hexMD5(data.password);
  return post("investor/register.json", data);
}

export function agreementDetail(data) {
  return post("common/agreement/detail.json", data);
}

//校验验证码
export function checkAuthCode(data) {
  return post("common/checkAuthCode.json", data);
}

export function listSupport(data) {
  return post("common/listSupport.json", data);
}

export function listQuestion(data) {
  return post("common/listQuestion.json", data);
}

export function searchQuestion(data) {
  return post("common/searchQuestion.json", data);
}

export function questionDetail(data) {
  return post("/common/detailQuestion.json", data);
}

/**
 * 判断link是否可用
 */
export function linkAvailable(data) {
  return post("common/linkAvailable.json", data);
}

/**
 * 资讯分类
 */
export function getNewsTypes(params) {
  return postV2("wbsres.infomationCategory.searchH5", params, postV2Api.webres);
}

/**
 * 公司名片
 */
export function getCompanyDetail(data) {
  return post("wechat/company/detail.json", data);
}

//通过code获取wc Token
export function weChatLogin(param) {
  return postV2(
    "wechat.login",
    {
      ...param,
    },
    postV2Api.wcWeb
  );
}

//微信关联
export function associate(param) {
  return postV2("wechat.associate", { ...param }, postV2Api.wcWeb);
}

//添加formId
export function addFormId(param) {
  return postV2("wechat.addformid", param, postV2Api.wcWeb);
}

// 获取订阅消息模板ID（未读 tid = 654）
export function getUnreadMsgTemplateID(
  param = { tid: 654, sourceCode: "wbs_investor" }
) {
  return postV2("wechat.obtain.tid", param, postV2Api.wcWeb);
}

/**
 * html转 wxml
 */
export function html2wxml(param) {
  return postV2(
    "wechat.wxml",
    {
      ...param,
    },
    postV2Api.wcWeb
  );
}

/**
 * 分享时长参数转短id存储接口
 */
export function authAddString(data) {
  return postV2("webapi.auth.addstring", data, postV2Api.webApi);
}

/**
 * 分享时长参数转短id获取接口
 */
export function authGetString(data) {
  let params = { paramKey: data.id };
  return get("api/v1/wechat/getParams", params, wbs.leadsApi).then((res) => {
    res.value = { stringUrl: res.param };
    return res;
  });
}

export function authContactAdvisor(data) {
  return postV2("webapi.investor.contactadvisor", data, postV2Api.webApi);
}

/**
 * 获取名片浏览记录, p1代表顾问页面
 */
export function wechatActivityList(userId) {
  return get(
    "api/v1/mini/wechat/activity/list",
    { userId },
    wbs.leadsApi,
    true,
    "p1"
  );
}

/**
 * 获取分享Id
 */
export function getShareUuid(params) {
  return get("api/v1/activity/share/uuid", params, wbs.leadsApi, true, "p1");
}

/**
 * 获取小程序分享Id
 */
export function getMINIShareUuid(params) {
  return get("api/v1/mini/wechat/share/uuid", params, wbs.leadsApi, true, "p1");
}

/**
 * 记录名片访问记录
 */
export function miniWechatPost(data) {
  return postV3(
    "/api/v1/mini/wechat/action",
    {
      respondent: {
        realm: "MICROMARKETING",
      },
      type: "VIEW",
      wechatCode: SOURCE_CODE,
      ...data,
    },
    true,
    true,
    "json"
  );
}

/**
 *  记录访问时长
 */
export function wechatDuration(data) {
  return postV3("api/v1/h5/wechat/duration", data);
}

/**
 * 记录微信用户动作
 */
export function wechatAction(data) {
  return postV3("api/v1/h5/wechat/action", data, true, true, "json");
}

/**
 * 营销线索接口
 */
export function marketWechat(param) {
  return postV3("api/v1/mini/wechat/action", { ...param }, true, true, "json");
}

function get(url, data, domain = wbs.service, needServiceChannel = true, flag, needToken = true) {
  return request.get({
    url: { path: url, prefix: domain },
    data,
    needServiceChannel,
    domain,
    flag,
    needToken
  });
}

//原始wbs请求方式
function post(
  url,
  param = {},
  token = true,
  sign = true,
  dataType = "form",
  domain = wbs.service,
  flag
) {
  return request.post({
    url: {
      path: `${url}`,
      prefix: domain,
    },
    param,
    token,
    sign,
    dataType,
    flag,
  });
}

//wbs postV2
function postV2(
  name,
  param = {},
  path = postV2Api.sop,
  token = true,
  sign = true,
  dataType = "json",
  flag,
  domain = wbs.v2Service
) {
  return request.post({
    url: {
      path,
      prefix: domain,
    },
    param: {
      ...param,
      name,
    },
    token,
    sign,
    dataType,
    signType: "v2",
    flag,
  });
}

// 展业中心Post
function postV3(
  url,
  param = {},
  token = true,
  sign = true,
  dataType = "form",
  domain = wbs.leadsApi,
  flag
) {
  return request.post({
    url: {
      path: `${url}`,
      prefix: domain,
    },
    param,
    token,
    sign,
    dataType,
    signType: "v3",
    flag,
  });
}

//访问名片夹
export function accessCard(param) {
  return postV2("webapi.card.access", param, postV2Api.webApi);
}

//名片夹列表
export function getCardList(param) {
  return postV2("webapi.card.list", param, postV2Api.webApi);
}

//opensaas小程序 获取unionId
export function getUnionId(params) {
  return postV3("api/v1/mini/wechat/getUnionid", params, true, true, "json");
}

//获取直播列表
export function getLiveList(params) {
  return postV2(
    "lc.app.live.webLivePage",
    params,
    postV2Api.newHttp,
    true,
    true,
    "json",
    "live",
    wbs.gfH5
  );
}

/**
 * 根据直播分类 ids
 * 查询移动端wbs直播分类列表
 */
export function getCategoryListByIds(params) {
  return postV2(
    "lc.app.live.wbsCategoryListByIds",
    params,
    postV2Api.newHttp,
    true,
    true,
    "json",
    "live",
    wbs.gfH5
  );
}

// 根据直播内容id获取直播详情
export function getLiveDetailById(params) {
  return postV2(
    "lc.app.live.webLiveDetail",
    params,
    postV2Api.newHttp,
    true,
    true,
    "json",
    "live",
    wbs.gfH5
  );
}

// 直播关联营销资料
export function getMarketPlanByLiveId(data) {
  return get(
    "marketing-api/api/v1/marketPlan/findByLiveId",
    data,
    wbs.gfService,
    true,
    "p1"
  );
}

// 直播端外人员信息
export function getStaffInfo(data) {
  return get(
    "uc-system/mobile/api/v1/org/staff/get",
    data,
    wbs.gfService,
    true,
    "p1"
  );
}

//汇添富接口判断是否为汇添富员工
export function isHtfPerson(data){
  return get(
    `fixed/api/account/isHtfPerson/${data}`,
    {},
    wbs.htfService,
    true,
    '',
    false,
  );
}

//汇添富接口判断是否为汇添富员工
export function getHtfCardInfo(data){
  return get(
    `app/api/business/card/info?phone=${data}`,
    {},
    wbs.htfService,
    true,
    '',
    false,
  );
}

