import { application } from '../../common/const/env.js'

import {
  md5,
  format,
} from '../index.js'

export default (data, access_token = '') => {
  // const appKey = userStorage.getUserAppKey()
  const secret = application.secret

  let opts = {
    app_key: 'test', // fs-upload里的appKey后端定的固定用test
    name: 'fs.file.common.upload',
    timestamp: format.formatTime(new Date(), '-'),
    version: '',
    format: 'json',
    data: encodeURIComponent(JSON.stringify(data))
  }

  if (access_token) {
    opts.access_token = access_token
  }

  const keys = Object.keys(opts).sort()

  let signature = ''
  keys.forEach((key, index) => {
    signature += `${key}${opts[key]}`
  })

  return {
    ...opts,
    sign: md5.hexMD5(`${secret}${signature}${secret}`).toUpperCase()
  }
}
