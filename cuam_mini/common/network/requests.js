import {
  openApiConfig,
  application,
  postV2Api,
  wbs,
} from '../../common/const/env.js'

import {
  storage,
  userStorage,
  md5,
  base64,
  format,
  interaction,
} from '../index.js'

import {
  getDomain
} from "../utils/util.js"

import config from '../const/config.js'

import fsSign from './fs-sign.js'

const domain = wbs.service

const contentType = {
  json: 'application/json;charset=utf-8',
  form: 'application/x-www-form-urlencoded;charset=utf-8'
}

export function post(params = {
  url: {prefix: domain},
  cancelable: false,
  dataType: 'form',
  token: true,
  sign: true,
  signType: 'v1',
  flag
}) {
  // console.log('========= request params >>>>>', params)
  if (params.param && params.param.name){
    let $symbol = params.url.path.indexOf('?') > -1 ? '&_=' : '?_='
    params.url.path += $symbol + params.param.name
  }

  let {
    token,
    param,
    sign,
    dataType,
    cancelable,
    signType = 'v1'
  } = params

  const userToken = popToken(param)
  let headers = header(dataType, signType, userToken)
  if (config.isOpenSaaS){
    const entId = getApp().globalData.entId
    if (entId){
      headers['entId'] = entId
    }
  }

  let data = {
    token: token && signType == 'v1' ? userToken : null,
    pageNo: param.pageNo >= 1 && param.pageSize > 0 ? param.pageNo : undefined,
    pageSize: param.pageNo >= 1 && param.pageSize > 0 ? param.pageSize : undefined,
    orderBy: param.orderBy
  }

  if (signType == 'v1'){
    if (param.pageNo >= 1 && param.pageSize > 0){
      delete param.pageNo
      delete param.pageSize
    }
  } else if (signType == 'v2'){
    if (param.pageNo >= 1 && param.pageSize > 0){
      param = {
        ...param,
        start: param.pageNo,
        limit: param.pageSize,
      }
      delete param.pageNo
      delete param.pageSize
    } else if (signType == 'v3' || signType == 'v4'){
      delete param.pageNo
      delete param.pageSize
    }
  }

  delete param.signType
  delete param.orderBy

  params.param = param
  const url = fetchUrl(params)
  data = signature(data, params, sign, signType)
  const wrapperData = wrapper(data, dataType, signType)

  // console.log('======= request post url >>>>', url)
  // console.log('======= request post wrapperData >>>>', wrapperData)
  // console.log('======= request post headers >>>>', headers)
  return new Promise((resolve, reject) => {
    wx.request({
      url: url,
      method: 'post',
      data: wrapperData,
      header: headers,
      success(res) {
        logHttpRes({
          status: 'success',
          requestMethod: 'post',
          signType,
          url,
          headers,
          params: param,
          wrapperData,
          res: res.data
        })
        resolve(res)
      },
      fail(res) {
        logHttpRes({status: 'false', requestMethod: 'post', signType, url, headers, params: param, wrapperData, res})
        reject(res)
      }
    })
  }).then(filterStatus)
    .then(filterNginxIntercept)
    .then(filterLogout)
    .catch(e => {
      const msg = e.errMsg ? e.errMsg : '服务器出错,请稍后再试'
      return {
        success: false,
        msg: msg
      }
    })
}

function logHttpRes(data = {
  status: 'success',
  requestMethod: 'POST',
  signType: 'v2',
  url: '',
  headers: {},
  params: {},
  wrapperData: {},
  res: {},
}) {
  const {status, requestMethod, signType, url, headers, params, wrapperData, res} = data
  //   console.info(`http ${requestMethod} ${status} ${signType ? signType : ''}`,
  //     '\n==== url >>>>: ', url,
  //     '\n===== headers >>>>>: ', JSON.stringify(headers),
  //     '\n====== params >>>>>>: ', JSON.stringify(params),
  //     '\n====== wrapperData >>>>: ', wrapperData,
  //     '\n===== res >>>>: ', res)
}

//post函数结束
function popToken(param) {
  const token = param.token ? param.token : userStorage.getUserToken()
  delete param.token
  // console.log('===== token >>>>',token)
  return token
}

function header(dataType, signType = 'v1', token) {
  switch (signType) {
    case 'v1':
      return {
        'Accept': 'application/json',
        'Content-Type': dataType == 'json' ? contentType.json : contentType.form,
        'X-Requested-With': 'XMLHttpRequest',
        'x-channel': 'miniprogram',
        'version': config.version,
        ...getProviderHeader()
      }
    case 'v2':
      return {
        'Accept': 'application/json',
        'Content-Type': contentType.json,
        'X-Requested-With': 'XMLHttpRequest',
        'token': token || '',
        'x-channel': 'miniprogram',
        'version': config.version,
        ...getProviderHeader()
      }
    case 'v3':
      return {
        'Accept': 'application/json',
        'Content-Type': dataType == 'json' ? contentType.json : contentType.form,
        'X-Requested-With': 'XMLHttpRequest',
        'token': token,
        'serviceChannel': 'WBS',
        'x-channel': 'miniprogram',
        'version': config.version,
        ...getProviderHeader()
      }
    case 'v4':
      return {
        'Accept': 'application/json',
        'Content-Type': dataType == 'json' ? contentType.json : contentType.form,
        'X-Requested-With': 'XMLHttpRequest',
        'Authorization': `bearer ${token}`,
        'x-channel': 'miniprogram',
        'version': config.version,
        ...getProviderHeader()
      }
  }
}

//header函数结束（根据不同的参数设置不同的请求头）

export function get(params = {
  url: {
    prefix: domain,
    path
  },
  withHeader: true,
  cancelable: false,
  dataType: 'form',
  flag,
  needToken:true
}) {
  const {
    url: {
      prefix,
      path
    },
    withHeader = true,
    needServiceChannel
  } = params
  let url = prefix ? `${prefix}/${path}` : `${path}`

  const {
    data,
    dataType = 'form',
    cancelable,
    needToken
  } = params

  let {
    token
  } = data ? data : {}

  let headers = header(dataType)
  if (config.isOpenSaaS){
    const entId = getApp().globalData.entId
    if (entId){
      headers['entId'] = entId
    }
  }

  if (!token){
    token = userStorage.getUserToken()
  }
  if(!needToken){
    token = ''
  }
  headers['X-Authorization'] = `Bearer ${token}`
  headers['Authorization'] = `Bearer ${token}`

  if (needServiceChannel){
    headers['serviceChannel'] = 'WBS'
    headers['token'] = `${token}`
  }

  headers['clientHost'] = config.isProvider ? config.extConfig.request.original : getDomain(wbs.service)
  headers['x-channel'] = 'miniprogram'

  console.log('### = request get url >>>>', url)
  console.log('### = request get data >>>>', data)
  // console.log('======= request get headers >>>>', headers)
  return new Promise((resolve, reject) => {
    wx.request({
      url: url,
      method: 'get',
      data: data,
      header: headers,
      success(res) {
        logHttpRes({
          status: 'success',
          requestMethod: 'get',
          url,
          headers,
          params: data,
          wrapperData: data,
          res: res.data
        })
        resolve(res)
      },
      fail(res) {
        logHttpRes({status: 'faile', requestMethod: 'get', url, headers, params: data, wrapperData: data, res})
        reject(res)
      }
    })
  }).then(filterStatus)
    .then(filterNginxIntercept)
    .then(filterLogout)
    .catch(e => {
      const msg = e.errMsg ? e.errMsg : '服务器出错,请稍后再试'
      return {
        success: false,
        msg: msg
      }
    })
}

//get函数结束

function signature(data, params, sign, signType = 'v1') {//resData要缓存的数据
  if (!sign) return
  const {
    param,
    url
  } = params
  switch (signType) {
    case 'v1': {
      const appkey = userStorage.getUserAppKey()
      var decodeAppKey = appkey ? new base64.Base64().decode(appkey) : null
      const signStr = `${JSON.stringify(param)},${decodeAppKey}`
      return {
        ...data,
        param,
        sign: md5.hexMD5(signStr)
      }
    }
    case 'v2': {
      const {
        appKey,
        secret
      } = url && url.path && url.path === postV2Api.openapi ? openApiConfig : application
      const {
        name
      } = param
      delete param.name
      const system = {
        name,
        app_key: appKey,
        data: encodeURIComponent(JSON.stringify(param)),
        timestamp: format.formatTime(new Date(), '-'),
        version: '',
        access_token: ''
      }

      const keys = Object.keys(system).sort()

      let signature = ''
      keys.forEach((key, index) => {
        signature += `${key}${system[key]}`
      })
      return {
        ...system,
        sign: md5.hexMD5(`${secret}${signature}${secret}`).toUpperCase()
      }
    }
    case 'v3':
    case 'v4': {
      return param
    }
  }
  return null
}

//signature函数结束


function fetchUrl(params) {
  let {
    url
  } = params
  return `${url.prefix}${url.path}`
}

function wrapper(data, dataType, signType = 'v1') {
  switch (signType) {
    case 'v1':
      return dataType == 'form' ? `data=${JSON.stringify(data)}` : data
    case 'v2':
      return data
    case 'v3':
    case 'v4':
      return dataType == 'json' ? JSON.stringify(data) : data
  }
}

function filterStatus(res) {
  console.info(`api request status: ${res.statusCode}`)
  if (res.statusCode >= 200 && res.statusCode <= 500){
    return res.data
  } else {
    let error = new Error()
    error.res = res
    error.errMsg = errMsg
    error.type = 'Http'
    throw error
  }
}

function filterLogout(data) {
  const {
    errorCode,
    code,
    statusCode
  } = data //errorCode 是wbs原生返回的错误码 code是postV2返回的错误码

  if ((code && code === 1001) || (errorCode && errorCode === 1001) || (statusCode && statusCode === 1001)){
    getApp().clearUserData()
    interaction.showLoginDialog()
  }
  return data
}

function filterNginxIntercept(res) {
  const {
    nginxIntercept,
    msg
  } = res
  if (nginxIntercept){
    return {
      code: -1,
      success: false,
      msg,
    }
  }
  return res
}

// download upload
export function upload({api, filePath, name, params,}) {
  let headers = {}
  if (config.isOpenSaaS){
    headers['entId'] = getApp().globalData.entId || getApp().getUserInfo().entId
  }
  // console.log('post upload header', headers)
  return new Promise((resolve, reject) => {
    wx.uploadFile({
      url: api,
      filePath,
      name,
      formData: params,
      header: {
        'x-channel': 'miniprogram',
        ...getProviderHeader(),
        ...headers
      },
      success: function(res) {
        resolve(res.data);
      },
      fail: function(err) {
        reject(new Error('上传附件失败'));
      }
    });
  }).catch(e => {
    const msg = e.errMsg ? e.errMsg : '服务器出错,请稍后再试'
    return {
      success: false,
      msg: msg
    }
  })
}

//upload image
export function uploadImg({api, filePath, name, params,}) {
  return new Promise((resolve, reject) => {
    wx.request({
      url: api,
      filePath,
      name,
      method: 'POST',
      header: {
        'content-type': params.contentType,
        'serviceChannel': 'PRI',
        'type': 'marketingApi',
        token: userStorage.getToken(),
      },
      data: params.buffer,
      success: function(res) {
        resolve(res.data);
      },
      fail: function(err) {
        reject(new Error('上传图片失败'));
      }
    });
  }).catch(e => {
    const msg = e.errMsg ? e.errMsg : '服务器出错,请稍后再试'
    return {
      success: false,
      msg: msg
    }
  })
}

export function download({url}) {
  const downloadPath = config.isProvider ? `${config.extConfig.request.initial}/file` : url
  const header = config.isProvider ? {'nb-redirect-url': url} : {}

  return new Promise((resolve, reject) => {
    wx.downloadFile({
      url: downloadPath,
      header,
      success: function(res) {
        var filePath = res.tempFilePath
        resolve(filePath)
      },
      fail: function(res) {
        reject(res)
      }
    })
  })
}

function getProviderHeader() {
  if (!config.isProvider){
    return {}
  }
  return {
    'nb-domain': config.extConfig.request.original
  }
}

// fs-upload
export function fsupload({api, filePath, name, params}) {
  return new Promise((resolve, reject) => {
    let data = {
      isPubRead: 1,
      entId: getApp().globalData.entId
    }
    let token = userStorage.getUserToken()
    let datas = fsSign(data)
    wx.uploadFile({
      url: api,
      filePath,
      name,
      formData: datas,
      header: {
        'x-channel': 'miniprogram',
        ...getProviderHeader(),
        // token,
        auth: '123456',
        'content-type': 'multipart/form-data'
      },
      success: function(res) {
        resolve(res.data);
      },
      fail: function(err) {
        reject(new Error('上传失败'));
      }
    });
  }).catch(e => {
    console.log(e)
    let msg = '服务器出错,请稍后再试'
    msg = e.errMsg ? e.errMsg : (e.message || '服务器出错,请稍后再试')
    return {
      success: false,
      msg: msg
    }
  })
}
