let loginDialogCount = 0

// 界面交互
const interaction = {
  showToast(text, duration) {
    wx.showToast({
      title: text,
      icon: 'none',
      duration: duration || 3000
    })
  },
  showLoading(text) {
    wx.showLoading({
      title: text || '加载中...',
    })
  },
  hideLoading() {
    wx.hideLoading()
  },
  showAlert(title, content, confirmCallback, cancelText = '取消', confirmText = '确定', ) {
    wx.showModal({
      title,
      content,
      cancelText,
      confirmText,
      success(res) {
        if (res.confirm) {
          confirmCallback && confirmCallback()
        }
      }
    })
  },
  showLoginDialog() {

  }
}

export default interaction
