export default {
  "tabbar": {
    "backgroundColor": "#FFFFFF",
    "color": "#CCCCCC",
    "selectedColor": '#f04b28ff',
    "list": [
      {
        "id": 3149722965773313,
        "type": 0,
        "name": '赢家home',
        "ordinal": 0,
        "pageId": 3149722965773313,
        "pageName": "赢家home",
        "pageShow": true,
        "icon": {
          "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/d1c06b8a-6994-45e6-bb6a-378cb64dedcd.png",
          "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/3d7874d6-c80f-4e98-9b68-b1ced8c37b1b.png"
        },
        "tabName": "HOME",
        "pageTabBarConf": {
          "name": "赢家home",
          "tabName": "HOME",
          "type": 0,
        },
        "path": "pages/home/<USER>",
        "status": "ENABLE"
      },
      {
        "id": 3150596094039568,
        "type": 1,
        "name": "赢家资讯",
        "ordinal": 1,
        "pageId": 3150596094039568,
        "pageName": "赢家资讯",
        "pageShow": true,
        "icon": {
          "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/e07d6ae5-d3cc-4a1a-9151-cde2819514c9.png",
          "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/ebefa580-8235-4ffd-9dee-550f9c48fe01.png"
        },
        "tabName": "news",
        "path": "pages/news/index",
        "pageTabBarConf": {
          "name": "赢家资讯",
          "tabName": "NEWS",
          "type": 1,
        },
        "status": "ENABLE"
      },
      {
        "id": 3153330889300743,
        "type": 2,
        "name": '赢家产品',
        "ordinal": 2,
        "pageId": 3153330889300743,
        "pageName": "赢家产品",
        "pageShow": true,
        "icon": {
          "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/f87feda2-84a3-4b82-8822-394f59211c1d.png",
          "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/0a92e9d3-86bc-4e00-8964-c7caae5572e8.png"
        },
        "tabName": "PRODUCT",
        "path": "pages/product/index",
        "pageTabBarConf": {
          "name": "赢家产品",
          "tabName": "PRODUCT",
          "type": 2,
        },
        "status": "ENABLE"
      },
      {
        "id": 3153330889300744,
        "type": 3,
        "name": "我的",
        "ordinal": 3,
        "pageId": 3153330889300744,
        "pageName": "我的",
        "pageShow": true,
        "icon": {
          "iconNormal": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/6e834f2d-42d5-43bc-854e-afbeeaec2e89.png",
          "iconSelector": "https://aim-pic.gffunds.com.cn/image/course/2022-04-01/00205a40-263c-46b5-804e-b809a6711054.png"
        },
        "tabName": "MINE",
        "path": "pages/mine/mine",
        "pageTabBarConf": {
          "name": "我的",
          "tabName": "MINE",
          "type": 3,
        },
        "status": "ENABLE"
      }
    ]
  },
}
