import { wbs } from "../index";
const TabBarType = {
  HOME: 0,
  NEWS: 1,
  PRODUCT: 2,
  MINE: 3,
  properties: {
    0: { path: "/pages/home/<USER>" },
    1: { path: "/pages/news/index" },
    2: { path: "/pages/product/index" },
    3: { path: "/pages/mine/mine" },
  },
};

const SHARE_IMG_DEFAULT =
  "https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2023-02-15/4a6ea2e5-c9ac-40df-9224-b2d20e313838.png";

const SHARE_BLOCK_DEFAULT = {
  title: "添富赢家",
  imageUrl: SHARE_IMG_DEFAULT,
  path: "/pages/loginAndRegist/startUp/index",
};

const DEV_PHONE = ["15354872767"];

/**
 * 标题显示区域  area   1 卡片内   2 卡片外
 * 卡片标题区   titleAreaVisible   1  展示   2  隐藏
 * @type {{"1": boolean, "2": boolean}}
 */
const CARD_SURROUND = {
  1: true,
  2: false,
};

/**
 * CONTENT filter type
 */
const CHANGE_REALM = {
  news: "ARTICLE",
  marketPlan: "MARKET_PLAN",
  multimedia: "MULTI_COURSE",
  live: "LIVE",
  fund: "FUND",
  fund_manager: "FUND_MANAGER",
};

/**
 * 渠道列表
 */
const CHANNEL_REALM = {
  marketPlan: "MARKET_PLAN",
  market_channel: "MARKET_PLAN",
  market_content: "MARKET_PLAN",
  picture_channel: "POSTER",
  poster_channel: "POSTER",
  information_channel: "ARTICLE",
  media_channel: "MULTI_COURSE",
  live_channel: "LIVE",
  product_manger_channel: "fund_manager",
  product_channel: "fund",
};

const PAGE_INFO_REALM = {
  news: "AppAdvNewsDetail",
  marketPlan: "advProductDataDetail",
  multimedia: "AppAdvInvestorTrainCom",
  live: "AppAdvLive",
  poster: "AppAdvPosterInfo",
  unscramble: "advFundScram",
  stock: "advFundScram",
  special_channel: "specialDetail",
  advSpecialTopic:'advSpecialTopic',
  reportLoading: "advReportLoading",
  // report: 'advFundScram',
  depth_report:"depthReport",//精选研报/通联研报
  meet_essence:'meetEssence',//券商研报速达/晨会精华
  depth_report_module:'depthReportModule', //专题研报/深度报告
  broker_week_finesse:'advWeeklyStrategy',//数据解读/券商周策略
  flash:'flashShare',
  pruduct:'advFundProduct',//产品详情
};

const LABEL_NAME = {
  NONE: {
    name: "无",
  },
  CLSD: {
    name: "财联社电",
    textColor: "#FFFFFF",
    bgColor: "-webkit-linear-gradient(left, #F77C7C, #F3392E)",
  },
  TFKB: {
    name: "添富快报",
    textColor: "#FFFFFF",
    bgColor: "-webkit-linear-gradient(left, #FBA075, #FF6D27)",
  },
  YDBG: {
    name: "异动点评",
    textColor: "#FFFFFF",
    bgColor: "-webkit-linear-gradient(left, #FBA075, #FF6D27)",
  },
};

const SHARE_RESPONDENT_REALM = {
  AppAdvNewsDetail: "ARTICLE",
  AppAdvInvestorTrainCom: "MULTI_COURSE",
  advProductDataDetail: "MARKETING_PLAN",
  AppAdvLive: "LIVE",
};

/**
 * 跳转页面属性
 * @type {{INTER_PAGE: number}}
 */
const PAGE_LINK_TYPE = {
  INTER_PAGE: 1, // 列表
  PRODUCT_PAGE: 2, // 产品详情、内容详情
  WEB_PAGE: 3, // 地址链接（action/http）
  POLYMERS_LIST: 4, //聚合列表
};

/**
 * 冷启动
 * 小程序场景值命中以下值时，可展示关注公众号组件：
 1011 扫描二维码
 1017 前往小程序体验版的入口页
 1025 扫描一维码
 1047 扫描小程序码
 1124 扫“一物一码”打开小程序
 */
const WECHAT_COLD_START = ["1011", "1017", "1025", "1047", "1124"];

/**
 * 直播状态
 */
// const LIVE_STATUS = { 1: "直播中", 2: "已结束", 3: "预告" , 4:"回放"};
const LIVE_STATUS = { 1: "预告", 2: "直播中", 3: "已结束" };

/**
 * 独立理财师状态
 */
const IndependentAdviserStatus = {
  APPLY: 0, //待审核
  SUCCESS: 1, //审核通过
  DISABLED: 2, //账号禁用
  ENABLED: 3, //账号启用
  DISMISSION: 4, //离职
  FAILED: 99, //审核不通过
};

const Currency = {
  1: "元",
  2: "美元",
  3: "欧元",
  4: "英镑",
  5: "港币",
  6: "瑞士法郎",
  7: "加拿大元",
  8: "澳大利亚元",
  9: "新加坡元",
  10: "新西兰元",
  11: "日元",
};

const Unit = {
  YUAN: 1, // "元"),
  WAN_YUAN: 2, // "万元"),
  WAN_CURRENCY: 3, //"万%s"),
};

const ShareRegisterSource = {
  PRODUCT: 1, //投资服务
  ARTICLE: 2, //资讯
  ACTIVITY: 3, //活动
  CARD: 4, //个人名片
  OWNER_CONTENT: 5, //转发助手
  HEADLINE: 6, //财经早报
  MICROMARKETING: 7, //营销主页
  ASSET: 8, //资产配置
  LM: 9, //事务服务
  INS: 10, //保险中心
  TRACH: 11, //投教中心
  TEACHSERIES: 13,
};

//进入小程序来源
const EnterSource = {
  APP: 1, //app分享
  WEBSITE: 2, //官网
  ENTERPRISE: 3, //企业后台
  MINI: 4, //小程序自身转发
};

//名片夹理财师类型
const RealmType = {
  ARTICLE: "ARTICLE", //资讯
  LIVE: "LIVE", //直播
  POSTER: "POSTER", //海报
  MULTI_COURSE: "MULTI_COURSE", //音视频
  MARKET_PLAN: "MARKET_PLAN", //营销方案
  UNSCRAMBLE: "UNSCRAMBLE", //基金解读
  STOCK: "STOCK", //股票
  REPORT: "REPORT", //运作报告
  MANGER: "fund_manager", //基金经理
  PRODUCT: "fund", //基金产品
  FLASH: "flash_channel", //快讯
  SPECIAL: "special_channel", //专题
  MARKETNEWS:"marketnews",//市场机会
  BROKER_RESEARCH_REPORT :'broker_research_report',//券商研报列表
  INDEX_INDICATORS:"index_indicators", //指数指标
};

//登录状态
const LoginState = {
  NO_BANDING: 30000, // 账号未绑定
  INREVIEW: 30003, //账号审核中，请稍后登录
  ABSENCE: 30035, //用户不存在，或未完善微信授权,转至登录/注册页
  ACTIVATED: 200, //已激活
  FORBIDDEN: 403, //禁用
  NOTACTIVATED: 30036, //账号未激活
  REGIST: 30002, //注册
  HASBINDUSER: 30005, //该微信已绑定过用户
  HASBINDWECHAT: 30006, //已绑定过微信，是否绑定当前微信
  CODEERROR: 30004, //验证码错误
  HASBINDPHONE: 30302, //已绑定过手机号，是否绑定当前手机号
  UNIONIDNOTPHONE: 30043, //unionid存在，手机号不存在；微信相同，手机号不同
  NOTUNIONIDPHONE: 30044, //unionid不存在，手机号存在；微信不同，手机号相同
  NOTUNIONIDNOPHONE: 30045, //unionid不存在,手机号不存在；注册
  UNIONIDPHONEDEF: 30047, //unionid存在，手机号不同
  PHONEONLY: 30048, //unionid不同，手机号唯一
  PHONENTONLY: 30049, //unionid不同，手机号不唯一
  BEREJUST: 30053, //审核不通过
  REJECTSTATUS:'D', //审核被拒绝
};

/**
 * 员工登录状态
 */
const EMPLOYEE_LOGIN_STATUS = {
  EMPLOYEE_NOT_EXIST: 30026, // 此员工不存在
  EMPLOYEE_HAS_DEACTIVATED: 30401, // 员工已停用
};

/**
 * 禁用||审核用户
 */
const BAN_ROLE = [
  30037, // 禁用
  30003, // 审核中
];

const LOGIN_OPENID_TYPE = 1;

const USER_ROLE_VISITOR = [30000, 30035, 30036, 0, 30053];

/**
 * 非用户
 * @type {number[]}
 */
const LOGIN_VISITOR = [
  30000,
  30002,
  30003, // 审核中
  30005,
  30006,
  30035,
  30036,
  403, // 禁用
  30053,//审核被拒绝
  30045,//unionId不存在,手机号不存在
];

/**
 * 页面类型
 * @type {{NORMAL_CONTENT: number, HOME: number}}
 */
const PAGE_TYPE = {
  HOME: 1,
  NORMAL_CONTENT: 2,
};

/**
 * 搜索&半屏小程序打开基本机构
 */
const SEARCH_CHANNEL_EDF = {
  information_channel: {
    label: "资讯文章",
    type: "ARTICLE",
    shortLink: "",
  },
  market_channel: {
    label: "营销资料",
    type: "MARKET_PLAN",
    shortLink: "",
  },
  special_channel: {
    label: "专题",
    type: "special_channel",
    shortLink: "",
  },
  fund_channel: {
    label: "基金产品",
    type: "fund",
    shortLink: "",
  },
  fund_manager_channel: {
    label: "基金经理",
    type: "fund_manager",
    shortLink: "",
  },
  flash_channel: {
    label: "实时快讯",
    type: "flash_channel",
    shortLink: "",
  },
  broker_research_report:{
    label: "券商研报",
    type: "broker_research_report",
    shortLink: "",
  },
  index_indicators:{
    label: "指数指标",
    type: "index_indicators",
    shortLink: "",
  }
};

/**
 * 上报线索基本机构
 */
const MARKET_CLUE_DEF = [
  {
    type: "page",
    name: "",
    id: "",
    level: 1,
  },
  {
    type: "card",
    name: "",
    id: "",
    level: 2,
  },
  {
    type: "module",
    name: "",
    id: "",
    level: 3,
  },
];

const TAB_BAR_PATH = [
  "pages/home/<USER>",
  "pages/news/index",
  "pages/product/index",
  "pages/mine/mine",
];

const LIST_PAGE_PATH = ["pages/home/<USER>/list"];

const LIST_PAGE_SEARCH = ["pages/common/search/search"];

const SENSORS_PAGE = {
  "pages/home/<USER>":"赢家HOME",
  "pages/news/index":"机会追踪",
  "pages/product/index":"赢家产品",
  "pages/mine/mine":"我的",
  "pages/loginAndRegist/login/login":"注册登录页",
  "package-activity/pages/common/search/search":'全局搜索',
  "package-activity/pages/loginAndRegist/invite/index":"邀请注册页",
  "package-activity/pages/loginAndRegist/managerInvite/managerInvite":"邀请注册—临时二维码",
  "package-activity/pages/marketnewsList/index":'市场热点追踪二级页',
  "package-aip/pages/index/index":'交易式定投',
  "package-surely/pages/approval/list/index":'注册审核页'
}
const BREAK_FUNC_NAME = {
  initWechatLogin: "initWechatLogin",
  checkLogin: "checkLogin",
  getUserInfo: "getUserInfo",
  goToLoginPage: "goToLoginPage",
  doClearSaveFiles: 'doClearSaveFiles',
  checkUserTokenState: 'checkUserTokenState'
};

//券商研报
const BROKER_REPORT = {
  0:{realm:"QUICK_MESSAGE",action:'flashShare'}, //快讯
  1:{realm:"ARTICLE",action:'AppAdvNewsDetail'}, //资讯
  2:{realm:"BROKER_WEEK_FINESSE",action:'advWeeklyStrategy'}, //数据解读/券商周策略
  3:{realm:"DEPTH_REPORT",action:'depthReport'}, //精选研报/通联研报
  4:{realm:"MEET_ESSENCE",action:'meetEssence'}, //券商研报速达/晨会精华
  5:{realm:"DEPTH_REPORT_MODULE",action:'depthReportModule'}, //专题研报/深度报告
} 

const actionType = {
  outLink:'OutLink',  //外链
  brokerResearchReport:'BrokerResearchReport', //券商研报列表
  selfBuiltList:'SelfBuiltList', //自建列表
}

//是否展示用户名片卡片
const showUserCard = {
  show:1,  //展示
  hide:0   //隐藏
}

const LIVE_STATE = {
  ORDERLIVE:6,   //预约直播
  TOLIVIE:2,  //跳转直播
}

const LIVE_STATUS_KEY = {
  START:1, //预告
  LIVE:2, //直播中
  END:3, //直播结束
}

const DETAIL_TYPE = {
  MARKET_PLAN:{
    INTER_PAGE:{
      pageType:'advProductDataDetail',
      path:`${wbs.gfH5}/share/advProductDataDetail`
    },
    OUTER_PAGE:{
      pageType:'productDataDetail',
      path:`${wbs.gfH5}/share/productDataDetail`
    }
  }
}

//h5端内端外详情
const DETAIL_URL = {
  'AppAdvMorningPaper':['/share/advMorningPaper','/share/morningPaper'],
  'AppAdvNewsDetail':['/share/advNewsDetail','/share/newsDetail'],
  'advProductDataDetail':['/share/advProductDataDetail','/share/productDataDetail'],
  'advProductDataNewDetail':['/share/advProductDataNewDetail','/share/productDataNewDetail'],
  'advFundProduct':['/share/advFundProduct','/share/advFundProduct/sharePage'],
  'advFundManager':['/share/advFundManager','/share/fundManager'],
  'advWeeklyStrategy':['/share/advWeeklyStrategy','/share/weeklyStrategy'],
  'meetEssence':['/brokerms','/brokerms'],
  'depthReportModule':['/channel_h5/depthreport/#/depthreport','/channel_h5/depthreport/#/depthreport']
}

//触达方法
const JUMP_TARGET = {
  TEMP_MSG:'TEMP_MSG',   //详情
  TEMP_LIST: 'TEMP_LIST',  //列表
  QR_LIVE_LIST: 'QR_LIVE_LIST',  //直播列表
  QR_LIVE_DETAIL: 'QR_LIVE_DETAIL', //直播详情
  MARKETNEWS_LIST: 'MARKETNEWS_LIST',  //市场机会列表
  TEMP_OUTLINK: 'TEMP_OUTLINK', //外链
  Open_Mini: 'Open_Mini',   //打开小程序
  BROKER_RESEARCH_REPORT: 'BROKER_RESEARCH_REPORT',//券商研报列表
  SELF_BUILT_LIST:"SELF_BUILT_LIST",//自建列表
  NET_WORTH_EXAMINE:"NET_WORTH_EXAMINE"//净值播报审核
}

//浮窗类型
const FLOATING_TYPE = {
  'Window': 'FLOATING_WINDOW',
  'LAYER': 'FLOATING_LAYER' 
}

//切分链接获取类型
const REAML_TYPE = {
  'advNewsDetail':'ARTICLE',
  'newsDetail': 'ARTICLE',
  'advFundProduct': 'FUND',
  'sharePage':'FUND',
  'advFundManager': 'FUND_MANAGER',
  'fundManager': 'FUND_MANAGER',
  'advSpecialTopic':'SPECIAL_TOPIC',
  'advProductDataDetail':'MARKET_PLAN',
  'productDataDetail':'MARKET_PLAN',
  'advQuarterlyReports':'FUND_QUARTERLY_REPORT',
  'allFundList':'ALL_FUND_LIST',
  'selfBuiltList':"SELF_BUILT_LIST",
}

//基金产品展示筛选范围
const SHOW_RANGE = {
  'DAY_RANGE': 'dayRange',
  'LAST_1_WRANGE': 'last1WRange',
  'LAST_1_MRANGE': 'last1MRange',
  'LAST_3_MRANGE': 'last3MRange',
  'LAST_6_MRANGE': 'last6MRange',
  'LAST_1Y_RANGE': 'last1YRange',
  'LAST_2_YRANGE': 'last2YRange',
  'LAST_3_YRANGE': 'last3YRange',
  'LAST_5_YRANGE': 'last5YRange',
  'YEAR_RANGE': 'yearRange'
}

//资讯组件类型
const ARTICLE_TYPE = {
  ARTICLE:'news',
  BROKER_WEEK_FINESSE:'broker_week_finesse',
  DEPTH_REPORT:'depth_report',
  MEET_ESSENCE:'meet_essence',
  DEPTH_REPORT_MODULE:'depth_report_module',
}

//产品组建使用页面类型
const PRODUCT_PAGE_USE_TYPE  = {
  SEARCH: 'search',
  SEARCH_ALL: 'search-all',
  NEWS_LIST: 'news-list',
  UNION_LIST: 'union-list'
}

export {
  TabBarType,
  DEV_PHONE,
  CARD_SURROUND,
  PAGE_INFO_REALM,
  PAGE_LINK_TYPE,
  CHANNEL_REALM,
  SHARE_RESPONDENT_REALM,
  LIVE_STATUS,
  Currency,
  Unit,
  ShareRegisterSource,
  EnterSource,
  IndependentAdviserStatus,
  CHANGE_REALM,
  RealmType,
  LoginState,
  SHARE_IMG_DEFAULT,
  SHARE_BLOCK_DEFAULT,
  SEARCH_CHANNEL_EDF,
  LOGIN_VISITOR,
  MARKET_CLUE_DEF,
  EMPLOYEE_LOGIN_STATUS,
  TAB_BAR_PATH,
  LIST_PAGE_PATH,
  LIST_PAGE_SEARCH,
  WECHAT_COLD_START,
  BAN_ROLE,
  LOGIN_OPENID_TYPE,
  USER_ROLE_VISITOR,
  LABEL_NAME,
  PAGE_TYPE,
  SENSORS_PAGE,
  actionType,
  showUserCard,
  LIVE_STATE,
  LIVE_STATUS_KEY,
  DETAIL_TYPE,
  DETAIL_URL,
  JUMP_TARGET,
  FLOATING_TYPE,
  REAML_TYPE,
  SHOW_RANGE,
  BROKER_REPORT,
  ARTICLE_TYPE,
  BREAK_FUNC_NAME,
  PRODUCT_PAGE_USE_TYPE
};
