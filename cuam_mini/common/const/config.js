//自定义配置信息
import config from '../../config/index.js'

const {
  env,
  plugin,
  theme,
  layout
} = config

const isProvider = env.isProvider
const extConfig = isProvider ? wx.getExtConfigSync() : {}

const colorPrimary = isProvider ? extConfig.colorPrimary : theme.colorPrimary
const ossPrefix = isProvider ? extConfig.ossPrefix : plugin.ossPrefix
const legalDomain = isProvider ? extConfig.webview : env.webview
const version = isProvider ? extConfig.version : env.version
const appId = isProvider ? extConfig.appId : env.appId

export default {
  "isProvider": isProvider,
  "isOpenSaaS": false,
  "version": version,
  "appId": appId,
  "common": {
    "ossPrefix": ossPrefix
  },
  "color": {
    "primary": colorPrimary
  },
  "tabbar": {
    "borderStyle": "white",
    "backgroundColor": "#FFFFFF",
    "color": "#CCCCCC",
    "selectedColor": colorPrimary,
    "list": layout.tabbar || [
      {
        "type": 0,
        "name": '赢家Home',
        "tabName": "HOME"
      },
      {
        "type": 1,
        "name": '赢家资讯',
        "tabName": "NEWS"
      },
      {
        "type": 2,
        "name": '赢家产品',
        "tabName": "PRODUCT"
      },
      {
        "type": 3,
        "name": "我的",
        "icon": "MINE"
      }
    ]
  },
  "background": {
    "common": {
      "footerButton": {
        "type": 0,
        "color": colorPrimary
      },
      "xsTop": {
        "type": 0,
        "color": colorPrimary
      },
      "xmTop": {
        "type": 0,
        "color": colorPrimary
      }
    },
  },
  "legalDomain": legalDomain,
  "extConfig": extConfig,
}
