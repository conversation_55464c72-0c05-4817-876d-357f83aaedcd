.share-time-box {
  display: flex;
  height: 71rpx;
  position: relative;
  margin-left: 5rpx;
}
/*竖线*/
.line {
  width: 1rpx;
  height: 26rpx;
  background-color: #f61d1d;
  position: absolute;
  left: 8rpx;
  top: 24rpx;
}
/*圆点*/
.dot {
  width: 18rpx;
  height: 18rpx;
  position: absolute;
}
.dot-1 {
  top: 4rpx;
  left: 0;
}
.dot-2 {
  top: 54rpx;
  left: 0;
}
.contentline {
  flex: 1;
  position: relative;
}
.content {
  width: 100%;
  color: #999;
  line-height: 22rpx;
  position: absolute;
}
.content text {
  color: #333;
}
.start-time {
  top: 0;
  position: absolute;
  left: 27rpx;
  margin-bottom: 26rpx;
}
.end-time {
  top: 52rpx;
  left: 27rpx;
}
