import { formatTimestamp } from "../../../common/utils/format";

Component({
  properties: {
    detail: { type: Object, value: {} },
    fontSize: { type: String, value: "24rpx" },
  },
  data: {
    startTime: "",
    endTime: "",
  },
  methods: {},
  created: function () {},
  attached: function () {
    this.setData({
      startTime: formatTimestamp(
        this.properties.detail.startTime,
        "yyyymmddhhmmss"
      ),
      endTime: formatTimestamp(
        this.properties.detail.endTime,
        "yyyymmddhhmmss"
      ),
    });
  },
  ready: function () {},
  moved: function () {},
  detached: function () {},
});
