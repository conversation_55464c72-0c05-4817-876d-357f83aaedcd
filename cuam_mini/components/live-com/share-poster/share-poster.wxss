.myCanvas {
  width: 640rpx;
  height: 980rpx;
  border-radius: 20rpx;
}

/* 分享海报 */
.overlay-box {
  width: 100%;
  height: 100%;
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
}
.overlay-poster {
  display: flex;
  width: 100%;
  height: 100%;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.overlay-tip {
  font-weight: 400;
  line-height: 31rpx;
  font-size: 32rpx;
  text-align: center;
  color: #ffffff;
  margin-bottom: 32rpx;
}
.overlay-content {
  background-color: #fff;
  box-shadow: 0 3rpx 3rpx 0 #bbbbbb;
  border-radius: 20rpx;
  width: 640rpx;
  height: 100%;
  position: absolute;
  top: 0;
}
.overlay-banner {
  height: 513rpx;
  width: 100%;
  border-radius: 20rpx 20rpx 0 0;
  display: block;
}
.overlay-title {
  margin: 32rpx 83rpx 40rpx;
  width: 473rpx;
  text-align: center;
  font-weight: 600;
  font-size: 34rpx;
  color: #333333;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 48rpx;
}
.overlay-time {
  margin: 0 117rpx 33rpx;
}
.live-qr {
  width: 100%;
  text-align: center;
  height: 250rpx;
}
.live-qr .draw_canvas {
  width: 250rpx;
  height: 250rpx;
}
.overlay-button {
  text-align: center;
  width: 267rpx;
  height: 44rpx;
  border-radius: 12rpx 12rpx 0 0;
  margin: 23rpx auto 0;
  position: relative;
}
.overlay-button .btn {
  font-size: 22rpx;
  font-weight: SC-Regular;
  color: #858585;
  border-radius: 12rpx 12rpx 0 0;
  position: absolute;
  top: 0;
  width: 100%;
  height: 44rpx;
  line-height: 44rpx;
  background: linear-gradient(
    180deg,
    rgba(245, 245, 245, 0.7),
    rgba(219, 219, 219, 1)
  );
  padding-top: 2rpx;
}
.overlay-close {
  width: 46rpx;
  height: 46rpx;
  margin-top: 40rpx;
}
.image-all {
  width: 100%;
  height: 100%;
}

/* 开始结束时间 */
.share-time-box {
  display: flex;
  height: 71rpx;
  position: relative;
  margin-left: 5rpx;
}
/*竖线*/
.line {
  width: 1rpx;
  height: 25rpx;
  background-color: #f61d1d;
  position: absolute;
  left: 5rpx;
  top: 22rpx;
}
/*圆点*/
.dot {
  width: 10rpx;
  height: 10rpx;
  border-radius: 50%;
  box-shadow: 0 0 6rpx 3rpx #fc5252;
  background-color: #f61d1d;
  position: absolute;
}
.dot-1 {
  top: 6rpx;
  left: 0;
}
.dot-2 {
  top: 54rpx;
  left: 0;
}
.contentline {
  flex: 1;
  position: relative;
}
.content {
  width: 100%;
  position: absolute;
  line-height: 22rpx;
}
.content-text {
  color: #999;
}
.content text {
  color: #333;
}
.start-time {
  top: 0;
  position: absolute;
  left: 27rpx;
  margin-bottom: 26rpx;
}
.end-time {
  top: 52rpx;
  left: 27rpx;
}
.my_canvas{
  background-color: #fff;
  border-radius: 20rpx;
}
