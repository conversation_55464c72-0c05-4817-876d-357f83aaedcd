//Component Object
import {
  qrCode,
  format,
  userStorage,
  enums
} from '../../../common/index'
const { getWXACodeImg } = qrCode;
const { formatTimestamp } = format;
const { showUserCard } = enums 
const { getUserId } = userStorage;
import PosterCard from "../pinter/card";

const PHOTO_HEIGHT = 886

Component({
  properties: {
    show: { type: Boolean, default: false },
    detail: { type: Object, default: {} },
    userId: { type: String, default: getUserId() },
    saveImg: { type: String, default: "" },
    shareParentId:{type:String,default:''},
    visitingCard:{ type: Number,value: showUserCard.show},
  },
  data: {
    qrCode: "", // 小程序码
    actionShow: false, // 底部操作
    actions: [{ name: "保存" }, { name: "发给好友" }],
    imgUrl: "", // canvas 图片
    startTime: "",
    endTime: "",
    islist: true,
    customActionStyle: {},
    width: 640,
    height: 980,
    saveImg: "",
  },
  methods: {
    //绘制海报
    doDrawCanvas() {
      wx.showLoading({
        title: '海报生成中...',
        mask:true
      })
      let params = {
        ...this.data,
        ...this.properties,
      };
      this.setData({
        paintPalette: new PosterCard({ ...params }).palette(),
      });
    },

    onImgOK(e) {
      console.log("===== PALETTE onImgOK e >>>", e);
      const { path: saveImg = "" } = e.detail || {};
      wx.hideLoading()
      this.setData({
        saveImg,
      });
    },
    // TODO(关闭海报)
    closeOverlay() {
      this.triggerEvent("close");
    },
    // 长按保存或转发
    longtap() {
      this.setData({ actionShow: true });
    },
    onClose() {
      this.setData({ actionShow: false });
    },
    onSelect(event) {
      const that = this;
      const params = {
        id:that.properties.detail.id,
        title:that.properties.detail.title
      }
      if (event.detail.name == "保存") {
        this.savePoster();
      } else if (event.detail.name == "发给好友") {
        wx.showShareImageMenu({
          //分享给朋友
          path: that.properties.saveImg,
          success: (res) => {
            that.triggerEvent("clickActivity",params)
            console.log("--分享成功：", res);
          },
          fail: (err) => {
            console.log("--分享取消：", err);
          },
        });
      }
    },

    
    savePoster() {
      const that = this;
      const params = {
        id:that.properties.detail.id,
        title:that.properties.detail.title
      }
      wx.saveImageToPhotosAlbum({
        filePath: that.properties.saveImg,
        success: function () {
          that.triggerEvent("clickActivity",params)
          wx.showToast({
            title: "保存成功",
            icon: "none",
            duration: 1500,
          });
        },
        fail(err) {
          if (
            err.errMsg === "saveImageToPhotosAlbum:fail:auth denied" ||
            err.errMsg === "saveImageToPhotosAlbum:fail auth deny" ||
            err.errMsg === "saveImageToPhotosAlbum:fail authorize no response"
          ) {
            wx.showModal({
              title: "提示",
              content: "需要您授权保存相册",
              showCancel: false,
              success: (modalSuccess) => {
                wx.openSetting({
                  success(settingdata) {
                    if (settingdata.authSetting["scope.writePhotosAlbum"]) {
                      wx.saveImageToPhotosAlbum({
                        filePath: that.properties.saveImg,
                        success: function () {
                          wx.showToast({
                            title: "保存成功",
                            icon: "success",
                            duration: 2000,
                          });
                        },
                      });
                    } else {
                      wx.showToast({
                        title: "授权失败，请稍后重新获取",
                        icon: "none",
                        duration: 1500,
                      });
                    }
                  },
                });
              },
            });
          }
        },
      });
    },

    getViewHeight() {
      const that = this;
      const query = wx.createSelectorQuery().in(this);
      query
        .select("#myview")
        .boundingClientRect(function (res) {
          console.log("ressssssss", res);
          that.setData({height:PHOTO_HEIGHT + res.height*2 })
        })
        .exec();
    },
  },

  attached: async function () {
    // 绘制小程序码
    const { detail, userId,shareParentId,visitingCard } = this.properties;
    this.getViewHeight();
    this.setData(
      {
        qrCode: await getWXACodeImg("QR_LIVE", {
          id: detail.id,
          userId,
          visitingCard,
          title: detail.title,
          shareParentId
        }),
        startTime: formatTimestamp(detail.startTime, "yyyymmddhhmmss"),
        endTime: formatTimestamp(detail.endTime, "yyyymmddhhmmss"),
        topIMGURL: detail.sharePicture || '/package-activity/pages/live/imgs/live/banner.png'
      },
      () => {
        this.doDrawCanvas();
      }
    );
  },
  ready: function () {},
  moved: function () {},
  detached: function () {},
});
