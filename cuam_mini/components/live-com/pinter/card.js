class PosterCard {
  constructor(props) {
    this.data = {...props}
  }

  palette() {
    console.log('===== PosterCard this.data >>>', this.data);
    const {
      width,
      height,
      detail,
      qrCode,
      topIMGURL
    } = this.data || {}
    console.log('detail=====',detail.url)
    return ({
      width: `${width}rpx`,
      height: `${height}rpx`,
      background: '#ffffff',
      borderRadius: '20rpx',
      views: [
        {
          id: 'topimage',
          type: 'image',
          url: `${topIMGURL}`,
          css: {
            width: `${width}rpx`,
            height: `${512}rpx`,
            borderRadius:'20rpx 20rpx 0 0',
            mode:"scaleToFill"
          },
        }, {
          id:'titletop',
          type: 'text',
          text: `${detail.posterTitle}`,
          css: {
            top: "calc(topimage.bottom + 30rpx)",
            left:'83rpx',
            color: '#333',
            fontSize: '34rpx',
            lineHeight:'48rpx',
            width:`${width - 166}rpx`,
            textAlign: 'center',
            maxLines:3,
            fontWeight:'bold'
          },
        },{
          type:'image',
          url: `data:image/png;base64,${qrCode}`,
          css:{
            top:'calc(titletop.bottom + 30rpx)',
            left:'195rpx',
            width:"250rpx",
            height:'250rpx'
          }
        },{
          type:'rect',
          css:{
            width:'267rpx',
            height:'44rpx',
            bottom:'0rpx',
            left:'187rpx',
            borderRadius:'10rpx 10rpx 0 0',
            color: "linear-gradient(0deg, rgba(245, 245, 245, 0.7) 0%, rgba(219, 219, 219, 1) 100%)",
          }
        },
        {
          type:'text',
          text:"邀您观看直播",
          css:{
            bottom:'11rpx',
            left:'255rpx',
            fontSize:'22rpx',
            color:'#858585',
          }
        },
      ]
    })
  }
}

function _textDecoration(decoration, index, sTop) {
  return ({
    type: 'text',
    id: `text_content_${index}`,
    text: `${decoration}`,
    css: {
      top: `${sTop}px`,
      color: '#333',
      fontSize: '32rpx',
      maxLines: 1,
      textAlign: 'center',
      left: `20px`
    },
  });
}

export default PosterCard
