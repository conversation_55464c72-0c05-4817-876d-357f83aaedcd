
const CountDownFormat = {
  S: 'ss',
  HMS: 'hh:mm:ss'
}
let intval = 1000

Component({
  options: {
    addGlobalClass: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    type: {
      type: String,
      value: CountDownFormat.String
    },
    timestamp: { // 只能传时间戳
      type: Number,
      value: 60000
    },
    // 业务定制参数 start
    startDate: {
      type: String,
      value: ''
    },
    duration: {
      type: String,
      value: 1 // 天为单位
    },
    // 业务定制参数 end
    color: {
      type: String,
      value: '#333'
    },
    endText: {
      type: String,
      value: '计时结束'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    result: '',
    time: 0,
    timer: 0
  },
  pageLifetimes: {
    show() {
      if (this.data.timer) {
        clearTimeout(this.data.timer)
      }
      const time = this.timeUniformity()
        this.setData({
          time: time,
          result: this.format(time)
        })
        this.init()
    },
    hide() {
      clearTimeout(this.data.timer)
    }
  },
  lifetimes: {
    ready() {
      if (this.data.timer) {
        clearTimeout(this.data.timer)
      }
      const time = this.timeUniformity()
      this.setData({
        time: time,
        result: this.format(time)
      })
      this.init()
    },
    detached() {
      clearTimeout(this.data.timer)
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    timeUniformity() {
      if (this.data.duration) {
        // ios 不支持 时间格式 为 xx-xx-xx
        const now = Date.now()
        const duration = this.data.duration * 24 * 60 * 60 * 1000
        const start = new Date(this.data.startDate.replace(/\-/g, '/')).getTime()
        const ds = duration + start
        const time = ds - now
        return time > 0 ? time : 0
      } else {
        return this.data.time
      }
    },
    // 定时启动
    init() {
      let timer = setTimeout(() => {
        let time = this.format(this.data.time)
        this.setData({
          result: time
        })
        if (this.data.time < intval) {
          clearTimeout(timer)
          this.triggerEvent('finished')
        } else {
          this.triggerEvent('counting', {
            format: time,
            time: this.data.time
          })
          this.data.time -= intval
          this.init()
        }
      }, intval)
      this.setData({
        timer: timer
      })
    },
    start() {
      this.init()
    },
    format(time) {
      const { d, h, m, s } = this.timeFormat(time)
      if (this.type === CountDownFormat.S) {} else if (CountDownFormat.HMS) {
        return `${h}:${m}:${s}`
      } else {
        return `${d}天${h}小时${m}分${s}`
      }
    },
    // 格式化时间
    timeFormat(time) {
      let minutesCount = 60 * 1000
      let hoursCount = 60 * minutesCount
      let dayCount = 24 * hoursCount
      let d = Math.floor(time / dayCount) // 天数
      let h = Math.floor((time % dayCount) / hoursCount) // 小时
      let m = Math.floor((time % hoursCount) / minutesCount ) // 分钟
      let s = Math.floor(time % minutesCount / 1000) // 秒
      h = h < 10 ? ('0' + h) : h
      m = m < 10 ? ('0' + m) : m
      s = s < 10 ? ('0' + s) : s
      return {d, h, m, s}
    }
  }
})
