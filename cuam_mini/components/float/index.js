import {util} from "../../common/index";

const {
  titleHeight,
  screenHeight,
} = getApp().appWindow;

const {rpx2px} = util

Component({
  properties: {
    contentHeight: {
      type: Number,
      value: rpx2px(screenHeight - titleHeight),
    },
    disabled: {
      type: Boolean,
      value: false
    },
  },

  attached() {
    console.log('==== float props >>>', this.properties)
  },

  methods: {
    onFloatAction(e) {
      console.log('===== e >>>>', e)
      let params = {
        disabled: !this.data.disabled
      }

      this.triggerEvent('onFloatClick', params, {bubbles: true, composed: true})
    }
  }
});
