<view class="monitor-page {{imgAnimate}}">
  <view class="monitor-top" bind:tap="onSheetCancel"/>

  <view class="monitor-bottom">
    <view class="sheet-title-bar">
      <view>
        {{title || '分享至'}}
      </view>
      <van-icon
          size="46"
          name="cross"
          color="#333"
          class="cross-icon"
          bind:tap="onSheetCancel"
      />
    </view>

    <view class="monitor-content">
      <view class="action-item">
        <button
            class='share-btn'
            open-type="share"
            style="background-color: transparent;border: none;">
          <image
              src="images/ic_wechat.png"
              mode="aspectFill"
              class="action-icon"/>
          <view>{{'微信好友'}}</view>
        </button>
      </view>

      <view class="action-item">
        <image
            src="images/ic_poster.png"
            mode="aspectFill"
            class="action-icon"
            bind:tap="onSharePosterAction"/>
        <view>{{'分享海报'}}</view>
      </view>
    </view>
  </view>
</view>
