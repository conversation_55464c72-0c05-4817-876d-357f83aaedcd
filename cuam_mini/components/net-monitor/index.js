Component({
  properties: {
    title: {
      type: String,
      value: '分享至'
    },
    path: {
      type: String,
      value: '',
    },
    currentCategory: {
      type: String,
      value: ''
    }
  },

  data: {
    imgAnimate: 'fade_null',
  },

  attached() {
    this.setData({
      imgAnimate: 'fade_in',
    })
  },

  methods: {
    onSheetCancel(e) {
      console.log('===== onSheetCancel e >>', e)
      this.setData({
        imgAnimate: 'fade_out'
      })

      this.triggerEvent('onSheetCancel', {}, {bubbles: true, composed: true})
    },

    onSharePosterAction(e) {
      console.log('====== onSheetAction e >>>', e)
      this.triggerEvent('onSheetPoster', {}, {bubbles: true, composed: true})
    },
  }
});
