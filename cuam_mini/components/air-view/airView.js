import { enums, util, jumpTarget, storage, global, wbs, userStorage, qs} from '../../common/index'
const { FLOATING_TYPE, LOGIN_VISITOR, PAGE_INFO_REALM, DETAIL_URL, LoginState } = enums
const { getQueryParams } = util
const {
  geStorageUnionId,
  getToken,
  getWechatInfoId,
  getOpenId,
  setWebPageShowWay,
  getUserId
} = userStorage
const {
  platform,
} = getApp().appWindow;
import newConfig from "../../config/index.js";
const { env, } = newConfig;
const marketListTag = [
  {id:'3286202589392429'},
  {},
  {id:"3419624104464719"},
  {id:"3419624104464719"},
]

Component({
  properties: {
    selectedFloat:{
      type:String,
      value:""
    },
    barPath:{
      type:String,
      value:''
    },
    barPic:{
      type:String,
      value:''
    },
    navPath:{
      type:String,
      value:''
    },
    navPic:{
      type:String,
      value:''
    },
    shareStaffId:{
      type:String,
      value:''
    },
    articleList:{
      type: Array,
      value:[]
    },
    selectedFloatTitle:{
      type:String,
      value:''
    }
  },
  data: {
    pageName: '',
    isIOS: platform == 'ios',
    infoBg:'/imgs/card/info-bg.png',
    aboutInfo:'/imgs/card/about-info.png',
    dotLine:'/imgs/card/dot-line.png',
  },
  methods: {
    checkUserState(obj){
      const userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);
      let hasLogin = !LOGIN_VISITOR.includes(userRole);
      if(!hasLogin && (obj.pageType == PAGE_INFO_REALM.advSpecialTopic || obj.pageType == PAGE_INFO_REALM.reportLoading)){
        return wx.reLaunch({
          url: `/pages/loginAndRegist/login/login`,
        });
      }else{
        obj.isSelectedFloat = true
        jumpTarget({name:obj.formChannel,params:obj})
      }
      
    },
    clickNav(){
      const { pageName } = this.data
      const { navPath } = this.properties
      let obj = getQueryParams(navPath)
      obj.pageName = pageName
      this.checkUserState(obj)
    },
    clickBar(){
      const { pageName } = this.data
      const { barPath } = this.properties
      let obj = getQueryParams(barPath)
      obj.pageName = pageName
      this.checkUserState(obj)
    },
    toDetail(e){
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      const { hasLogin } = this.data
      this.sensorsTrack(item.title)
      let pageType = 'AppAdvNewsDetail'
      if(item.articleType == 'OUT_LINK'){
        const ontLinkParams = {
          articleType: "OUT_LINK",
          articleId: item.id,
          realm: "ARTICLE",
          title:item.title,
        };
        ontLinkParams.title = encodeURIComponent(ontLinkParams.title);
        let url = `/pages/common/webview/webPage?url=${encodeURIComponent(item.contentLink)}&ontLinkParams=${JSON.stringify(
          ontLinkParams
        )}&cardTitle=${
          item.cardTitle || "内部投资策略"
        }&pageType=OUT_LINK`
        return wx.navigateTo({
          url,
          success(res) {
            setWebPageShowWay(1);
          },
        });
      }
      let detailUrl = DETAIL_URL[pageType]
      let pageUrl = `${wbs.gfH5 + detailUrl[0]}?value=${item.id}`
      let lastPageUrl = `${wbs.gfH5 + detailUrl[1]}?value=${item.id}`
      let sharePageUrl = `${wbs.gfH5}/marketing-api/api/v1/h5/redirect/wechat?url=${encodeURIComponent(lastPageUrl)}`;
      const allParams = {
        unionid:geStorageUnionId(),
        openid: getOpenId(),
        token: getToken(),
        wechatInfoId: getWechatInfoId(),
      }
      let url = hasLogin ? `${pageUrl}&${qs.stringify(allParams,{encode:false})}`:sharePageUrl
      setWebPageShowWay(1)
      let fullUrl = `/pages/common/webview/webPage?url=${encodeURIComponent(url)}`
      return wx.navigateTo({
        url:fullUrl,
      })
    },
    sensorsTrack(contentName){
      let title = decodeURIComponent(this.properties.selectedFloatTitle)
       // 神策埋点业务逻辑
      console.log('banner资讯===',title,contentName)
      getApp()?.sensors?.track('userClick', {
        pageType: title,
        pageContent: '资讯浮层',
        pageNum: contentName
      })
    },
    toMoreArticle(){
      this.sensorsTrack('查看更多')
      return wx.navigateTo({
        url: `/package-activity/pages/marketnewsList/index?id=${marketListTag[env.serviceTag].id}&name=市场热点追踪`,
      });
    },
  },

  created: function () {},
  attached: function () {
    let userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);
    let hasLogin = !LOGIN_VISITOR.includes(userRole * 1)
    this.setData({
      hasLogin,
    })
  },
  ready: function () {},
  moved: function () {},
  detached: function () {},
});
