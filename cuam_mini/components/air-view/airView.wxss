.tool{
  position: fixed;
  z-index: 999999;
}
.nav{
  right: 0;
  bottom: 600rpx;
  width: 120rpx;
  height: 120rpx;
}
.bar{
  left: 14rpx;
  width: 100%;
  height: 202rpx;
  bottom: 0;
  /* border-top: 1rpx solid #E2E2E2; */
  background-color: #fff;
  /* padding: 10rpx 20rpx 30rpx; */
}
.info-bar{
  position: relative;
  left: 20rpx;
  bottom: 0;
  width: 100%;
  height: 202rpx;
  /* border-top: 1rpx solid #E2E2E2; */
  background-color: #fff;
}
.bar-image{
  border-radius: 16rpx;
  width: 723rpx;
  height: 178rpx;
}
.nav-image{
  width: 100rpx;
  height: auto;
}
.about-bar-image{
  width: 68rpx;
  height: 68rpx;
  position: absolute;
  top: 32rpx;
  left: 30rpx;
}
.about-bar-ios-image{
  width: 88rpx;
  height: 88rpx;
  position: absolute;
  bottom: 120rpx;
  left: 44rpx;
}
.more-text{
  position: absolute;
  top: 108rpx;
  left: 30rpx;
  color: #8D8D8D;
  font-size: 18rpx;
}
.content-line{
  position: absolute;
  left: 157rpx;
  top: 23rpx;
  display: flex;
  flex-direction: row;
  width: 548rpx;
  white-space: nowrap; /* 设置文本不换行 */
  overflow: hidden; /* 设置溢出隐藏 */
  text-overflow: ellipsis; /* 设置显示省略号 */
}
.tag{
  height: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  padding-top: 1rpx;
  padding-bottom: 1rpx;
  padding-left: 13rpx;
  padding-right: 10rpx;
  border:2rpx solid #FED3D0;
  background-color: #FFF0F0;
  border-radius: 4rpx;
  color:#FF1F1F;
  font-size: 22rpx;
  margin-right: 6rpx;
}
.content-text{
  display: flex;
  flex: 1;
  margin-left: 4rpx;
  font-size: 28rpx;
  color: #222222;
  font-weight: 500;
  white-space: nowrap; /* 设置文本不换行 */
  overflow: hidden; /* 设置溢出隐藏 */
  text-overflow: ellipsis; /* 设置显示省略号 */
}
.dot{
  width: 18rpx;
  height: 113rpx;
  position: absolute;
  top: 34rpx;
  left: 125rpx;
}