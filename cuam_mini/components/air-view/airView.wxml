<view>
  <cover-view class="tool nav" wx:if="{{navPic}}" bindtap="clickNav">
    <cover-image src='{{navPic}}' mode="aspectFill" class="nav-image"/>
  </cover-view>
  <cover-view class="tool bar" wx:if="{{barPic}}" bindtap="clickBar" style="height:{{isIOS?202:260}}rpx">
    <cover-image src='{{barPic}}' class='bar-image' style="height:{{isIOS?178:240}}rpx" />
  </cover-view>
  <cover-view class="tool bar" wx:if="{{articleList.length}}" style="height:{{201}}rpx">
    <cover-image src='{{infoBg}}' class='bar-image' style="height:{{148}}rpx" />
    <cover-image src="{{dotLine}}" class='dot'/>
    <cover-image src='{{aboutInfo}}' bindtap="toMoreArticle" class='{{"about-bar-image"}}'/>
    <cover-view class="more-text" bindtap="toMoreArticle">查看更多</cover-view>
    <cover-view class="content-line" wx:if="{{articleList[0].id}}" catch:tap="toDetail" data-item="{{articleList[0]}}">
      <cover-view wx:if="{{articleList[0].topicList.length}}" wx:for="{{articleList[0].topicList}}" wx:for-item="item">
        <cover-view class="tag">{{item.topicName}}</cover-view>
      </cover-view>
      <cover-view class="content-text">{{articleList[0].title}}</cover-view>
    </cover-view>
    <cover-view class="content-line" style="top:89rpx;"  wx:if="{{articleList[1].id}}" catch:tap="toDetail" data-item="{{articleList[1]}}">
      <cover-view wx:if="{{articleList[1].topicList.length}}" wx:for="{{articleList[1].topicList}}" wx:for-item="item">
        <cover-view class="tag">{{item.topicName}}</cover-view>
      </cover-view>
      <cover-view class="content-text">{{articleList[1].title}}</cover-view>
    </cover-view>
  </cover-view>
</view>
