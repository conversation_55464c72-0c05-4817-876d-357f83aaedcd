// components/nb-page/nb-home/nb-home.js
Component({
  options: {
    addGlobalClass: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    btnText: String,
    showIcon: Boolean,
    status: {
      type: Boolean,
      value: true
    },
    icon: String,
    text: String
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleIcon() {
      this.triggerEvent("clickIcon")
    },
    handleBtn() {
      this.triggerEvent("clickBtn")
    }
  }
})