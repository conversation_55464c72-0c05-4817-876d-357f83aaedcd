// components/nb-page/nb-footer/nb-footer.js
Component({
  options: {
    addGlobalClass: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    btnText: String,
    showIcon: Boolean,
    status: {
      type: Boolean,
      value: true
    },
    icon: {
      type: String,
      value: 'phone-o'
    },
    text:String
  },

  /**
   * 组件的初始数据
   */
  data: {

  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleIcon() {
      this.triggerEvent("clickIcon")
    },
    handleBtn() {
      if(this.data.status){
        this.triggerEvent("clickBtn")
      }
    }
  }
})
