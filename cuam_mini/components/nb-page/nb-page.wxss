/* component/nb-page/nb-page.wxss */
.container{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    position: relative;
}

.header-block-img{
    width: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 0;
}

.header-block{
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    z-index: 99999;
    overflow: hidden;
    background-color: transparent;
}

.logo-icon{
    max-height: 68px;
    margin-left: 20rpx;
}

.authorization-pop{
    top: 45%;
    border-radius: 12rpx;
    width: 510rpx;
    height: 616rpx;
    background-color: transparent;
}

.modal-footer{
    display: flex;
    height: 100rpx;
    flex-direction: row;
    align-items: center;
    font-size: 32rpx;
}

.modal-btn{
    width: 50%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
}

.authorization-background{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-items: center;
}

.authorization-cover{
    width: 100%;
    height: 100%;
    position: absolute;
}

.authorization-title{
    color: #fff;
    font-size: 30rpx;
    font-weight: 500;
    text-align: center;
    width: 100%;
    margin-top: 80rpx;
}

.authorization-image-text{
    width: 100%;
    margin-top: 100rpx;
}

.authorization-btn{
    width: 400rpx;
    height: 74rpx;
    background: rgba(255, 255, 255, 1);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.05);
    border-radius: 8rpx;
    font-size: 28rpx;
    line-height: 74rpx;
    font-weight: 500;
    position: absolute;
    bottom: 94rpx;
    left: 56rpx;
    right: 56rpx;
}

.authorization-cancel{
    color: #fff;
    position: absolute;
    bottom: 30rpx;
    left: 207rpx;
    right: 207rpx;
    font-size: 24rpx;
}

.share-bottom{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 86vw;
    height: 40px;
    border-radius: 35px;
    margin-top: -20rpx;
    font-size: 14Px;
    color: #fff;
}

.page-share-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}
