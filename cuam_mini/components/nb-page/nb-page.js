// component/nb-page/nb-page.js
// 页面通用组件，可将全局UI弹窗放置于此，并且通过store进行全局控制
import {
  regeneratorRuntime,
  eventName
} from '../../common/index.js'

// import {
//   titleHeight,
//   statusBarHeight,
//   footHeight,
//   screenHeight,
//   getContentHeight,
//   shareBottomSize,
//   navBottomFloat
// } from '../../common/const/systeminfo.js'

import {
  TabBarType
} from '../../common/const/enum.js'

const { SET_LISTENER_TAB_BAR } = eventName

// const app = getApp()

const {
  titleHeight,
  footHeight,
  getContentHeight,
  shareBottomSize,
  navBottomFloat
} = getApp().appWindow;

const {
  properties
} = TabBarType || {}

Component({
  options: {
    addGlobalClass: true,
  },

  /**
   * 组件的属性列表
   */
  properties: {
    // container背景色
    containerBgColor: {
      type: String,
      value: '#f7f7f7' // 默认 #f7f7f7
    },
    // 可传入改变navbar样式
    navBgStyle: {
      type: String,
      // value: getApp().store.$state.config.color.primary
      value: '#fff'
    },
    // 可传入改变navbar title样式
    navTitleStyle: {
      type: String,
      value: 'color: black;'
    },
    navTitle: {
      type: String,
      value: ''
    },
    // 可传入改变nav back页面数
    navDelta: {
      type: Number,
      value: 1
    },
    // 决定是否显示loading
    navShowLoading: {
      type: Boolean,
      value: false
    },
    navTextStyle: {
      type: String,
      value: 'black'
    },
    // 是否显示返回按钮
    navShowBack: {
      type: Boolean,
      value: true
    },
    inHomePage: {
      type: Boolean,
      value: false
    },
    showTabBar: {
      type: Boolean,
      value: false
    },
    showNavBar: {
      type: Boolean,
      value: true
    },
    contentUseView: {
      type: Boolean,
      value: false
    },
    scrollEnable: {
      type: Boolean,
      value: true
    },
    tabBarType: {
      type: Number,
      value: 0
    },
    showLeftCustomIcon: {
      type: Boolean,
      value: false
    },
    customLeftIconUrl: {
      type: String,
      value: ''
    },
    showFloatChat: {
      type: Boolean,
      value: false
    },
    showPageShareBar: {
      type: Boolean,
      value: false
    },
    showHomeIcon: {
      type: Boolean,
      value: false
    },
    shareTxt: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    titleHeight,
    footHeight,
    shareBottomSize,
    authorizationBackground: {
      // type:2,
      // img:'/common/authorization.png',
      type: 0,
      // color: getApp().store.$state.config.color.primary
      color: '#fff'
    },
    navBottomFloat,
  },

  attached() {
    console.log('======= NB-PAGE props ##### >>>>', this.properties)
    const {
      showTabBar,
      showNavBar,
      inHomePage,
      showPageShareBar
    } = this.data
    let contentHeight = getContentHeight(showNavBar, showTabBar, showPageShareBar)
    this.setData({
      contentHeight,
      inHomePage
    })
  },
  pageLifetimes: {
    show() {
      // 页面被展示
    },
    hide() {
      // 页面被隐藏
    },
    resize() {
      // 页面尺寸变化
    }
  },

  /**
   * 组件的方法列表
   */
  methods: {
    onTabChange(e) {
      console.log('====== onTabChange e >>>>', e)
      const {
        tabbar: {
          type = 0,
          tabName = '',
        }
      } = e.detail || {}
      let _path = '/pages/home/<USER>'
      if (tabName) {
        _path = properties[type * 1].path;
      }
      console.log('====== path >>>', _path)
      getApp().event.emit(SET_LISTENER_TAB_BAR, type)
      wx.switchTab({
        url: `${_path}`
      })
    },
    onPressNavLeft() {
      this.triggerEvent('onPressNavLeft', {})
    },
    onPageShare() {
      // console.log('==== onPageShare e >>>', e)
      let page = getCurrentPages().pop()
      this.triggerEvent('onPageShare', {page}, {bubbles: true, composed: true})
    }
  }
})
