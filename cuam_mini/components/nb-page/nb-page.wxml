<!--component/nb-page/nb-page.wxml-->
<view class='container' style="{{containerBgColor && 'background-color:' + containerBgColor+';'}}">
  <privacy/>
  <!-- 标题栏 -->
  <navigation
      wx:if="{{showNavBar}}"
      show-loading="{{navShowLoading}}"
      title="{{navTitle}}"
      title-style="{{navTitleStyle}}"
      bg-style="{{navBgStyle}}"
      delta="{{navDelta}}"
      text-style='{{navTextStyle}}'
      show-back="{{navShowBack}}"
      showLeftCustomIcon="{{showLeftCustomIcon}}"
      customLeftIconUrl="{{customLeftIconUrl}}"
      showHomeIcon="{{showHomeIcon}}"
      bind:onPressNavLeft="onPressNavLeft"
  />

  <!--logo-->
  <cover-view wx:if="{{inHomePage}}" class="header-block" 
    style="height: {{titleHeight}}rpx;">
    <cover-image
        src="../../imgs/empty/<EMAIL>"
        mode="heightFix"
        class="logo-icon"
        style="height:30px;width:110px;margin-bottom: {{navBottomFloat}}px"
        />
  </cover-view>
   <!--logo-->
  <!-- <view
     wx:if="{{inHomePage}}" 
     class="header-block" 
     style="height: {{titleHeight}}rpx;">
    <image
        mode="heightFix"
        src="../../imgs/empty/<EMAIL>"
        style="height:30px;margin-bottom: {{navBottomFloat}}px"
        class="logo-icon"/>
  </view> -->

  <!-- 内容区域 -->
  <scroll-view
      scroll-y="{{scrollEnable}}"
      style='height:{{contentHeight}}rpx;margin-top:{{showNavBar?titleHeight:0}}rpx; margin-bottom:{{showTabBar?footHeight:0}}rpx;'
      wx:if="{{!contentUseView}}"
  >
    <slot></slot>
  </scroll-view>

  <!-- 包含canvas等原生组件的内容区域 -->
  <view
      style='height:{{contentHeight}}rpx;margin-top:{{showNavBar?titleHeight:0}}rpx; padding-bottom:{{showTabBar?footHeight:0}}rpx;'
      wx:if="{{contentUseView}}">
    <slot></slot>
  </view>

  <!-- 底部TabBar -->
  <tabbar
      wx:if="{{showTabBar}}"
      currentSelectedType="{{tabBarType}}"
      bind:tabChange="onTabChange"
  />

  <view
      wx:if="{{showPageShareBar}}"
      class="page-share-block"
      style="height: {{shareBottomSize}}rpx;width:100%;background-color: #fff;position: absolute;left: 0;bottom: 0;margin-bottom: {{-shareBottomSize}}rpx;"
      bind:tap="onPageShare">
    <button open-type="share" class="share-bottom" style="background-color: {{$state.themeColor}}">
      {{shareTxt || ""}}
    </button>
  </view>
</view>
