// const cellBehavior = require('../cell-behavior.js')
import cellBehavior from '../cell-behavior.js'

Component({
  behaviors: [cellBehavior],
  properties: {
    indicator: {
      type: String,
      value: '',
    }
  },
  data: {
    value: ''
  },
  methods: {
    handleBindinput(e) {
      // console.log(e.detail)
      this.setData({
        value: e.detail.value
      })
      const { item } = this.data
      this.triggerEvent('valuechanged', { item, value: e.detail.value }, { bubbles: true, composed: true })
    }
  }
})
