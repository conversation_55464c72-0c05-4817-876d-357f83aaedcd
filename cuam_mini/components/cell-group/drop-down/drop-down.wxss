.container {
  background-color: #fff;
  display: flex;
  flex-direction: column;
  padding: 24rpx 0 22rpx 0;
}
.title-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.content {
  padding-top: 24rpx;
  color: #969696;
}

.font13 {
  font-size: 26rpx;
}
.right-arrow {
  width: 16rpx;
  height: 16rpx;
}
.open {
  transform: rotate(90deg);
}
.close {
  transform: rotate(0);
}