<view class='container' style='border-bottom:{{showDivide ? "1px solid #eeeeee;" : "0rpx solid #eeeeee;"}}'>
  <view class='title'>{{item.name}}</view>
  <textarea
    class='textarea'
    placeholder='{{item.placeholder || "请输入备注信息"}}'
    placeholder-style='{{item.placeholderStyle || "color:#CCCCCC;"}}'
    maxlength='{{item.maxlength || 20}}'
    bindinput='handleBindInput'
  />
  <view class='word-num'>
    <view class='current' style='color: {{$state.themeColor}}'>{{currentLength}}</view>
    <view class='total'>/{{item.maxlength || 20}}</view>
  </view>
</view>
