// const cellBehavior = require('../cell-behavior.js')
import cellBehavior from '../cell-behavior.js'

Component({
  behaviors: [cellBehavior],
  properties: {
    
  },
  data: {
    currentLength: 0
  },
  methods: {
    handleBindInput(e) {
      const { item } = this.data
      this.setData({currentLength: e.detail.value.length})
      this.triggerEvent('valuechanged', { item, value: e.detail.value }, { bubbles: true, composed: true })
    }
  }
})