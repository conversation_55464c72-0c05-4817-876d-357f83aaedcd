<view class='container'>
  <block wx:for='{{data}}' wx:key='index'>
    <!-- info -->
    <block wx:if='{{item.type == "info"}}'>
      <info item='{{item}}' showDivide='{{item.showDivide != null ? item.showDivide : index == data.length -1 ? false : true}}' />
    </block>
     <block wx:elif='{{item.type == "info-full"}}'>
      <info-full item='{{item}}' showDivide='{{item.showDivide != null ? item.showDivide : index == data.length -1 ? false : true}}' />
    </block>
    <!-- setting -->
    <block wx:elif='{{item.type == "setting"}}'>
      <setting 
        item='{{item}}' 
        showDivide='{{item.showDivide != null ? item.showDivide : index == data.length -1 ? false : true}}' 
        hideRightArrow='{{item.hideRightArrow}}'
        styles='{{styles}}'
      />
    </block>
    <!-- smallInfo -->
    <block wx:elif='{{item.type == "smallInfo"}}'>
      <small-info item='{{item}}' />
    </block>
    <!-- dropDown -->
    <block wx:elif='{{item.type == "dropDown"}}'>
      <drop-down item='{{item}}' showDivide='{{item.showDivide != null ? item.showDivide : index == data.length -1 ? false : true}}'  />
    </block>
    <!-- memo -->
    <block wx:elif='{{item.type == "memo"}}'>
      <memo item='{{item}}' showDivide='{{item.showDivide != null ? item.showDivide : index == data.length -1 ? false : true}}' />
    </block>
    <!-- input -->
    <block wx:elif='{{item.type == "input"}}'>
      <nb-input item='{{item}}' showDivide='{{item.showDivide != null ? item.showDivide : index == data.length -1 ? false : true}}' />
    </block>
    <!-- selector -->
    <block wx:elif='{{item.type == "selector"}}'>
      <selector item='{{item}}' showDivide='{{item.showDivide != null ? item.showDivide : index == data.length -1 ? false : true}}' />
    </block>
    <!-- text-area -->
    <block wx:elif='{{item.type == "textArea"}}'>
      <text-area item='{{item}}' showDivide='{{item.showDivide != null ? item.showDivide : index == data.length -1 ? false : true}}' />
    </block>
  </block>
</view>