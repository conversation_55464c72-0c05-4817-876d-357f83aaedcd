.container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 26rpx 0 26rpx 0;
}
.showDivide {
  position: relative;
}
.showDivide:after {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1rpx;
  width: 100%;
  background: #eee;
  content: '';
  transform: scaleY(.5);
}
.reDraw .key {
  font-size: 28rpx;
  color: #333;
}
.reDraw .value {
  font-size: 28rpx;
}
.key {
  font-size: 26rpx;
  color: #969696;
}
.value {
  flex: 1;
  margin-left: 86rpx;
  text-align: right;
  font-size: 26rpx;
  color: #333333;
  line-height: 24rpx;
}