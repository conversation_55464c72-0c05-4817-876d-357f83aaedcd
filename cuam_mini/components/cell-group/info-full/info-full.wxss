/* components/cell-group/info-full/info-full.wxss */
.ifof-container {
  /* display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center; */
  padding: 26rpx 0 26rpx 0;
}
.ifof-showDivide {
  position: relative;
}
.ifof-showDivide:after {
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 1rpx;
  width: 100%;
  background: #eee;
  content: '';
  transform: scaleY(.5);
}
.ifof-reDraw .ifof-key {
  font-size: 28rpx;
  color: #333;
}
.ifof-reDraw .ifof-value {
  font-size: 28rpx;
}
.ifof-key {
  font-size: 26rpx;
  color: #969696;
  padding-bottom: 10rpx;
}
.ifof-value-wrap {
  flex: 1;
  text-align: right;
}
.ifof-value {
  font-size: 26rpx;
  color: #333333;
  line-height: 24rpx;
}