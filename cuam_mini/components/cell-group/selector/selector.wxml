<!-- 地区 -->
<block wx:if='{{item.mode == "region"}}'>
  <van-picker mode='region' bindchange='pickerValueChanged' value='{{region}}'>
    <view class='container' style='border-bottom:{{showDivide ? "1px solid #eeeeee;" : "0rpx solid #eeeeee;"}}'>
      <view class='key'>{{item.name}}</view>
      <block wx:if='{{region[0] == "" && region[1] == "" && region[2] == ""}}'>
        <view class='value'>{{item.value}}</view>
      </block>
      <block wx:else>
        <view class='value'>{{region[0]}} {{region[1]}} {{region[2]}}</view>
      </block>
    </view>
  </van-picker>
</block>
<!-- 日期 -->
<block wx:elif='{{item.mode == "date"}}'>
  <van-picker mode='date' bindchange='pickerValueChanged' value='{{value}}'>
    <view class='container' style='border-bottom:{{showDivide ? "1px solid #eeeeee;" : "0rpx solid #eeeeee;"}}'>
      <view class='key'>{{item.name}}</view>
      <view class='value'>{{value || item.value}}</view>
    </view>
  </van-picker>
</block>
<!-- 其他 -->
<block wx:else>
  <template is='cell' data='{{...item}}' />
</block>

<template name='cell'>
  <view class='container' bindtap='clickItem' style='border-bottom:{{showDivide ? "1px solid #eeeeee;" : "0rpx solid #eeeeee;"}}'>
    <view class='key'>{{name}}</view>
    <view class='value'>{{value}}</view>
  </view>
</template>