// const cellBehavior = require('../cell-behavior.js')
import cellBehavior from '../cell-behavior.js'

Component({
  behaviors: [cellBehavior],
  properties: {
    
  },
  data: {
    value: null,
    region: ['', '', ''],
  },
  methods: {
    pickerValueChanged(e) {
      // console.log(e.detail)
      const { item } = this.data
      if (item.mode == 'region') {
        this.setData({
          region: e.detail.value
        })
        this.triggerEvent('valuechanged', { item, value: e.detail.code[2] }, { bubbles: true, composed: true })
      } else {
        this.setData({
          value: e.detail.value
        })
        this.triggerEvent('valuechanged', { item, value: e.detail.value }, { bubbles: true, composed: true })
      }
      
    },
    clickItem() {

    }
  }
})