// components/nb-card/nb-card.js
// nb-card组件默认存在左右边距14px，非card模式无左右边距
Component({
  options: {
    addGlobalClass: true,
  },
  externalClasses: ['card-class', 'card-item-class'],
  /**
   * 组件的属性列表
   */
  properties: {
    isCard: {
      type: Boolean,
      value: true
    },
    data: {
      type: Array,
      value: []
    },
    style: {
      type: Object,
      value: {}
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {}
})
