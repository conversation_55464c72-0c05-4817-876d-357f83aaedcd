// const cellBehavior = require('../cell-behavior.js')
import cellBehavior from '../cell-behavior.js'

Component({
  behaviors: [cellBehavior],
  properties: {
    styles: {
      type: String,
      value: ''
    },
    hideRightArrow: {
      type: Boolean,
      value: false
    }
  },
  data: {

  },
  methods: {
    itemClick() {
      const { item } = this.data
      this.triggerEvent('settingclick', { item }, { bubbles: true, composed: true })
    }
  }
})