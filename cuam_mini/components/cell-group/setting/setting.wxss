.container {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding: 26rpx 0 28rpx 0;
}
.left {
  font-size: 32rpx;
  color: #333333;
  font-weight: bold;
}
.right {
  font-size: 26rpx;
  color: #969696;
}
.showDivide {
  border-bottom: 2rpx solid #eeeeee;
}
.right-arrow {
  width: 16rpx;
  height: 16rpx;
}
.value {
  flex: 1;
  margin-left: 86rpx;
  margin-right: 18rpx;
  text-align: right;
  font-size: 26rpx;
  color: #333333;
}