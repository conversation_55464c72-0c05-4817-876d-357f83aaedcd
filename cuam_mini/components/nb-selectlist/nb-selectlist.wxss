.modal {
  position: fixed;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background: rgba(0, 0, 0, 0.3);
}

.flex {
  flex: 1;
}

.center {
  max-height: 50vh;
  min-height: 50vh;
}

.item {
  display: flex;
  box-sizing: border-box;
  flex-direction: column;
  width: 100%;
}

.titletext {
  margin: 0 0 10rpx 0;
  font-family: PingFangSC-Regular;
  font-weight: 400;
  font-size: 32rpx;
  color: #000000;
  letter-spacing: 0;
  line-height: 36rpx;
  margin-bottom: 28rpx;
}

.datas {
  display: flex;
  flex-direction: row;
  box-sizing: border-box;
  align-content: flex-start;
  flex-wrap: wrap;
}

.data {
  border: 1rpx solid rgba(0, 0, 0, 0.2);
  padding: 3rpx 20rpx 3rpx 20rpx;
  margin: 0 0rpx 28rpx 9rpx;
  min-width: 200rpx;
  height: 68rpx;
  align-items: center;
  justify-content: center;
  display: flex;
  border-radius: 16rpx;
}

.select {
  border: 2rpx solid #FFAB3E;
  background: #fff;
}

.unselect {
  background: #EEEEEE;
  border: 2rpx solid #EEEEEE;
}

.itemselect {
  background-color: aqua;

}

.itemunselect {
  background-color: beige;

}

.text {
  font-size: 30rpx;
  line-height: 30rpx;
  text-align: center;
  padding: 30rpx;
  width: 100%;
  border-bottom: 0.5rpx solid #EAEAEA;
}

.content {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  display: flex;
  max-height: 70%;
  align-items: center;
  flex-direction: column;
  margin-bottom: 20%;
}

.contentcard {
  width: 100%;
  box-sizing: border-box;
  background-color: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  display: flex;
  max-height: 70%;
  align-items: center;
  flex-direction: column;
}

.title {
  font-size: 36rpx;
  margin: 40rpx;
  color: darkgray;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #000000;
  letter-spacing: 0;
  text-align: center;
  line-height: 36rpx;
}

.line {
  height: 2rpx;
  width: 100%;
  opacity: 0.3;
  background: #EEEEEE;
  margin-bottom: 28rpx;
}

.bottom {
  width: 100%;
  background-color: #fff;
  display: flex;
  box-sizing: border-box;
  padding-top: 30rpx;
  margin-bottom: 80rpx;
  align-items: center;
  justify-content: center;
  border-top: 6rpx solid #EAEAEA;
}

.btnleft {
  border: 2rpx solid #FFC200;
  border-radius: 60rpx;
  align-items: center;
  justify-content: center;
  display: flex;
  box-sizing: border-box;
  flex: 1;
  margin-left: 0rpx;
  margin-right: 15rpx;
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}

.btnright {
  align-items: center;
  border-radius: 60rpx;
  justify-content: center;
  display: flex;
  box-sizing: border-box;
  flex: 1;
  margin-left: 15rpx;
  background: #FFC200;
  padding-top: 10rpx;
  padding-bottom: 10rpx;
}

.btnlefttext {
  font-family: PingFangSC-Medium;
  font-weight: 500;
  font-size: 32rpx;
  color: #FFC200;
  letter-spacing: 0;
  text-align: center;
}

.text {
  font-weight: 400;
  font-size: 28rpx;
  text-align: center;
  color: #333333 100%;

}

.btnrighttext {
  font-weight: 400;

  font-size: 28rpx;
  text-align: center;
  color: #333333 100%;

}