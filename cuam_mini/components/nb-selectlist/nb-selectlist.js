import {
  fundContext,
} from "../../common/nb/home";
Component({
  properties: {
    showModal: {
      type: Boolean,
      value: true
    },
    fromCard:{
      type: Boolean,
      value: false
    },
    selectList: {
      type: Array,
      value: []
    }
  },
  data: {
    data: [],
    animationData: {},
  },

  /**
   * 组件的方法列表
   */
  attached() {

  },

  methods: {
    // 点击选项
    chooseItem: function (e) {
      this.triggerEvent("chooseItem", {...e.currentTarget.dataset});
    },

    sureBtn: function () {
      if (this.properties.showModal) {
        this.showModal()
      } else {
        this.hideModal()
      }
    },


    // 显示遮罩层
    showModal: function () {
      var _this = this;
      this.triggerEvent("isShowModal", { data: '' });
      // 创建动画实例
      var animation = wx.createAnimation({
        duration: 400,//动画的持续时间
        timingFunction: 'ease',//动画的效果 默认值是linear->匀速，ease->动画以低速开始，然后加快，在结束前变慢
      })
      this.animation = animation; //将animation变量赋值给当前动画
      var time1 = setTimeout(function () {
        _this.slideIn();//调用动画--滑入
        clearTimeout(time1);
        time1 = null;
      }, 100)
    },

    close() {
      this.triggerEvent("isShowModal", {});
    },

    // 隐藏遮罩层
    hideModal: function () {
      var that = this;
      var animation = wx.createAnimation({
        duration: 400,//动画的持续时间 默认400ms
        timingFunction: 'ease',//动画的效果 默认值是linear
      })
      this.animation = animation
      that.slideDown();//调用动画--滑出
      var time1 = setTimeout(function () {
        this.triggerEvent("isShowModal", { data: that.data.data });
        clearTimeout(time1);
        time1 = null;
      }, 220)//先执行下滑动画，再隐藏模块

    },
    //动画 -- 滑入
    slideIn: function () {
      this.animation.translateY(0).step() // 在y轴偏移，然后用step()完成一个动画
      this.setData({
        //动画实例的export方法导出动画数据传递给组件的animation属性
        animationData: this.animation.export()
      })
    },
    //动画 -- 滑出
    slideDown: function () {
      this.animation.translateY(300).step()
      this.setData({
        animationData: this.animation.export(),
      })
    },
  }
})
