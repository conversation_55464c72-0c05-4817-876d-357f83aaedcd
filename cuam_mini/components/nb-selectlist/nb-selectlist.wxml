<view class="modal" wx:if="{{showModal}}">
  <view bindtap="close" class="flex"></view>
  <view class="{{fromCard?'contentcard':'content'}}" animation="{{animationData}}">
    <view class="item" wx:for="{{selectList}}" wx:key="name" wx:for-item="item" wx:for-index="index">
      <text bindtap="chooseItem" data-item="{{item}}" class="text">{{item.name+'涨跌幅'}}</text>
    </view>
    <view class="bottom" bindtap="sureBtn">
      <text class="btnrighttext">取消</text>
    </view>
  </view>
</view>