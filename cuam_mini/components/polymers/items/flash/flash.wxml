<view>
  <view class="card"
        style="border-top-left-radius:{{index===0?16:0}}rpx; border-top-right-radius:{{index==0?16:0}}rpx; border-bottom-right-radius:{{isLast?16:0}}rpx; border-bottom-left-radius:{{isLast?16:0}}rpx;">
    <view
        class="card-box"
        data-item="{{data}}"
        style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{isLast || !showBorder?'#fff':'#eaeaea'}}">

      <view class="card-left" catchtap="onShowMore">
        <view class="card-flash-title {{!isShowMore?'text-three-line':''}}"><text wx:if="{{data.labelName&&data.labelName != '无'}}" class="copyright-txt" style='color:{{data.labelColor}};background:{{data.labelBGColor}}'>{{data.labelName}}</text><text class="{{(data.labelName && data.labelName !='无') ? 'card-news-title ' : 'no-padding card-news-title'}}" decode="{{true}}">{{data.title}}&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</text><text>{{data.quickMessageContent || data.content ||'-'}}</text></view>
      </view>
      
      <view class="card-row-box">
            <view wx:key="idx"
              wx:for-index="idx"
              wx:for-item="item"
              wx:if="{{data.quickMessageTopicList.length >0}}"
              wx:for="{{data.quickMessageTopicList}}">
              <view class="card-bottom-title text-two-line">
                <text class='topicName' data-item="{{item}}" style="color:{{item.status === 'PUBLISHED'?'#F04E41':'#999999'}}" catchtap="{{item.status === 'PUBLISHED'?'toSpecial':''}}">{{item.status === "PUBLISHED"?'#':'//'}}{{item.topicName}}</text>
                <text class='publish-txt' data-item="{{item}}"  wx:if="{{item.status === 'PUBLISHED'}}" catchtap="{{'toList'}}">相关产品推荐</text>
              </view>
            </view>
            <view></view>

            <view class='card-show-box'>
              <image src="{{isShowMore?'../../../../imgs/close_arrow.png':'../../../../imgs/open_arrow.png'}}" class='open-arrow'/>
              <view class='card-show-txt' bind:tap="onShowMore">{{isShowMore?'收起':'展开'}}</view>
              <view class='line-view'></view>
              <view data-item="{{data}}" catchtap="{{'toSpecial'}}">
                <image src="../../../../imgs/home_share.png" class='share-img'/>            
              </view>
            </view>
        </view>
    </view>
  </view>
</view>
