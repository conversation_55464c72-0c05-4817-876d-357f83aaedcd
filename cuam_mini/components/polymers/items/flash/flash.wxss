@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.card{
    background-color: #fff;
}
.card-box{
    display: flex;
    flex-direction: column;
    margin: 14px 14px 0 14px;
    padding-bottom: 14px;
    /* align-items: center; */
    border-bottom: 1rpx solid #eaeaea;
}

.card-left{
    display: flex;
    flex: 1;
    align-items: flex-start;
    /* min-height: 140rpx; */
    /*height: 140rpx;*/
    /* padding: 0 28rpx; */
}

.text-two-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}
.text-three-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-subtitle-txt{
    font-size: 24rpx;
    color: #999;
    font-weight: 400;
    margin-top: 6rpx;
    /* font-family: PingFang-Bold; */
}

.card-left-title{
    font-size: 16px;
    /*line-height: 22px;*/
    color: #333;
    font-weight: 500;
    /* font-family: PingFang-Bold; */
    letter-spacing: 0;
    line-height: 20px;
    position: relative;
}

.card-left-time{
    font-size: 12px;
    font-weight: 400;
    color: #969696;
    /* display: flex;
    flex-direction: row;
    align-items: center; */
    /*line-height: 16px;*/
}

.image{
    width: 100px;
    height: 75px;
    border-radius: 6px;
    margin-left: 20px;
}

.card-news-bottom{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    flex: 1
}
.icon-time{
    width: 14px;
    height: 14px;
    padding: 0;
    margin: 0;
    margin-right: 12rpx;
}

.fade_in {
    animation: fadeIn 1.2s both;
}


.card-bottom-title{
    font-size: 24rpx;
    color:#F04E41;
    font-weight: 400;
    line-height: 32rpx;
}
.copyright-txt{
    font-size: 18rpx;
    padding: 2rpx 6rpx;
    /* margin-right: 5px; */
    border-radius: 8rpx 0 8rpx 0;
    vertical-align: middle;
    position: absolute;
    left: 0;
    top: 6rpx;
    /* height: 27rpx;
    width: 88rpx; */
    text-align: center;
    line-height: 27rpx;
  }
 .topicName{
    font-size: 12px;
    font-weight: 400;
    line-height: 16px;
}
.card-flash-title{
    font-size: 28rpx;
    font-weight: 400;
    color: #333;
    line-height: 32rpx;
    position: relative;
    width: 100%;
}
.card-row-box{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    margin-top: 4px;
}

.share-img{
    width: 12px;
    height: 12px;
}
.open-arrow{
    width: 12rpx;
    height: 9rpx;
    margin-right: 4px;
}
.card-show-box{
    display: flex;
    flex-direction: row;
    align-items: center;
}
.card-show-txt{
    color:#E8340F;
    font-size: 12px;
    line-height: 16px;
    font-weight: 400;
}
.line-view{
    background-color: #EAEAEA;
    width: 2rpx;
    height: 19rpx;
    margin: 0 12rpx;
}
.publish-txt{
    background-color: #F04E41;
    border-radius: 2px;
    color:#fff;
    font-size: 9px;
    padding: 2px;
    margin-left: 10px;
}
.card-news-title{
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    line-height: 40rpx;
    padding-left: 95rpx;
}

.no-padding {
  padding-left: 0rpx;
}
.wrapper {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
}

.block {
    width: 90%;
    margin-left: 5%;
    margin-right: 5%;
    margin-top: 20%;
    background-color: #FBFBFB;
    padding: 30px 9px 45px 9px;
    position: relative;

}
.overTitle{
    display: flex;
    justify-content: center;
    font-size: 18px;
    color: #D12D2E;
    font-weight: 600;
    text-align: left;
}
.blockTop{
    background-color: #fff;
    border-radius: 24px;
    padding:12px 20px;
}
.titlePho{
   display: flex;
   justify-content: center;
   position: absolute;
   left:25%;
   top: 15px;
}
.blockBottomPho{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 30px;
}
.blockTime{
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 10px;
    margin-bottom: 16px;
    color:#9A9A9A;
    font-size: 8px;
}
.blockTopicName{
    background-color: rgba(183, 183, 183, 0.2);
    border-radius: 4px;
    color:#878787;
    font-size: 8px;
    margin-bottom: 12px;
    padding: 4px;
}
.saomiao{
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
}
