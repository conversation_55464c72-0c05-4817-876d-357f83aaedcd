import {enums, format, global, storage,wbs,eventName,interaction} from "../../../../common/index.js";
import {
  geStorageUnionId,
  getCardRefreshMoment,
  getOpenId,
  getToken,
  getWechatInfoId,
  setWebPageShowWay
} from "../../../../common/utils/userStorage";
const {
  SEND_WEBVIEW_OPTIONS,
} = eventName


const {PAGE_INFO_REALM,LABEL_NAME,LOGIN_VISITOR} = enums

const moment = require("../../../../lib/monment/index")
moment.locale('en', {
  longDateFormat: {
    l: "YYYY-MM-DD",
    L: "YYYY-MM-DD HH:mm"
  }
});

Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    realmType: {
      type: String,
      value: ''
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    },
    newsType:{
      type: String,
      value: 'ARTICLE'
    },
    showBorder:{
      type:Boolean,
      value:true
    }
  },
  data: {
    imgAnimate: 'fade_null',
    index:0,
    isLast:false,
    newsType:'',
    isShowMore: false,
},

  attached() {
    console.log('====== NEWS props >>>>', this.properties)
    let {data = {},newsType = ''} = this.properties || {}
    this.setData({
      index:data?.index || 0,
      isLast:data?.isLast || false,
      newsType:newsType
    })
    return this.installCardInfo(data)
  },

  methods: {
    onShowMore(){
      const {isShowMore} = this.data
      this.setData({
        isShowMore :!isShowMore
      })
    },
    toList(e){
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      return wx.navigateTo({
        url: `/pages/home/<USER>/list?type=fund&categoryIds=${item.topicId}&name=${item.topicName}专题产品&isTopicList=true`
      })
    },
    toSpecial(e){
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      let params = {
        token: getToken(),
        openid: getOpenId(),
        unionid: geStorageUnionId(),
        wechatInfoId: getWechatInfoId(),
      }
      // if(item.topicId){
      //   params.perfix = `${wbs.gfH5}/share/advSpecialTopic`
      //   params.value = item.topicId
      //   params.title = `${item.topicName}专题页`
      //   params.pageType = 'specialDetail'

      //   return wx.navigateTo({
      //     url: '/pages/common/webview/webPage',
      //     success(res) {
      //       res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params)
      //       setWebPageShowWay(1)
      //     }
      //   });
      // }else{
      //   console.log('item=====',item)
      //   getApp().event.emit("SHOWMODAL",item)
      // }
      if(item.topicId){
        let userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);
        const hasLogin = !LOGIN_VISITOR.includes(userRole * 1);
        if(!hasLogin){
          return wx.navigateTo({
            url: '/pages/loginAndRegist/login/login',
            success(res) {}
          })
        }
        params.perfix = `${wbs.gfH5}/share/advSpecialTopic`
        params.value = item.topicId
        params.title = `${item.topicName}页`
        params.pageType = 'specialDetail'
      }else{
        params.perfix = `${wbs.gfH5}/share/advNewsShare`
        params.value = item.id
        params.title = item.label
        params.pageType = 'flashShare'
        params.cardTitle = item.cName
      }
      
      return wx.navigateTo({
        url: '/pages/common/webview/webPage',
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params)
          setWebPageShowWay(1)
        }
      });

    },


    installCardInfo(params = {}) {
      params.labelName = params.labelType?LABEL_NAME[params.labelType]?.name:params.copyright?LABEL_NAME[params.copyright]?.name:''
      params.labelColor = params.labelType?LABEL_NAME[params.labelType]?.textColor:params.copyright?LABEL_NAME[params.copyright]?.textColor:''
      params.labelBGColor = params.labelType?LABEL_NAME[params.labelType]?.bgColor:params.copyright?LABEL_NAME[params.copyright]?.bgColor:''
      params.showTime = params.timePublished?params.timePublished.slice(0,10) : params.timeCreatedStr ||'-'
      params.titleImg = params.copyright == 'CLSD'?'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2022-10-26/277c5d2d-d10f-413b-9c5f-59609d26642a.png':params.copyright == 'TFKB'?'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2022-10-27/0fbca3a7-9868-477d-88ec-5751fd4024e4.png':''
      params.timeCreated = params?.timeCreated?.replace("T","  ") || ''
      this.setData({
        data:params
      })
    },
    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },
  }
});
