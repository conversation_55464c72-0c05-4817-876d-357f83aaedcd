<view class="card-live">
  <view class="card-live-box">
    <view class="card-live-line">
      <view class="card-status card-status-{{liveItem.liveStatus}}">
        {{LIVE_STATUS[liveItem.liveStatus]}}
      </view>
      <view class="card-live-title">{{liveItem.title }}</view>
    </view>
    <view class="card-live-value">{{liveItem.briefIntroduction}}</view>
    <view class="card-live-line card-live-other">
      <view class="card-live-line-top">
        <image
          src="{{liveItem.liveStatus == 1 || liveItem.liveStatus == 2 ? '../../../../imgs/live/live-time.png' : '../../../../imgs/live/live-time-end.png'}}"
          class="head-icon"
        />
        <view
          class="{{liveItem.liveStatus == 1 || liveItem.liveStatus == 2?'card-live-time':'card-live-time-end'}}"
          >{{liveItem.startTimeStr}}</view
        >
      </view>
      <view class="card-live-stub"></view>
      <view class="card-live-line-top">
        <image src="../../../../imgs/live/live-avatar.png" class="head-icon" />
        <view class="card-live-speaker">{{liveItem.speaker || '-'}}</view>
      </view>
      <!-- <view class="card-live-line-top">
        <image
          src="../../../../imgs/home_share.png"
          mode="scaleToFill"
          class="head-icon"
          catchtap="showPoster"
        />
      </view> -->
    </view>
  </view>
</view>
