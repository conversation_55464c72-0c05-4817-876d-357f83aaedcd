import { enums } from "../../../../common/index.js";
import { formatTimestamp } from "../../../../common/utils/format";
const { LIVE_STATUS } = enums;
Component({
  properties: {
    item: { type: Object, value: {} },
  },
  data: {
    LIVE_STATUS,
    liveItem: {},
  },
  attached() {
    this.setData({
      liveItem: {
        ...this.properties.item,
        startTimeStr: formatTimestamp(
          this.properties.item.startTime,
          "mmddhhmmss"
        ),
      },
    });
    console.log("**", this.data.liveItem);
  },
  methods: {
    showPoster() {
      this.triggerEvent("showPoster", this.properties.item);
    },
  },
  detached: function () {},
});
