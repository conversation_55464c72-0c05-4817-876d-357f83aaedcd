.card-live {
  flex-direction: column;
  align-items: center;
  padding: 0 30rpx;
}
.card-live-box {
  padding-top: 25rpx;
}
.card-live-line {
  display: flex;
  align-items: center;
}
/* .card-live-line-top:last-child {
  flex: 1;
  justify-content: flex-end;
} */
.card-live-title {
  font-weight: 500;
  width: 462rpx;
  line-height: 40rpx;
  font-size: 34rpx;
  text-align: left;
  color: #333333;
  margin-left: 14rpx;
  flex: 1;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.card-live-value {
  font-size: 26rpx;
  line-height: 26rpx;
  color: #555;
  margin-top: 18rpx;
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.card-live-other {
  font-size: 26rpx;
  font-weight: 400;
  line-height: 22rpx;
  padding: 59rpx 0 29rpx;
  border-bottom: 1rpx #efefef solid;
}
.card-live-time {
  color: #ff1f1f;
  margin-left: 14rpx;
  width: 200rpx;
}
.card-live-time-end {
  color: #999;
  margin-left: 14rpx;
  width: 200rpx;
}

.card-live-speaker {
  margin-left: 14rpx;
  color: #999999;
  width: 330rpx;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
  overflow: hidden;
  line-height: 30rpx;
}
.head-icon {
  width: 23rpx;
  height: 23rpx;
}
.card-live-stub {
  width: 2rpx;
  height: 19rpx;
  line-height: 19rpx;
  padding: 1rpx 0;
  background: #eaeaea;
  margin: 0 28rpx;
}
.card-live-line-top {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.card-status {
  font-size: 20rpx;
  color: #fff;
  width: 83rpx;
  text-align: center;
  border-radius: 8rpx 0 8rpx 0;
  line-height: 30rpx;
}
.card-status-1 {
  background: linear-gradient(90deg, #dbb87c, #eecb92);
  width: 68rpx !important;
}
.card-status-2 {
  background: linear-gradient(90deg, #f32e2e, #f77c7c);
}
.card-status-3 {
  background: linear-gradient(90deg, #999999, #c6c6c6);
}
