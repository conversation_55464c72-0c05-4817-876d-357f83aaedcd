@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.card{
    background-color: #fff;
}
.card-box{
    display: flex;
    flex-direction: row;
    margin: 14px 14px 0 14px;
    padding-bottom: 14px;
    justify-content: space-between;
    border-bottom: 1px solid rgba(238, 238, 238, 0.3);
}

.card-right{
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
    justify-content:space-around;
    /* height: 140rpx; */
}

.card-title-text{
    font-size: 14px;
    color: #333;
    font-weight: 500;
    /* font-family: PingFang-Bold; */
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}


.card-subtitle-text{
    font-size: 24rpx;
    color: #969696;
    font-weight: 400;
    margin-top: 6px;
    /* font-family: 'DINCond-Bold'; */
}

.report-icon{
  width: 46px;
  height: 46px;
  border-radius: 12rpx;
  margin-right: 14px;
}

