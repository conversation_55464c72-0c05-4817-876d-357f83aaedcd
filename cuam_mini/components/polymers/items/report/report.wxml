<view class='card'
      style="border-top-left-radius:{{(signalBlock && data.index===0)?12:0}}rpx; border-top-right-radius:{{(signalBlock && data.index==0)?12:0}}rpx; border-bottom-right-radius:{{data.isLast?12:0}}rpx; border-bottom-left-radius:{{data.isLast?12:0}}rpx;">
  <view
      class="card-box"
      data-item="{{data}}"
      bind:tap="onMarketingAction"
      style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">
    
    <image src="../../../../imgs/icon/<EMAIL>" class="report-icon" mode="aspectFill"/>

    <view class="card-right">
      <text class="card-title-text text-one-line">{{data.fundName || '-'}}</text>
      <text class="card-subtitle-text text-one-line">{{data.fundCode || '-'}}</text>
    </view>
  </view>
</view>
