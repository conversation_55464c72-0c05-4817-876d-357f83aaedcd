import {enums, global, storage} from "../../../../common/index.js";

const {PAGE_INFO_REALM} = enums

Component({
  properties: {
    data: {
      type: Object,
      value: {}
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    }
  },

  data: {
    imgAnimate: 'fade_null'
  },

  attached() {
    // console.log('========== MARKETING_PLAN props >>>', this.properties)
    const {data = {}} = this.properties || {}
    const {id = ''} = data || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    // console.log('======== BBBB hashPool >>>>', !hashPool.includes(id))
    if (!hashPool.includes(id) && !!id){
      this.setData({
        imgAnimate: 'fade_in'
      })
      hashPool.push(id)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
  },

  methods: {
    onMarketingAction(e) {
      console.log('======= onMarketingAction e >>>', e)
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}


      const {realm = ''} = item || {}

      const passParams = {
        ...item,
        action: `action://share/${PAGE_INFO_REALM[realm] || 'reportDetail'}`
      }

      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})
    }
  }
});
