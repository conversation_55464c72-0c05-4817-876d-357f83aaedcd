import {enums, global, storage, userStorage, wbs,qs} from "../../../../common/index.js";
const {
  getToken,
  getOpenId,
  geStorageUnionId,
  getWechatInfoId,
  setWebPageShowWay
} = userStorage

const {PAGE_INFO_REALM} = enums

Component({
  properties: {
    data: {
      type: Object,
      value: {}
    },
    showBorder:{
      type:Boolean,
      value:true
    }
  },

  data: {
    bgImg:'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2023-11-20/cd4d2f10-cf66-4022-89e8-1f2dae340b1d.png',
    bgEmptyImg:'',
    showUpBox: false
  },

  attached() {
    const { data,showBorder} = this.properties
    let showUpBox = data?.indexTag?.includes('涨') || false
    this.setData({
      data,
      showBorder,
      showUpBox
    })
  },

  methods: {
    clickItem(e){
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      let params = {
        indexCode:item.indexCode,
        indexName:item.indexName,
        token:getToken(),
        openid:getOpenId(),
        unionid:geStorageUnionId(),
        wechatInfoId:getWechatInfoId(),
        pageType:"OutLink",
        faId:''
      }
      setWebPageShowWay(1)
       let url = `${wbs.gfH5}/channel_h5/indexNewTFYJ/#/allIndexDetails?${qs.stringify(params,{encode:false})}`
       return wx.navigateTo({
        url: `/pages/common/webview/webPage?url=${encodeURIComponent(url)}`,
      })
    },
    clickMark(){
      this.triggerEvent('clickNotes');
    }
  }
});
