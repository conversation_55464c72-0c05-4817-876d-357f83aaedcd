.exponent-box-card{
  width: 100%;
  height: 100%;
  border-top: 1rpx solid #EEEEEE;
  padding: 0rpx;
  margin: 0rpx;
  background-color: #fff;
  padding: 20rpx 20rpx 20rpx;
}
.exponent-box-card2{
  width: 100%;
  height: 100%;
  border-top: 1rpx solid #EEEEEE;
  padding: 0rpx;
  margin: 0rpx;
  background-color: #fff;
  padding: 24rpx 20rpx 32rpx;
  border-radius: 18rpx;
}
.border-box-bg{
  width: 650rpx;
  display: flex;
  flex-direction: column;
  background-color: #fff;
  box-shadow: 0 2rpx 16rpx 0 #e1e1e133;
  border: 2rpx solid #fff;
  border-radius: 18rpx;
}
.title-box{
  margin-top: 26rpx;
  margin-left: 20rpx;
  margin-right: 20rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.title-text{
  font-size: 38rpx;
  line-height: 38rpx;
  color: #333333;
  font-weight: 600;
  max-width: 260rpx; 
  overflow: hidden;
  white-space: nowrap; /* 禁止文本换行 */
  text-overflow: ellipsis; /* 超出容器宽度时显示省略号 */
}
.code-text{
  font-size: 26rpx;
  color: #FF1F1F;
  margin-left: 30rpx;
  line-height: 56rpx;
}
.info-line{
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: space-around;
  margin-top: 24rpx;
}
.info-content-box{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 200rpx;
  height: 115rpx;
  background-color: rgba(248, 248, 248, 0.4);
  border-radius: 12rpx;
}
.info-title{
  font-size: 28rpx;
  line-height: 28rpx;
  color: #333333;
  font-weight: 500;
}
.exponent-info-img{
  width: 108rpx;
  height: 26rpx;
}
.info-text{
  font-size: 22rpx;
  line-height: 22rpx;
  color: #999999;
  margin-top: 18rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
}
.info-text2{
  font-size: 20rpx;
	line-height: 20rpx;
  color: #999999;
  margin-left: 12rpx;
  margin-right: 12rpx;
}
.code-box{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.code-title{
  font-size: 34rpx;
  font-weight: 600;
  color: #FF1F1F;
}

.code-title .code-positiveVal{
	font-size: 34rpx;
  font-weight: 600;
  color: #FF1F1F;
}
.code-title1{
	display: flex;
	align-items: center;
}
.code-title .code-text{
	margin: 0;
	padding: 0;
	font-size: 20rpx;
	height: 20rpx;
	line-height: 20rpx;
  color: #333333;
	margin-left: 8rpx;	
	font-weight: normal;
}
.code-info{
  font-size: 20rpx;
  color: #333333;
  margin-right: 16rpx;
}
.code-info1{
	margin-right: 8rpx;
}
.code-line{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding-left: 14rpx;
  padding-right: 14rpx;
}
.note-box{
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 22rpx;
  color: #999999;
}
.note-img{
  width: 33rpx;
  height: 28rpx;
}
.bottom-tips{
	width: 100%;
	font-size: 14rpx;
	font-family: PingFang SC;
	font-weight: 600;
	color: #999999;
	text-align: center;
	margin-top: 8rpx;
	padding-bottom: 18rpx;
	line-height: 18rpx;
}
.date-text{
  height: 26rpx;
  width: 70rpx;
  opacity: 0.5;
  border: 1rpx solid #A6A6A6;
  border-radius: 4rpx;
  font-size: 18rpx;
  color: #333333;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 5rpx;
}
.mark{
  width: 25rpx;
  height: 25rpx;
  margin-left: 7rpx;
}
.row-box{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.row-box2{
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-left: 30rpx;
  margin-right: 30rpx;
}
.up-box{
  width: 80rpx;
  height: 26rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 17rpx;
  color: #F71A0B;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  background: #ffedec;
  margin-left: 16rpx;
}
.up-box2{
  width: 80rpx;
  height: 26rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 17rpx;
  color: #999;
  border-radius: 4rpx 4rpx 4rpx 4rpx;
  background: #F6F6F6;
  margin-left: 16rpx;
}
.ctrl{
  width: 74rpx;
  height: 1rpx;
  opacity: 0.2;
  background: #333333;
}
.mark-box{
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
}
.wifi-img{
  width: 48rpx;
  height: 35rpx;
}
.wifi-text{
  width: 41rpx;
  height: 37rpx;
  display: flex;
  flex-wrap: wrap;
  font-size: 18rpx;
  line-height: 20rpx;
  color: #FF1F1F;
  font-weight: 600;
  margin-left: 8rpx;
  margin-right: 10rpx;
}