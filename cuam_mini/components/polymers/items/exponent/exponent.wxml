<view data-item="{{data}}" class="{{showBorder?'exponent-box-card':'exponent-box-card2'}}" bindtap="clickItem">
  <view class="border-box-bg" style="margin: 0;padding: 0;background-image: linear-gradient(179deg, #FFEFF0 0%, #FFFFFF 20%, #FFFFFF 100%);background-size:100% 100%;">
    <view class="title-box">
      <view class="row-box">
        <image wx:if="{{data.signalLevel == '2'}}" src="/imgs/exponent/wifi.png" class="wifi-img"/>
        <image wx:if="{{data.signalLevel == '3'}}" src="/imgs/exponent/wifi-big.png" class="wifi-img"/>
        <view wx:if="{{data.signalLevel == '2' || data.signalLevel == '3'}}" class="wifi-text">{{data.signalLevelText}}</view>
        <view class="title-text">{{data.indexNameAbbr}}</view>
        <view catch:tap="clickMark" class="mark-box">
         <image  src="/imgs/exponent/mark.png" class="mark"/>
        </view>
      </view>
      <view class="row-box">
        <view class="code-text" wx:if="{{data.changepct}}" style="color: {{data.changepct<0?'#009F54':'#FF1F1F'}};">{{data.changepct>0?'+':''}}{{data.changepct}}%</view>
        <view class="{{showUpBox?'up-box':'up-box2'}}" wx:if="{{data.indexTag}}" style="">{{data.indexTag}}</view>
      </view>
    </view>
    <view class="info-line">
      <view class="info-content-box">
        <view class="info-title">{{data.valuationTab || '估值'}}</view>
        <view wx:if="{{data.valuationVal}}" class="info-text">估值分位<text style="color: {{data.valuationVal<0?'#009F54':'#FF1F1F'}};">{{data.valuationVal}}%</text></view>
        <view wx:else class="info-text" style="margin-top:20rpx">暂无数据</view>
      </view>
      <view class="info-content-box" style="background-image: url({{data.performanceVal?bgEmptyImg:bgImg}});background-size: 100% 100%;width: 220rpx;">
        <view class="info-title">{{data.performanceTab || '基本面'}}</view>
        <view wx:if="{{data.performanceVal}}" class="info-text">净利润同比<text style="color: {{data.performanceVal<0?'#009F54':'#FF1F1F'}};">{{data.performanceVal>0?'+':''}}{{data.performanceVal}}%</text></view>
        <view wx:else class="info-text" style="margin-top:20rpx">暂无数据</view>
      </view>
      <view class="info-content-box" style="background-image: url({{data.speciesVal?bgEmptyImg:bgImg}});background-size: 100% 100%;">
        <view class="info-title">主力资金流入</view>
        <view wx:if="{{data.speciesVal}}" class="info-text" style="color: {{data.speciesVal<0?'#009F54':'#FF1F1F'}};"><text wx:if="{{data.speciesVal}}" class="date-text">近20日</text><text style="color: {{data.speciesVal<0?'#009F54':'#FF1F1F'}};">{{data.speciesVal>0?'+':''}}{{data.speciesVal}}亿</text></view>
        <view wx:else class="info-text" style="margin-top:20rpx">暂无数据</view>
      </view>
    </view>
    <view wx:if="{{data.averageVal && data.positiveYieldCount >= 50 }}" style="margin-top: 20rpx;">
      <view class="row-box2">
        <view class="ctrl"></view>
        <view class="info-text2">{{data.aveAndPosText}}</view>
        <view class="ctrl"></view>
      </view>
      <view class="code-line" style="margin-top:12rpx">
        <view class="row-box">
          <view class="code-info">{{data.averageValText}}</view>
          <view class="code-title">+{{data.averageVal}}%</view>
        </view>
        <view  class="row-box">
          <view class="code-info code-info1">{{data.positiveValText}}</view>
          <view class="code-title" wx:if="{{data.positiveVal<0.8}}">+{{data.positiveVal}}%</view>
					<view class="code-title code-title1" wx:else="">
						<text class="code-positiveVal">达80%</text>
						<view class="code-text">以上</view>
					</view>
        </view>
        </view>
    </view>
		<view class="bottom-tips" wx:if="{{data.averageVal && data.positiveYieldCount >= 50 }}">
			注:平均收益与历史正收益概率为指数历史业绩测算结果,不代表指数未来走势,不预示基金未来表现。
		</view>
  </view>
</view>