<view class="card"
      style="border-top-left-radius:{{(signalBlock && data.index===0)?12:0}}rpx; border-top-right-radius:{{(signalBlock && data.index==0)?12:0}}rpx; border-bottom-right-radius:{{data.isLast?12:0}}rpx; border-bottom-left-radius:{{data.isLast?12:0}}rpx;">
  <view class="live-block"
        bind:tap="onLiveAction"
        style="margin-top:{{(signalBlock && data.index==0)?-6:0}}px;padding-top:{{(signalBlock && data.index==0)?12:14}}px;">
    <view class="live-img-block" style="border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">
      <image
          src="{{data.cover||''}}?x-oss-process=image/resize,m_pad,h_280,w_496"
          class="live-img {{imgAnimate}}"
          mode="widthFix"/>

      <view wx:if="{{data.liveStatusStr}}" class="live-status-block"
            style="background-color:{{LIVE_STATUS[data.liveStatus]||'crimson'}};">
        <view class="status-tips">{{data.liveStatusStr || ''}}</view>
      </view>
    </view>

    <view class="live-content-block">
      <view class="live-title text-two-line">
        {{data.title || '-'}}
      </view>

      <view class="live-bottom">
        <view class="live-bottom-txt text-one-line" wx:if="{{data.shortDesc}}">
          {{data.shortDesc || '-'}}
        </view>

        <view class="live-bottom-txt text-one-line" wx:if="{{!data.shortDesc && data.subTitle}}">
          {{data.subTitle || '-'}}
        </view>
      </view>
    </view>
  </view>
</view>
