@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}


.card{
    background-color: #fff;
}

.live-block{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    padding-bottom: 14px;
    margin: 14px 14px 0 14px;
    border-bottom: 1px solid rgba(238, 238, 238, 0.3);
}

.live-img-block{
    display: flex;
    flex-direction: column;
    margin-right: 20rpx;
    min-height: 140rpx;
    max-width: 248rpx;
}

.live-status-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: absolute;
    /*background-color: crimson;*/
    border-top-right-radius: 12rpx;
    border-bottom-left-radius: 12rpx;
    padding: 4rpx 8rpx;
    z-index: 99;
    align-self: flex-end;
}

.status-tips{
    font-size: 24rpx;
    color: #fff;
    font-weight: 400;
    /* font-family: PingFang-Bold; */
}

.live-img{
    height: 140rpx;
    width: 248rpx;
    border-radius: 12rpx;
    max-height: 140rpx;
}

.live-content-block{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-start;
    flex: 1;
    padding-right: 28rpx;
}

.live-title{
    font-size: 16Px;
    color: #333;
    font-weight: 500;
    /* font-family: PingFang-Bold; */
}

.live-bottom{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    flex: 1
}

.live-bottom-txt{
    font-size: 24rpx;
    color: #999;
    font-weight: 400;
    margin-top: 6rpx;
    /* font-family: PingFang-Bold; */
}

.text-two-line {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.com-image-live-statas-div1{
    background: #00B659;
}
.com-image-live-statas-div2{
    background: #2179FF;
}
.com-image-live-statas-div3{
    background: #ccc;
}
.com-image-live-statas-div4{
    background: #FF9800;
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}
