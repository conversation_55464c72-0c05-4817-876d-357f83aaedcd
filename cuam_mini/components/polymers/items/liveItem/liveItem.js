import {enums, global, qs, storage} from "../../../../common/index.js";

const {PAGE_INFO_REALM, LIVE_STATUS} = enums


Component({
  properties: {
    data: {
      type: Object,
      value: {}
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    }
  },
  data: {
    LIVE_STATUS,
    imgAnimate: 'fade_null'
  },

  attached() {
    // console.log('========== LIVE_ITEM props >>>', this.properties)
    const {data = {}} = this.properties || {}
    const {id = ''} = data || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    // console.log('======== hashPool >>>>', !hashPool.includes(id))
    if (!hashPool.includes(id) && !!id){
      this.setData({
        imgAnimate: 'fade_in'
      })
      hashPool.push(id)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
  },

  methods: {
    onLiveAction(e) {
      // console.log('======   onLiveAction e >>>> ', e)
      const {data, cName = '', cId = '', mName = ''} = this.data || {}
      const {realm} = data || {}

      const passParams = {
        ...data,
        action: `action://share/${PAGE_INFO_REALM[realm] || 'AppAdvLive'}`,
        cName,
        cId,
        mName,
      }

      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})
    }
  }
});
