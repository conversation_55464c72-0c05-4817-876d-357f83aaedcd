@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.card {
  background-color: #fff;
}

.card-box {
  display: flex;
  flex-direction: column;
  margin: 28rpx 28rpx 0 28rpx;
  padding-bottom: 34rpx;
  overflow: hidden;
  /* align-items: center; */
  border-bottom: 1rpx solid #eaeaea;
}

.top_view {
  display: flex;
  flex-direction: row;
  align-items: center;
}
.top-box{
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  height: 31rpx;
}
.left-box{
  width: 60rpx;
  display: flex;
  justify-content: flex-end;
  align-items: center;
}
.start{
  width: 36rpx;
  height: 36rpx;
}

.product_name {
  font-weight: 600;
  font-size: 32rpx;
  color: #333;
  text-align: left;
}

.product_code {
  font-weight: 300;
  font-size: 26rpx;
  color: #999999;
  /* letter-spacing: 1rpx; */
  margin-left: 20rpx;
}

.img_header {
  width: 128rpx;
  height: 128rpx;
  border-radius: 20rpx;
  background-color: blue;
}

.header_name {
  /* font-family: PingFang-Bold; */
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  letter-spacing: 0;
  line-height: 40rpx;
}

.top_name_view {
  display: flex;
  flex-direction: row;
}

.tags {
  display: flex;
  flex-direction: row;
  margin-top: 20rpx;
  border-radius: 4rpx;
  white-space: nowrap;
  width: 100%;
}

.tag-item {
  /* display: inline-block; */
  line-height: 28rpx;
  font-weight: 400;
  font-size: 20rpx;
  color: rgba(255, 31, 31, 0.8);
  /* letter-spacing: 5rpx; */
  padding: 0rpx 8rpx;
  background-color: rgba(255,240,239,1);
  border-radius: 8rpx;
  margin-right: 18rpx;
  word-spacing: 2rpx;
}

.header_des {
  /* font-family: PingFang-Bold; */
  font-weight: 400;
  font-size: 28rpx;
  color: #666666;
  letter-spacing: 0;
  text-align: justify;
  line-height: 40rpx;
  margin-top: 16rpx;
}

.center {
  display: flex;
  flex: 1;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 24rpx;
}

.center_left {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-start;
}

.center_center {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: center;
}

.center_right {
  display: flex;
  flex: 1;
  flex-direction: column;
  align-items: flex-end;
}

.center_num_text {
  font-weight: 500;
  font-size: 32rpx;
  display: flex;
  text-align: center;
  /* margin-bottom: 8rpx; */
}

.center_des_text_left {
  font-weight: 400;
  font-size: 22rpx;
  color: #FF1F1F;
  letter-spacing: 0;
  /* margin-top: 6rpx; */
  display: flex;
  align-items: center;

  border-radius: 30rpx;
  padding: 0rpx 10rpx;
  border: 1rpx solid #FF1F1F;
}

.jt {
  width: 15rpx;
  height: 15rpx;
  margin: 8rpx 1rpx 12rpx 8rpx;
}

.center_des_text {
  font-weight: 400;
  font-size: 22rpx;
  color: #999999;
  letter-spacing: 0;
  margin-top: 9rpx;
  display: flex;
  align-items: center;
}

.center_text {
  font-weight: 400;
  font-size: 24rpx;
  color: #666666;
  letter-spacing: 0;
  text-align: center;
  line-height: 28rpx;
}

.bottom {
  display: flex;
  flex: 1;
  background-color: #FFF8F3;
  padding: 12rpx 20rpx;
  border-radius: 10rpx;
  margin-top: 18rpx;
  align-items: flex-start;
}

.bottom_img {
  width: 30rpx;
  height: 30rpx;
  margin-right: 18rpx;
}

.bottom_text {
  display: flex;
  flex: 1;
  font-weight: 400;
  line-height: 30rpx;
  font-size: 20rpx;
  color: #B36428;
  letter-spacing: 0;
  /* padding: 5rpx 0rpx 5rpx 0rpx; */
  /* border-radius: 5rpx; */
}

.text-one-line {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}

.text-two-line{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.item-tag{
  display: flex;
  flex-direction: row;
  align-items: center;
  background: linear-gradient(90deg,#FF1F2F, #FF5D5D);
  font-size: 20rpx;
  line-height: 28rpx;
  color: #fff;
  padding: 0rpx 10rpx;
  margin-left: 18rpx;
  margin-top: 10rpx;
  border-radius: 4rpx;
}
.newcenter{
  display: flex;
  flex: 1;
  flex-direction: row;
  align-items: center;
  justify-content: space-around;
  margin-top: 26rpx;
  margin-bottom: 8rpx;
}
.newcenterleft{
  display: flex;
  flex-direction: row;
}
.newleft{
  width: 50rpx;
  height: 56rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-right: 8.5rpx;
  font-size: 34rpx;
  font-weight: 600;
  color: #E81C1C;
}
.newcentercenter{
  background-color: #DBDBDB;
  width: 1rpx;
  height: 58rpx;
}
.newright{
  display: flex;
  flex-direction: row;
  color: #FF1F1F;
  font-size: 28rpx;
  font-weight: 600;
}