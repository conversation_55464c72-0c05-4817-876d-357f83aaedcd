import { enums, format, global, storage, interaction, eventName } from "../../../../common/index.js";
import { setStatus } from "../../../../common/nb/home";


const { PAGE_INFO_REALM, LOGIN_VISITOR, } = enums

const { CHANGE_POCUDT_CARE, CHANGE_PRODUCT_START, CANCEL_ATTENTION_PRODUCT } = eventName

const moment = require("../../../../lib/monment/index")
moment.locale('en', {
  longDateFormat: {
    l: "YYYY-MM-DD",
    L: "YYYY-MM-DD HH:mm"
  }
});

Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    realmType: {
      type: String,
      value: ''
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    isFromSearch:{
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    },
    showBorder: {
      type: Boolean,
      value: true
    },
    itemindex: {
      type: Number,
      value: 0
    },
    fromTab: {
      type: String,
      value: ''
    },
    isFromSingleContent:{
      type:Boolean,
      value:false
    },
    pageType:{
      type: String,
      value: ''
    }
  },
  data: {
    imgAnimate: 'fade_null',
    index: 0,
    isLast: false,
    showData: [],
    more6: false,
    isNewItem:false,
    newBg:'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2023-03-06/240acb49-b937-4389-95f0-04881df3ac00.png'
  },

  attached() {
    // console.log('====== Product props >>>>', this.properties)
    let { data = {} } = this.properties || {}
    // let _this = this
    // getApp().event.on('PRODUCT_SELECT_SELECT', (e) => {
    //   if(data?.fundCode == e?.fundCode){
    //     _this.setData({
    //       showData: { ...e, selectname: e.name, selectnumber: e.number },
    //     })
    //   }
    // })
    return this.installCardInfo(data)
  },

  methods: {
    onNewsAction(e) {
      // console.log('===== sensors_is_show this product >>> ', this.properties)
      console.log('== sensors_is_show_message == news',this.data.data);
      console.log('== sensors_is_show ==',this.properties.showData?.sensorsdata?.fromTab|| this.properties?.data?.page_name || '所属页面_null');
      console.log('== sensors_is_show ==',this.properties?.cName || e.currentTarget.dataset?.item?.sensorsdata?.moduleTitle || '位置区_null');
      console.log('== sensors_is_show ==',this.properties.showData?.sensorsdata?.ordinal+'');
      console.log('== sensors_is_show ==',this.properties?.showData?.specialTopicName || this.properties?.mName || this.properties.showData?.sensorsdata?.secondaryTitle || this.properties?.data?.title_name || '二级标题_null');
      console.log('== sensors_is_show ==',this.properties?.data?.fundName || this.properties?.data?.fund?.fundName || '产品名称_null');
      console.log('== sensors_is_show ==',this.properties?.data?.fund?.fundCode || this.properties?.data?.fundCode || '产品代码_null');
      // 神策埋点业务逻辑
      getApp().sensors.track('userClick', {
        pageName: this.properties.showData?.sensorsdata?.fromTab || this.properties?.data?.page_name || '所属页面_null',
        cardName: this.properties?.cName || e.currentTarget.dataset?.item?.sensorsdata?.moduleTitle || '位置区_null',
        sequenceId: this.properties.showData?.sensorsdata?.ordinal + '',
        sub_position: this.properties?.showData?.specialTopicName || this.properties?.mName || this.properties.showData?.sensorsdata?.secondaryTitle || this.properties?.data?.title_name || '二级标题_null',
        fund_name: this.properties?.data?.fundName || this.properties?.data?.fund?.fundName || '产品名称_null',
        fund_code: this.properties?.data?.fund?.fundCode || this.properties?.data?.fundCode || '产品代码_null',
        content_name: this.data.data?.content_name || 'Null',
        content_title: this.data.data?.secondaryTitle || 'Null',
        page_title: this.data.data?.tabTitle || 'Null',
        button: '基金产品'
      })
      // console.log('===== onNewsAction this.data >>> ', this.data)
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}

      const { data, cName = '', cId = '', mName = '' } = this.data || {}
      const { realm = '' } = item || {}

      const passParams = {
        ...item,
        action: `action://share/${PAGE_INFO_REALM[realm] || 'advFundProduct'}`,
        cName,
        cId,
        mName,
      }

      // console.log('====== news passParams >>>', passParams)
      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, { bubbles: true, composed: true })
    },
    onClicke(e) {
      if (this.properties.isFromCard) {
        getApp().event.emit('PRODUCT_SELECT_SHOW', { ...this.data.showData, fromTab: this.properties.fromTab })
      } else {
        if (!this.data.showData.setupIsExceedSixMonth) { return }
        const passParams = { itemindex: this.properties.itemindex }
        this.triggerEvent('onItemClicke', passParams, { bubbles: true, composed: true })
      }
    },
    installCardInfo(data = {}) {
      if (data == null) {
        return this.setData({ showData: null })
      }
      let temData = data
      temData.netShowDate = temData?.netDate?.slice(5) || ''
      temData.sinceShowRange = temData?.sinceRange ? Number(temData.sinceRange).toFixed(2) : ''
      temData.dayShowRange = temData?.dayRange ? Number(temData.dayRange).toFixed(2) : ''
      temData.annShowYield = temData?.annYield ? (Number(temData.annYield) * 100).toFixed(3) : ''
      temData.netShowValue = temData?.netValue ? Number(temData.netValue).toFixed(4) : ''
      temData.incomeShowPerMillion = temData?.incomePerMillion ? Number(temData.incomePerMillion).toFixed(4) : ''
      temData.showdata = this.getMAX(data)
      temData.newHeight = ''
      switch(temData.fundNewRecordType){
        case "TAG_TWL":
           temData.newHeight = '近一年新高';
           break;
        case "TAG_SIX":
          temData.newHeight = '近六月新高';
          break
        case "TAG_THR":
          temData.newHeight = '近三月新高';
          break;
      }
      const {isFromSingleContent,isFromSearch,isFromCard } = this.properties
      this.setData({
        index: data?.index || 0,
        showData: temData,
        isLast: data?.isLast || false,
        isNewItem:isFromCard || isFromSearch ?true:false
      })
    },
    async changeStartStatus(){
          //判断是否登录
      const userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);
      let hasLogin = !LOGIN_VISITOR.includes(userRole * 1);
      if(!hasLogin){
        return  wx.reLaunch({
          url: `/pages/loginAndRegist/login/login`
        }) 
      }
      const {showData} = this.data
      let temData = showData
      temData.fromTab = this.properties.fromTab ||  this.properties.showData?.sensorsdata?.fromTab
      temData.pageType = this.properties.pageType
      temData.cardName = this.properties.cName
      getApp()?.event?.emit(CHANGE_PRODUCT_START, temData);
    },

    changeSelected(e){
      const {showData} = this.data
      let temData = showData
      temData.fromTab = this.properties.fromTab ||  this.properties.showData?.sensorsdata?.fromTab
      temData.pageType = this.properties.pageType
      //提供给事件监听函数
      getApp()?.event?.emit(CANCEL_ATTENTION_PRODUCT, temData);
    },

    getMAX(temData) {
      if (!temData.setupIsExceedSixMonth) { return { name: '成立以来', number: temData.sinceRange } }
      const rangelist = [{ name: '近六月', number: temData.last6MRange }, { name: '近一年', number: temData.last1YRange }, { name: '近两年', number: temData.last2YRange }, { name: '近三年', number: temData.last3YRange }, { name: '近五年', number: temData.last5YRange }, { name: '成立以来', number: temData.sinceRange }, { name: '今年以来', number: temData.yearRange }]
      let maxnumber = rangelist[0]?.number
      let maxname = rangelist[0]?.name
      for (let index = 0; index < rangelist.length; index++) {
        const item = rangelist[index];
        if (item?.number !== null && item?.number > maxnumber) {
          maxnumber = item?.number
          maxname = item?.name
        }
      }
      maxnumber = (maxnumber == null) ? '--' : Number(maxnumber).toFixed(2)
      return { name: maxname, number: maxnumber }
    },
    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },
  }
});
