<view class="card" wx:if="{{showData != null}}" style="border-top-left-radius:{{index===0?16:0}}rpx; border-top-right-radius:{{index==0?16:0}}rpx; border-bottom-right-radius:{{isLast?16:0}}rpx; border-bottom-left-radius:{{isLast?16:0}}rpx;">
  <view class="card-box" data-item="{{showData}}" bind:tap="onNewsAction" style="margin-top:{{(signalBlock && index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && index==0)?12:(isFromCard&&index!= 0)?14:0}}px;border-bottom-color: {{isLast || !showBorder?'#fff':'#eaeaea'}}">
    <view class="top-box">
      <view class="top_view">
        <view class="product_name text-one-line">{{showData.fundName}}</view>
        <view class="product_code">{{showData.fundCode}}</view>
      </view>
      <view class="left-box"  catch:tap="{{showData.isCare?'changeSelected':'changeStartStatus'}}" >
        <image wx:if="{{showData.isCare}}" src="/imgs/icon/start-selected.png" class="start"/>
        <image wx:else src="/imgs/icon/start.png"  class="start"/>
      </view>
    </view>

    <scroll-view wx:if="{{showData.fundTypeName || showData.riskLevelName || showData.operateLabelDTO.length}}" scroll-x="true" class="tags">
      <view style='display:flex;flex-direction:row'>
        <view class="tag-item" wx:if="{{showData.fundTypeName}}">{{showData.fundTypeName}}</view>
        <view class="tag-item" wx:if="{{showData.riskLevelName}}">{{showData.riskLevelName}}</view>
        <view wx:for="{{showData.operateLabelDTO}}" wx:key="id">
          <view class="tag-item">{{item.name}}</view>
        </view>
      </view>
    </scroll-view>
    <view class="newcenter" wx:if="{{isNewItem && showData.isLocalCreated}}">
      <view class='newcenterleft'>
        <view class="newleft" style="background-image:url({{newBg}});background-size: 100% 100%;">重</view>
        <view class="newleft" style="background-image:url({{newBg}});background-size: 100% 100%;">磅</view>
        <view class="newleft" style="background-image:url({{newBg}});background-size: 100% 100%;">新</view>
        <view class="newleft" style="background-image:url({{newBg}});background-size: 100% 100%;">基</view>
      </view>
      <view class='newcentercenter'></view>
      <view class="newright">{{showData.publishTimeDesc || '火热发行中' }}</view>
    </view>
    <view class="center" wx:elif="{{showData.fundGroupTp != 1}}">
      <view catch:tap="onClicke" data-item="{{showData}}" class="center_left">
        <view wx:if="{{showData.selectname}}" style="color: rgba(0, 153, 1, 1);" class="center_num_text" style="color:{{showData.selectnumber>0?'#FF0000':showData.selectnumber<0?'#009901':'#999999'}}">{{showData.selectnumber>0?'+':''}}{{showData.selectnumber||'0.00'}}{{showData.selectnumber=='--'?'':'%'}}</view>
        <view wx:else style="color: rgba(0, 153, 1, 1);" class="center_num_text" style="color:{{showData.showdata.number>0?'#FF0000':showData.showdata.number<0?'#009901':'#999999'}}">{{showData.showdata.number>0?'+':''}}{{showData.showdata.number||'0.00'}}{{showData.showdata.number=='--'?'':'%'}}</view>

        <view wx:if="{{!showData.setupIsExceedSixMonth}}" class="center_des_text">{{(showData.selectname || showData.showdata.name)+"涨跌幅"}}</view>
        <view wx:else class="center_des_text_left">{{(showData.selectname || showData.showdata.name)+"涨跌幅"}}<image class="jt" src="./jt.png"></image>
        </view>
      </view>
      <view class="center_center">
        <view style="color: rgba(255, 0, 0, 1)" class="center_num_text" style="color:{{showData.dayRange>0?'#FF0000':showData.dayRange<0?'#009901':'#999999'}}">{{showData.dayRange>0?'+':''}}{{showData.dayShowRange || '0.00'}}%</view>
        <view class="center_des_text">日涨跌幅</view>
      </view>
      <view class="center_right">
        <view class="center_num_text">{{showData.netShowValue || '0'}}</view>
        <view class="center_des_text">最新净值<text wx:if="{{showData.netDate}}">({{showData.netShowDate}})</text></view>
      </view>
    </view>
    <view class="center" wx:else>
      <view class="center_left">
        <view style="color: rgba(0, 153, 1, 1);" class="center_num_text" style="color:{{showData.annYield>0?'#FF0000':showData.annYield<0?'#009901':'#999999'}}">{{showData.annYield>0?'+':''}}{{showData.annShowYield|| '0.00'}}%</view>
        <view class="center_des_text">7日年化</view>
      </view>
      <view class="center_right">
        <view class="center_num_text">{{showData.incomeShowPerMillion || '0'}}</view>
        <view class="center_des_text">万份收益</view>
      </view>
    </view>
    <view wx:if="{{showData.fundGroupTp != 1&&showData.fundGroupTp!=8&&showData.fundGroupTp != 2}}" style="display:flex;flex-direction:row;justify-content: flex-end;">
       <view wx:if="{{showData.keepRiseDays}}" class="item-tag">
        <image src="/imgs/whitearrowup.png" style="width:20rpx;height:20rpx;margin-right:2rpx;"/>
        连涨{{showData.keepRiseDays}}天
      </view>
      <view wx:if="{{showData.newHeight}}" class="item-tag">{{showData.newHeight}}</view>
    </view>
    <view class="bottom" wx:if='{{showData.fundReview}}'>
      <image src="../manager/manger_des.png" class="bottom_img" />
      <view class="bottom_text text-two-line">{{showData.fundReview}}</view>
    </view>
  </view>
</view>