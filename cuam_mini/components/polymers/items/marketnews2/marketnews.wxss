@keyframes fadeIn {
	0% {opacity: 0;}
	100% {opacity: 1;}
}

.card-box{
	background-color:transparent;
	width: 100%;
	display: flex;
	align-items:stretch;
	flex-wrap: wrap;
	position: relative;
}
.recomend-icon{
width: 99rpx;
height: 120rpx;
position: absolute;
right: 20rpx;
top: 0;
}
.recomend-icon-first{
top: 70rpx;
}
.market-day{
width: 100%;
}
.card-box-l{
width: 42rpx;
height:inherit;
position: relative;
display: flex;
flex-direction: column;
}
.red-cir{
width: 11rpx;
height: 11rpx;
background: #E92115;
border-radius: 50%;
position: absolute;
left: 20rpx;
top:33rpx;
}
.card-box-l0 .red-cir{
	top:105rpx;
}
.diver{
margin-left: 24rpx;
width: 0rpx;
flex: 1;
border-left:2rpx dashed #D2D2D2;
}
.card-box-r{
flex: 1;
display: flex;
flex-direction: column;
border-radius: 14rpx;
background: #FFFFFF;
box-shadow: 0px 10rpx 30rpx 0px rgba(187,187,187,0.08);
margin-bottom: 20rpx;
margin-right: 20rpx;
}
.card-box-r-date{
background: url(https://aim-pic.99fund.com/image/market_information/hotspot-base.png) no-repeat;
background-size:100% auto;
}
.market-day{
	width: 100%;
	height: 70rpx;
	font-size: 30rpx;
	font-family: PingFang SC;
	font-weight: 500;
	color: #FFC5C5;
	line-height: 70rpx;
	background: transparent;
	margin-left:27rpx;
}
.market-contain{
display: flex;
flex-direction: column;
background: #fff;
border-radius: 14rpx;
padding-bottom: 33rpx;
position: relative;
}
.market-contain-bg{
	width: 388rpx;
	height: 55rpx;
	position: absolute;
	left: 0;
	top: 0;
}
.market-title{
font-size: 36rpx;
font-family: PingFang SC;
font-weight: 600;
color: #222222;
line-height: 55rpx;
padding: 16rpx 37rpx 0rpx 25rpx;
margin-top: 0rpx;
background: #FFFFFF;
}
.market-content{
padding: 0 0rpx 0rpx 25rpx;
font-size: 26rpx;
font-family: PingFang SC;
font-weight: 400;
color: #555555;
line-height: 40rpx;
padding-right: 37rpx;
margin-top: 20rpx;
background: #FFFFFF;
overflow: hidden;
display: -webkit-box;
-webkit-line-clamp: 2; /* 设置最大显示行数 */
-webkit-box-orient: vertical;
text-overflow: ellipsis;
}
.market-time{
height:22rpx;
font-size: 22rpx;
font-family: PingFang SC;
font-weight: 400;
color: #9F9F9F;
line-height: 22rpx;
border-radius:14rpx;
padding: 28rpx 37rpx 0rpx 25rpx;
box-sizing: content-box;
}
.card-box-l0{
padding-top: 105rpx;
}
.tagList{
	display: flex;
	align-items: center;
}
.scroll-view-tags{
	width: 660rpx;
	margin-left: 26rpx;
	margin-top:30rpx;
}
.scroll-view-tags-index{
	width: 660rpx;
	margin-left: 26rpx;
	margin-top:30rpx;
}
.link-index-list{
	display: flex;
	align-items: center;
}
.index-item{
	height: 40rpx;
	display: flex;
	background: linear-gradient(273deg, #FFFFFF 0%, #FFF1F0 100%);
	border-radius: 4rpx;
	border: 1rpx solid #FED3D0;
	align-items: center;
	flex-shrink: 0;
	margin-right: 12rpx;
}
.index-item .objname{
	height: 30rpx;
	font-size: 22rpx;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #666666;
	line-height: 30rpx;
	padding-right: 10rpx;
}
.index-item .changepctStr{
	height: 30rpx;
	font-size: 22rpx;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #22B26E;
	line-height: 30rpx;
	padding-right: 10rpx;
}
.index-item  .changepctStr-red{
	color: #fe1f2c;
}
.topicList{
	display: flex;
	align-items: center;
}
.topicList .topicName{
	height: 30rpx;
	font-size: 22rpx;
	font-family: PingFangSC, PingFang SC;
	font-weight: 400;
	color: #FF1F1F;
	line-height: 30rpx;
	margin-right: 4rpx;
	padding-left: 10rpx;
}
.topicList .red-right-icon{
	width: 9rpx;
	height: 14rpx;
	margin-right: 12rpx;
}
.topicList .topicList-diver{
	width: 1rpx;
	height: 14rpx;
	background:#FF1F1F;
}
.scroll-view-tags2{
	margin-top: 15rpx;
}
.tagList .tag-item{
flex-shrink: 0;
height: 40rpx;
border: 1rpx solid #FED3D0;
background: linear-gradient(90deg, #FFF0EF 0%, #FFFFFF 100%);
border-radius: 4rpx;
flex-shrink: 0;
display: flex;
align-items: center;
margin-right: 12rpx;
}
.tag-item .topicName{
	height: 38rpx;
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: #FF1F1F;
	margin-left: 17rpx;
	line-height: 38rpx;
	margin-right: 9rpx;
}
.tag-item .topicName-r{
	margin-right: 17rpx;
}
.tag-item .tag-diver{
width: 1rpx;
height: 14rpx;
background: #FF1F1F;

}
.tag-item .objname{
height: 22rpx;
font-size: 22rpx;
font-family: PingFang SC;
font-weight: 400;
color: #777777;
line-height: 22rpx;
margin-left: 9rpx;
}
.tag-item .changepctStr{
	height: 22rpx;
	font-size: 22rpx;
	font-family: PingFang SC;
	font-weight: 400;
	color: #00A758;
	line-height: 22rpx;
	margin-left: 11rpx;
}
.tag-item .changepctStr-red{
  color: #fe1f2c;
}
.tag-item .red-right-icon{
width: 9rpx;
height: 13rpx;
margin-right: 12rpx;
margin-left: 12rpx;
}
.tagList2{


}
.tagList-vis{
	display: none!important;
}
.tag-item2{
height: 40rpx;
background: #F7F7F7;
border-radius: 4rpx;
flex-shrink: 0;
display: flex;
align-items: center;
margin-right: 12rpx;

}
.tag-item2 .fundName{
height: 22rpx;
font-size: 22rpx;
font-family: PingFang SC;
font-weight: 400;
color: #777777;
line-height: 22rpx;
margin-left: 11rpx;
}
.tag-item2 .tag-diver{
width: 1rpx;
height: 14rpx;
background: #BFBFBF;
margin-left: 11rpx;
}
.tag-item2 .fundCode{
width: 70rpx;
font-size: 20rpx;
font-family: PingFang SC;
font-weight: 400;
color: #B7B7B7;	
margin-left: 11rpx;
}
.tag-item2 .red-right-icon{
width: 9rpx;
height: 13rpx;
margin-left:20rpx ;
margin-right: 12rpx;
}