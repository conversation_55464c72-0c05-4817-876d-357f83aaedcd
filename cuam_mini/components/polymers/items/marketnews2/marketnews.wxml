<view class="card-box" data-item="{{data}}" bind:tap="{{'onNewsAction'}}">
	<image wx:if="{{data.isRecommended}}" class="{{index==0?'recomend-icon recomend-icon-first':'recomend-icon'}}" src="../../img/recomend-icon.png" mode=""/>
	<view class="{{'card-box-l card-box-l'+index}}">
		<view class="diver"></view>
		<view class="red-cir"></view>
	</view>
	<view class="{{data.isFirst?'card-box-r card-box-r-date':'card-box-r'}}">
		<view class="market-day" wx:if="{{data.isFirst&&index==0}}">
			{{data.day}} {{data.weekday}}
		</view>
		<view class="market-contain">
			<image src="https://aim-pic.99fund.com/image/market_information/hotspot-back1.png" class="market-contain-bg" wx:if="{{index==0}}" alt="" srcset=""/>
			<text class="market-time">{{data.time}}</text>
			<text class="market-title">{{data.type==1?data.content:data.title}}</text>
			<text class="market-content" wx:if="{{data.summary}}">{{data.summary}}</text>
			<scroll-view wx:if="{{data.indexList||data.indexList.length>0}}" scroll-x="true" class="scroll-view-tags scroll-view-tags-index" catch:tap="catchtap">
			<view class="link-index-list">
				<view class="index-item"
				wx:key="index"
				wx:for-index="index"
				wx:for-item="model"
				wx:for="{{data.indexList}}"
				data-item="{{model}}" 
			>
				<view class="topicList" 
					wx:key="index"
					wx:for-index="index"
					wx:for-item="item"
					wx:for="{{data.quickMessageTopicList}}"
					data-item="{{item}}"
					catch:tap="toSpecial"
					>
					<text class="topicName">{{'#'+item.topicName}}</text>
					<image class="red-right-icon" src="../../img/red-right-icon.png" mode=""/>
					<view class="topicList-diver" wx:if="{{data.quickMessageTopicList.length!=index+1}}"></view>
				</view>
				<text class="objname">{{model.objname}}</text>
				<text  class="{{model.changepct>=0?'changepctStr changepctStr-red':'changepctStr'}}">{{model.changepct>=0?'+'+model.changepctStr:model.changepctStr}}</text>
			</view>
			<view class="tag-item2"
						wx:if="{{data.indexList.length==1}}"
            wx:key="index"
            wx:for-index="index"
            wx:for-item="model"
            wx:for="{{data.articleProductList}}"
						data-item="{{model}}" 
						catch:tap="toProductDatail"
          >
					<text class="fundName">{{model.fundName}}</text>
					<view class="tag-diver"></view>
					<view class="fundCode">{{model.fundCode}}</view>
					<image class="red-right-icon" src="../../img/right-icon.png" mode=""/>
          </view>
			</view>

			</scroll-view>
			<scroll-view wx:if="{{data.indexList==null||data.indexList==''}}" scroll-x="true" class="{{data.quickMessageTopicList&&data.quickMessageTopicList.length>0?'scroll-view-tags':'scroll-view-tags tagList-vis'}}" catch:tap="catchtap">
			<view  class="{{data.quickMessageTopicList&&data.quickMessageTopicList.length>0?'tagList':'tagList tagList-vis'}}">
				<view class="tag-item"
            wx:key="index"
            wx:for-index="index"
            wx:for-item="model"
            wx:for="{{data.quickMessageTopicList}}"
						data-item="{{model}}" 
						catch:tap="toSpecial"
          >
						<text class="{{model.indexList[0].objname?'topicName':'topicName topicName-r'}}">{{'#'+model.topicName}}</text>
						<view class="tag-diver" wx:if="{{model.indexList[0].objname}}"></view>
						<view class="objname" wx:if="{{model.indexList[0].objname}}">{{model.indexList[0].objname?model.indexList[0].objname:'--'}}</view>
						<view class="{{model.indexList[0].changepct>=0?'changepctStr changepctStr-red':'changepctStr'}}" wx:if="{{model.indexList[0].objname}}">{{model.indexList[0].changepct>=0?'+'+model.indexList[0].changepctStr:model.indexList[0].changepctStr}}</view>
						<image class="red-right-icon" wx:if="{{model.indexList[0].objname}}" src="../../img/red-right-icon.png" mode=""/>
          </view>
					<view class="tag-item2"
						wx:if="{{data.quickMessageTopicList.length==1}}"
            wx:key="index"
            wx:for-index="index"
            wx:for-item="model"
            wx:for="{{data.articleProductList}}"
						data-item="{{model}}" 
						catch:tap="toProductDatail"
          >
					<text class="fundName">{{model.fundName}}</text>
					<view class="tag-diver"></view>
					<view class="fundCode">{{model.fundCode}}</view>
					<image class="red-right-icon" src="../../img/right-icon.png" mode=""/>
          </view>
			</view>
			</scroll-view>
			<scroll-view wx:if="{{((data.indexList==null||data.indexList=='')&&data.quickMessageTopicList.length!=1)||(data.indexList&&data.indexList.length>1)}}" class="{{data.articleProductList&&data.articleProductList.length>0?'scroll-view-tags scroll-view-tags2':'scroll-view-tags scroll-view-tags2 tagList-vis'}}" scroll-x="true">
			<view class="{{data.articleProductList&&data.articleProductList.length>0?'tagList tagList2':'tagList tagList2 tagList-vis'}}" >
					<view class="tag-item2"
            wx:key="index"
            wx:for-index="index"
            wx:for-item="model"
            wx:for="{{data.articleProductList}}"
						data-item="{{model}}" 
						catch:tap="toProductDatail"
          >
					<text class="fundName">{{model.fundName}}</text>
					<view class="tag-diver"></view>
					<view class="fundCode">{{model.fundCode}}</view>
					<image class="red-right-icon" src="../../img/right-icon.png" mode=""/>
          </view>
			</view>
		</scroll-view>
		</view>

	</view>
</view>