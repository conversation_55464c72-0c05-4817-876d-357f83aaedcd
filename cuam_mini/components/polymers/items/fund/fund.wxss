.box{
  display: flex;
  flex: 1;
  /* height: 70rpx; */
  flex-direction: row;
  margin-left: 20rpx;
  margin-right: 20rpx;
  margin-top: 28rpx;
  padding-bottom: 30rpx;
  border-bottom: 1rpx solid #EAEAEA;
}
.item-box{
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.item-title{
  color: #333333;
  font-weight: 500;
  font-size: 28rpx;
  line-height: 32rpx;
}
.item-code{
  color: #999999;
  font-size: 20rpx;
  line-height: 20rpx;
  margin-top: 18rpx;
}
.item-num{
  font-size: 28rpx;
  color: #333333;
  line-height: 28rpx;
}
.manger{
  font-size: 24rpx;
  color: #333333;
}
.item-tag{
  display: flex;
  flex-direction: row;
  align-items: center;
  background: linear-gradient(90deg,#FF1F2F, #FF5D5D);
  line-height: 28rpx;
  font-size: 20rpx;
  color: #fff;
  padding: 0rpx 10rpx;
  margin-top: 10rpx;
  border-radius: 4rpx;
}
.left-box{
  width: 60rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}
.start{
  width: 36rpx;
  height: 36rpx;
}