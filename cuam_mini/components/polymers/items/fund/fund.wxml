 <view class="box" bind:tap="onNewsAction"  data-item="{{showData}}">
    <view class="left-box" catch:tap="changeStartStatus" data-item="{{showData}}">
      <image wx:if="{{showData.isCareSelect}}" src="/imgs/icon/start-selected.png" class="start"/>
      <image wx:else src="/imgs/icon/start.png" class="start"/>
    </view>
    <view class="item-box"  style="flex:2;align-items:flex-start;margin-left:9rpx">
      <view class="item-title">{{showData.fundName}}</view>
      <view class="item-code" style="margin-top: 16rpx;">{{showData.fundCode}}</view>
    </view>
    <view class="item-box"  style="flex:1">
      <view class="item-num">{{showData.netValue?showData.netValue:'-'}}</view>
      <view class="item-code" wx:if="{{showData.netDate}}" style="margin-top:18rpx">({{showData.netShowDate}})</view>
    </view>
    <view class="item-box"  style="flex:1;">
      <view class="item-num" style="color:{{showData.showRange>0?'#FF2F25':showData.showRange==0 || showData.showRange == null?'#999999':'#00B54B'}};align-items:center;">{{showData.showRange>0?'+':''}}{{showData.showRangeValue}}</view>
      <view wx:if="{{showData.keepRiseDays&&showData.fundGroupTp != 1&&showData.fundGroupTp!=8&&showData.fundGroupTp != 2}}" class="item-tag">
        <image src="/imgs/whitearrowup.png" style="width:20rpx;height:20rpx;margin-right:2rpx;"/>
        连涨{{showData.keepRiseDays}}天
      </view>
      <view wx:if="{{showData.newHeight && showData.fundGroupTp != 1&&showData.fundGroupTp!=8&&showData.fundGroupTp != 2}}" class="item-tag">{{showData.newHeight}}</view>
    </view>
    <view class="item-box"  style="flex:1;align-items: flex-end;margin-right: 10rpx;">
      <view wx:for="{{showData.fundManagerList}}" wx:key="item">
        <view class="manger">{{item}}</view>
      </view>
    </view>
</view>