import {enums, format, global, storage,wbs,eventName} from "../../../../common/index.js";

import {
  geStorageUnionId,
  getCardRefreshMoment,
  getOpenId,
  getToken,
  getWechatInfoId,
  setWebPageShowWay
} from "../../../../common/utils/userStorage";

const {
  SEND_WEBVIEW_OPTIONS,
  SEND_REGISTER_OPTIONS,
  SEND_EVENT_TO_POLYMERS,
  SEND_REFRESH_TO_POLYMERS,
  REFRESH_PAGE_DATA
} = eventName

const {PAGE_INFO_REALM} = enums

const moment = require("../../../../lib/monment/index")
moment.locale('en', {
  longDateFormat: {
    l: "YYYY-MM-DD",
    L: "YYYY-MM-DD HH:mm"
  }
});

Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    realmType: {
      type: String,
      value: ''
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    },
    showBorder:{
      type:Boolean,
      value:true
    },
    sortList:{
      type: Array,
      value:[]
    }
  },
  data: {
    imgAnimate: 'fade_null',
    index:0,
    isLast:false,
    showData:[]
  },

  attached() {
    let {data = {},} = this.properties || {}
    // console.log('====== FUND _data >>>>', data)
    return this.installCardInfo(data)
  },

  methods: {
    onNewsAction(e) {
      // console.log('===== sensors_is_show this product >>> ', this.properties)
      // console.log('== sensors_is_show_message == news',this);
      // console.log('== sensors_is_show ==',wx.getStorageSync('unionid')??'用户ID_null');
      // console.log('== sensors_is_show ==',this.properties.showData?.sensorsdata?.fromTab || this.properties?.data?.page_name || '所属页面_null');
      // console.log('== sensors_is_show ==',this.properties?.cName || e.currentTarget.dataset?.item?.sensorsdata?.moduleTitle || '位置区_null');
      // console.log('== sensors_is_show ==',this.properties.showData?.sensorsdata?.ordinal+'');
      // console.log('== sensors_is_show ==',this.properties?.showData?.specialTopicName || this.properties?.mName || this.properties.showData?.sensorsdata?.secondaryTitle || this.properties?.data?.title_name || '二级标题_null');
      // console.log('== sensors_is_show ==',this.properties?.data?.fundName || this.properties?.data?.fund?.fundName || '产品名称_null');
      // console.log('== sensors_is_show ==',this.properties?.data?.fund?.fundCode || this.properties?.data?.fundCode || '产品代码_null');
      // 神策埋点业务逻辑
      // getApp().sensors.track('userClick',{
      //   pageName: this.properties.showData?.sensorsdata?.fromTab|| this.properties?.data?.page_name || '所属页面_null',
      //   cardName: this.properties?.cName || e.currentTarget.dataset?.item?.sensorsdata?.moduleTitle || '位置区_null',
      //   sequenceId: this.properties.showData?.sensorsdata?.ordinal+'',
      //   sub_position: this.properties?.showData?.specialTopicName || this.properties?.mName || this.properties.showData?.sensorsdata?.secondaryTitle || this.properties?.data?.title_name || '二级标题_null',
      //   fund_name: this.properties?.data?.fundName || this.properties?.data?.fund?.fundName || '产品名称_null',
      //   fund_code: this.properties?.data?.fund?.fundCode || this.properties?.data?.fundCode || '产品代码_null',
      //   button: '基金产品'
      // })
      // console.log('===== onNewsAction this.data >>> ', this.data)
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      const passParams = {
        ...item,
        action: `action://share/advFundProduct`,
      }

      // console.log('====== news passParams >>>', passParams)
      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, { bubbles: true, composed: true })
    },

    changeStartStatus(e){
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      const passParams = {
        ...item,
      }
      //提供给事件监听函数
      this.triggerEvent('onChangeStatus', passParams, { bubbles: true, composed: true })
    },
    
    installCardInfo(data = {},sortList=[]) {
      let temData = data
      temData.netShowDate = temData?.netDate?.slice(5) || ''
      temData.showRangeValue = temData.showRange?temData.showRange+'%':temData.showRange == 0?'0.00%':'-'
      temData.newHeight = ''
      switch(temData.fundNewRecordType){
        case "TAG_TWL":
           temData.newHeight = '近一年新高';
           break;
        case "TAG_SIX":
          temData.newHeight = '近六月新高';
          break
        case "TAG_THR":
          temData.newHeight = '近三月新高';
          break;
      }
      this.setData({
        index:data?.index || 0,
        showData:temData,
        isLast:data?.isLast || false 
      })
    },
    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },
  }
});
