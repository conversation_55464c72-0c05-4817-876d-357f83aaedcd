@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.media{
    background-color: #fff;
}
.media-block{
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin: 14px 14px 0 14px;
    padding-bottom: 14px;
    border-bottom: 1px solid rgba(238, 238, 238, 0.3);
}

.image{
    height: 74px;
    width: 132px;
    border-radius: 12rpx;
    margin-right: 10px;
}

.media-content-block{
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: space-between;
    flex: 1;
    /*padding-right: 28rpx;*/
}

.media-title{
    font-size: 14px;
    /* line-height: 22px; */
    color: #333;
    font-weight: 500;
    /* font-family: PingFang-Bold; */
}

.media-bottom{
    display: flex;
    flex-direction: column;
    /*align-items: flex-start;*/
    justify-content: flex-end;
    /*flex: 1*/
}

.media-bottom-txt{
    font-size: 24rpx;
    /* line-height: 16px; */
    color: #CCCCCC;
    font-weight: 400;
    margin-top: 6px;
    /* font-family: PingFang-Bold; */
}

.text-two-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-row-box{
    display: flex;
    /*flex: 1;*/
    flex-direction: row;
    align-items: center;
    /*margin-top: 14px;*/
}
.icon-time{
    margin-right: 10rpx;
    margin-bottom: -10rpx;
    color: #ccc;
}
.card-left-time{
    /* font-family: PingFang-Bold; */
    font-weight: 400;
    font-size: 12px;
    color: #969696;
    letter-spacing: 0;
    /* line-height: 16px; */
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}

