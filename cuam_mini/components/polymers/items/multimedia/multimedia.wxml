<view class='media'
      style="border-top-left-radius:{{(signalBlock && data.index===0)?12:0}}rpx; border-top-right-radius:{{(signalBlock && data.index==0)?12:0}}rpx; border-bottom-right-radius:{{data.isLast?12:0}}rpx; border-bottom-left-radius:{{data.isLast?12:0}}rpx;">
  <view class="media-block"
        data-item="{{data}}"
        bind:tap="onMediaAction"
        style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{data.isLast?'#fff':'rgba(238, 238, 238, 0.3)'}}">
    <image
        src="{{data.cover || data.avatar || ''}}?x-oss-process=image/resize,m_fill,h_280,w_496"
        mode="aspectFill"
        class="image {{imgAnimate}}"
    />

    <view class="media-content-block">
      <view class="media-title text-two-line">
        {{data.title || data.name || '-'}}
      </view>
      <view class="media-bottom">
        <view wx:if="{{data.timeCreatedStr || data.chapterName}}"
              class="card-row-box">
          <van-icon class="icon-time" name="underway-o" size="15px"/>
          <view wx:if="{{data.timeCreatedStr}}" class="card-left-time text-one-line">
            {{data.timeCreatedStr}} | 共{{data.countChapter || '-'}}章
          </view>

          <view wx:if="{{data.chapterName}}" class="card-left-time text-one-line">
            {{data.chapterName || '-'}}
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
