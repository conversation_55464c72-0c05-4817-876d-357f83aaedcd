<view class='card'
      style="border-top-left-radius:{{(signalBlock && data.index===0)?12:0}}rpx; border-top-right-radius:{{(signalBlock && data.index==0)?12:0}}rpx; border-bottom-right-radius:{{data.isLast?12:0}}rpx; border-bottom-left-radius:{{data.isLast?12:0}}rpx;">
  <view
      class="card-box"
      data-item="{{data}}"
      bind:tap="onMarketingAction"
      style="margin-top:{{(signalBlock && data.index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && data.index==0)?12:(isFromCard&&data.index!= 0)?14:0}}px;border-bottom-color: {{data.isLast || !showBorder ||!isFromCard?'#fff':'#eaeaea'}}">
    <image
        src="{{data.cover||data.avatar || data.coverImg || data.detailsPageImg}}?x-oss-process=image/resize,m_mfit,h_400,limit_0/crop,w_500,x_0,g_west"
        mode="aspectFill"
        class="image {{imgAnimate}}"
    />
    <view class="card-right">
      <text class="market-title-txt text-one-line">{{data.title || '-'}}</text>
      <view class="card-left-subtitle text-one-line">{{data.subTitle || data.summary || '-'}}</view>

      <view class="card-bottom-info">
        <view class="card-left-time text-one-line">{{data.shortDesc || data.guide || data.chapterName}}</view>
      </view>
    </view>
  </view>
</view>
