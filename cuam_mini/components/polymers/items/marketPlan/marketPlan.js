import {enums, global, storage} from "../../../../common/index.js";

const {PAGE_INFO_REALM} = enums

Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    data: {
      type: Object,
      value: {}
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    },  
    showBorder:{
      type:Boolean,
      value:true
    },
    type:{
      type:String,
      value:''
    }
  },

  data: {
    imgAnimate: 'fade_null'
  },

  attached() {
    // console.log('========== MARKETING_PLAN props >>>', this.properties)
    const {data = {}} = this.properties || {}
    const {id = ''} = data || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    // console.log('======== BBBB hashPool >>>>', !hashPool.includes(id))
    if (!hashPool.includes(id) && !!id){
      this.setData({
        imgAnimate: 'fade_in'
      })
      hashPool.push(id)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
  },

  methods: {
    onMarketingAction(e) {
      console.log('======= onMarketingAction e >>>', e, this.data.data);
      // 神策埋点
      getApp().sensors.track('userClick',{
        fund_name: this.data.data?.title || '内容标题null',
        content_name: this.data.data?.content_name || 'Null',
        content_title: this.data.data?.secondaryTitle || 'Null',
        page_title: this.data.data?.tabTitle || 'Null',
      })

      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}

      const {cName = '', cId = '', mName = ''} = this.data || {}
      const {realm = ''} = item || {}
      const {type = ''} = this.properties
      // console.log('realm===',PAGE_INFO_REALM['special_channel'])
      const passParams = {
        ...item,
        action: `action://share/${type || PAGE_INFO_REALM[realm] || 'advProductDataDetail'}`,
        cName,
        cId,
        mName,
      }
      // console.log('passParams===',passParams)

      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})
    }
  }
});
