@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.card{
    background-color: #fff;
}
.card-box{
    display: flex;
    flex-direction: row;
    margin: 28rpx 28rpx 0 28rpx;
    padding-bottom: 28rpx;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1rpx solid #EAEAEA;
}

.card-right{
    display: flex;
    flex-direction: column;
    flex: 1;
    align-items: flex-start;
    justify-content: flex-start;
    /* height: 140rpx; */
}

.market-title-txt{
    font-size: 30rpx;
    color: #333;
    font-weight: 500;
    /* font-family: PingFang-Bold; */
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-left-title{
    font-size: 36rpx;
    color: #333;
}

.card-left-subtitle{
    font-size: 26rpx;
    color: #666;
    margin-top: 10rpx;
    font-weight: 500;
    font-family: PingFangSC;
}

.card-bottom-info{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    justify-content: flex-start;
    flex: 1;
}

.card-left-time{
    font-size: 24rpx;
    margin-top: 10rpx;
    color: #999;
}

.image{
    width: 160rpx;
    height: 128rpx;
    border-radius: 12rpx;
    margin-right: 28rpx;
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}
