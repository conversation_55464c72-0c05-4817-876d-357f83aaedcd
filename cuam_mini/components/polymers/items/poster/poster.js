import {global, storage} from "../../../../common/index.js";

Component({
  properties: {
    item: {
      type: Object,
      value: {}
    },
  },
  data: {
    imgAnimate: 'fade_null'
  },
  attached() {
    const {item = {}} = this.properties || {}
    const {id = ''} = item || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    // console.log('======== hashPool >>>>', !hashPool.includes(id))
    if (!hashPool.includes(id) && !!id){
      this.setData({
        imgAnimate: 'fade_in'
      })
      hashPool.push(id)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
  },
  methods: {
    onPosterAction(e) {
      // console.log('====== onPosterAction e >>>', e)
    }
  }
});
