@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.card-box {
    display: flex;
    flex-direction: column;
    width: calc((100vw - 28px)/3);
    align-items: center;
    justify-content: center;
}

.poster-image{
    /* width: 220rpx; */
    height: 340rpx;
    margin-bottom: 12px;
    /*margin-right: 14px;*/
    /* border-radius: 12rpx; */
}

.poster-block{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    /* width: 220rpx; */
    height: 360rpx;
    border-radius: 12rpx;
    background-color: #ccc;
}

.poster-empty-img{
    /* width: 60rpx;
    height: 60rpx;
    border-radius: 8rpx; */
    height: 340rpx;
    margin-bottom: 12px;
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}
