Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    icon: {
      type: String,
      value: '../../../../imgs/empty/<EMAIL>'
    },
    iconWidth: {
      type: String,
      value: '20vw'
    },
    iconHeight: {
      type: String,
      value: '20vh'
    },
    tips: {
      type: String,
      value: ''
    },
    isContent: {
      type: Boolean,
      value: false
    },
    noTabBar: {
      type: Boolean,
      value: false
    },
    height: {
      type: Number,
      value: 300
    },
    fromHome:{
      type: Boolean,
      value: false
    }
  },
  data: {},
  methods: {
    doRefresh(e) {
      // console.log('===== doRefresh e >>>>', e)
    }
  }
});
