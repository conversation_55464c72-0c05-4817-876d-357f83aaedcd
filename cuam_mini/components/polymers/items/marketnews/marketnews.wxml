<view class="card">
  <view class="card-box" data-item="{{data}}" bind:tap="{{'onNewsAction'}}">
    <view class="time-line">
      <image wx:if="{{data.isFirst}}" src="/imgs/card/false-circle.png" class="time-dot"/>
      <image wx:else src="/imgs/card/true-circle.png" class="time-dot2"/>
    </view>
    <view class="right-box" style="margin-bottom:{{data.isLast?0:48}}rpx">
      <view class="time-text"><text style="margin-right:14rpx">{{data.day}}</text>{{data.time}}</view>
      <view  class="content-text">
        <text class='content-text-bottom' decode="{{true}}">{{data.title || data.comment || data.content || '-'}}</text>
      </view>
			<scroll-view wx:if="{{data.indexList||data.indexList.length>0}}" scroll-x="true" class="scroll-view-tags scroll-view-tags-index" catch:tap="catchtap">
			<view class="link-index-list">
				<view class="index-item"
				wx:key="index"
				wx:for-index="index"
				wx:for-item="model"
				wx:for="{{data.indexList}}"
				data-item="{{model}}" 
			>
				<view class="topicList" 
					wx:key="index"
					wx:for-index="index"
					wx:for-item="item"
					wx:for="{{data.quickMessageTopicList}}"
					data-item="{{item}}"
					catch:tap="toSpecial"
					>
					<text class="topicName">{{'#'+item.topicName}}</text>
					<image class="red-right-icon" src="../../img/red-right-icon.png" mode=""/>
					<view class="topicList-diver" wx:if="{{data.quickMessageTopicList.length!=index+1}}"></view>
				</view>
				<text class="objname">{{model.objname}}</text>
				<text  class="{{model.changepct>=0?'changepctStr changepctStr-red':'changepctStr'}}">{{model.changepct>=0?'+'+model.changepctStr:model.changepctStr}}</text>
			</view>
			<view class="tag-item2"
						wx:if="{{data.indexList.length==1}}"
            wx:key="index"
            wx:for-index="index"
            wx:for-item="model"
            wx:for="{{data.articleProductList}}"
						data-item="{{model}}" 
						catch:tap="toProductDatail"
          >
					<text class="fundName">{{model.fundName}}</text>
					<view class="tag-diver"></view>
					<view class="fundCode">{{model.fundCode}}</view>
					<image class="red-right-icon" src="../../img/right-icon.png" mode=""/>
          </view>
			</view>

			</scroll-view>
      <scroll-view  scroll-x="true" class="tags" wx:if="{{!data.indexList&&data.quickMessageTopicList&&data.quickMessageTopicList.length}}">
          <view class="topic-line">
            <view wx:for="{{data.quickMessageTopicList}}" wx:key="id" wx:for-item="item" wx:for-index="index">
              <template is="topic" data="{{item}}"></template>
            </view>
            <view wx:if="{{data.quickMessageTopicList.length<2 && (data.articleProductList || data.quickMessageProductList)}}"  wx:for="{{data.articleProductList || data.quickMessageProductList}}" wx:key="id" wx:for-item="item" wx:for-index="index">
              <template is="product" data="{{item}}"></template>
            </view>
          </view>
        </scroll-view>
        <scroll-view   wx:if="{{((data.indexList==null||data.indexList=='')&&data.quickMessageTopicList.length!=1)||(data.indexList&&data.indexList.length>1)}}"  scroll-x="true" class="tags" style="margin-bottom: 10rpx;">
          <view class="topic-line">
            <view wx:for="{{data.articleProductList || data.quickMessageProductList}}" wx:for-item="item" wx:for-index="index" wx:key="id">
              <template is="product" data="{{item}}"></template>
            </view>
          </view>
        </scroll-view>
    </view>
  </view>
</view>

<template name="product" data="{{item}}">
  <view class="product-box"  data-item="{{item}}"  catch:tap="toProductDatail">
    <view class="product-name">{{item.fundName}}</view>
    <view class="topic-name-line" style="background-color: #BFBFBF;"></view>
    <view class="product-name-code">{{item.fundCode}}</view>
    <image src="/imgs/card/gary-right-arrow.png" class="right-arrow"/>
  </view>
</template>

<template name="topic" data="{{item}}">
  <view class="topic-box"  data-item="{{item}}"  catch:tap="toSpecial">
    <text class="topic-name">#{{item.topicName}}</text>
    <view class="topic-name-line" wx:if="{{item.indexList}}" style="background-color: #FF1F1F;"></view>
    <text class="exponent"  wx:if="{{item.indexList}}" >{{item.indexList[0].objname}}</text>
    <text class="exponent-num"  wx:if="{{item.indexList}}"  style="color:{{item.indexList[0].changepct > 0 ? '#FE1F2C': item.indexList[0].changepct == 0 ?'#777777':'#00A758'}}">{{item.indexList[0].changepct > 0 ?'+':''}}{{item.indexList[0].changepctCode}}%</text>
    <image src="/imgs/card/red-right-arrow.png"  wx:if="{{item.indexList}}"  class="right-arrow"/>
  </view>
</template>