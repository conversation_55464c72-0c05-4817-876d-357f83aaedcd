@keyframes fadeIn {
	0% {opacity: 0;}
	100% {opacity: 1;}
}

.card-box{
	background-color:transparent;
	width: 100%;
	display: flex;
	flex: 1;
	flex-direction: row;
}
.time-line{
border-left: 2rpx dashed #D2D2D2;
padding: 0rpx;
}
.time-dot{
width: 15rpx;
height: 15rpx;
position: relative;
top: -18rpx;
left: -8rpx;
}
.time-dot2{
width: 17rpx;
height: 17rpx;
position: relative;
top: -18rpx;
left: -9rpx;
}
.right-box{
margin-left: 13rpx;
margin-bottom: 48rpx;
margin-top: -6rpx;
padding: 0;
}
.time-text{
color: #9F9F9F;
font-size: 22rpx;
line-height: 22rpx;
margin-left: 4rpx;
}
.content-text{
color: #222222;
font-size: 30rpx;
font-weight: 500;
margin-top: 10rpx;
width: 602rpx;
display: -webkit-box; /* 设置为弹性盒子布局 */
-webkit-line-clamp: 2; /* 设置最大行数 */
-webkit-box-orient: vertical; /* 设置为垂直方向排列 */
overflow: hidden; /* 设置溢出隐藏 */
text-overflow: ellipsis; /* 设置显示省略号 */
}
.content-text-bottom{
background: linear-gradient(#FFF0EF ,#FFF0EF) no-repeat;
/*调整下划线的宽度占百分之百  宽度是1px */
background-size: 100% 10rpx;
/* 调整下划线的起始位置 左侧是0 上边是1.15em */
background-position: 0 23rpx;
padding-left: 4rpx;
}
.tags {
display: flex;
flex-direction: row;
white-space: nowrap;
margin-top: 20rpx;
width: 647rpx;
}
.topic-line{
display: flex;
flex-direction: row;
}
.topic-box{
height: 38rpx;
border-radius: 4rpx;
background: linear-gradient(90deg, #fff0ef, #ffffff);
border: 1rpx solid #FED3D0;
display: flex;
align-items: center;
padding-left: 10rpx;
padding-right: 10rpx;
margin-right: 12rpx;
}
.product-box{
height: 38rpx;
border-radius: 4rpx;
background-color: #F7F7F7;
display: flex;
align-items: center;
padding-left: 10rpx;
padding-right: 10rpx;
margin-right: 12rpx;
}
.topic-name{
font-size: 22rpx;
color: #FF1F1F;
}
.topic-name-line{
width: 1rpx;
height: 14rpx;
margin-left: 9rpx;
margin-right: 9rpx;
}
.exponent{
font-size: 22rpx;
color: #777777;
}
.exponent-num{
font-size: 22rpx;
font-weight: 400;
margin-left: 11rpx;
}
.right-arrow{
width: 9rpx;
height: 14rpx;
margin-left: 12rpx;
}
.product-name{
color: #777777;
font-size: 22rpx;
/* max-width: 150rpx;
overflow: hidden;
text-overflow: ellipsis;
white-space: nowrap;  */
}
.product-name-code{
color: #777777;
font-size: 22rpx;
}
.scroll-view-tags-index{
width: 660rpx;
margin-top:20rpx;
}
.link-index-list{
display: flex;
align-items: center;
}
.index-item{
display: flex;
height: 40rpx;
background: linear-gradient(273deg, #FFFFFF 0%, #FFF1F0 100%);
border-radius: 4rpx;
border: 1rpx solid #FED3D0;
align-items: center;
flex-shrink: 0;
margin-right: 12rpx;
}
.index-item .objname{
height: 30rpx;
font-size: 22rpx;
font-family: PingFangSC, PingFang SC;
font-weight: 400;
color: #666666;
line-height: 30rpx;
padding-right: 10rpx;
}
.index-item .changepctStr{
height: 30rpx;
font-size: 22rpx;
font-family: PingFangSC, PingFang SC;
font-weight: 400;
color: #22B26E;
line-height: 30rpx;
padding-right: 10rpx;
}
.index-item  .changepctStr-red{
color: #fe1f2c;
}
.topicList{
display: flex;
align-items: center;
}
.topicList .topicName{
height: 30rpx;
font-size: 22rpx;
font-family: PingFangSC, PingFang SC;
font-weight: 400;
color: #FF1F1F;
line-height: 30rpx;
margin-right: 4rpx;
padding-left: 10rpx;
}
.topicList .red-right-icon{
width: 9rpx;
height: 14rpx;
margin-right: 12rpx;
}
.topicList .topicList-diver{
width: 1rpx;
height: 14rpx;
background:#FF1F1F;
}
.tagList-vis{
display: none!important;
}
.tag-item2{
height: 40rpx;
background: #F7F7F7;
border-radius: 4rpx;
flex-shrink: 0;
display: flex;
align-items: center;
margin-right: 12rpx;

}
.tag-item2 .fundName{
height: 22rpx;
font-size: 22rpx;
font-family: PingFang SC;
font-weight: 400;
color: #777777;
line-height: 22rpx;
margin-left: 11rpx;
}
.tag-item2 .tag-diver{
width: 1rpx;
height: 14rpx;
background: #BFBFBF;
margin-left: 11rpx;
}
.tag-item2 .fundCode{
width: 70rpx;
font-size: 20rpx;
font-family: PingFang SC;
font-weight: 400;
color: #B7B7B7;	
margin-left: 11rpx;
}
.tag-item2 .red-right-icon{
width: 9rpx;
height: 13rpx;
margin-left:20rpx ;
margin-right: 12rpx;
}