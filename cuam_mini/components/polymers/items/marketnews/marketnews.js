import {
  enums,
  format,
  global,
  storage,
  wbs,
  eventName,
  interaction,
  breakIn
} from "../../../../common/index.js";
import {
  geStorageUnionId,
  getCardRefreshMoment,
  getOpenId,
  getToken,
  getUserLoginStatus,
  getWechatInfoId,
  setWebPageShowWay
} from "../../../../common/utils/userStorage";
const { SEND_WEBVIEW_OPTIONS } = eventName;

const { PAGE_INFO_REALM, LABEL_NAME, BROKER_REPORT, LOGIN_VISITOR, BREAK_FUNC_NAME } = enums;

const moment = require("../../../../lib/monment/index");
moment.locale("en", {
  longDateFormat: {
    l: "YYYY-MM-DD",
    L: "YYYY-MM-DD HH:mm",
  },
});

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: "onHandleRefresh",
    },
    index:{
      type: Number,
      value:999,
      observer: "onHandleRefresh",
    },
    sensorsData: {
      type: Object,
      value: {},
      observer: "onHandleRefresh",
    },
    realmType: {
      type: String,
      value: "",
    },
    isFromCard: {
      type: Boolean,
      value: true,
    },
    signalBlock: {
      type: Boolean,
      value: false,
    },
    cId: {
      type: String | Number,
      value: "",
    },
    cName: {
      type: String,
      value: "",
    },
    mName: {
      type: String,
      value: "",
    },
  },
  data: {
    imgAnimate: "fade_null",
		isShowMore: false,
		tagList1:[{v1:'#光伏',v2:'中证光伏',v3:'12.5%'},{v1:'#光伏',v2:'中证光伏',v3:'12.5%'}],
		tagList2:[{y1:'汇添富纳斯达...',y2:'000032'},{y1:'汇添富纳斯达...',y2:'000032'}]
  },

  attached() {
    // console.log('======MARKET NEWS props >>>>', this.properties)
    const { data = {} } = this.properties || {};
    return this.installCardInfo(data);
  },

  methods: {
		catchtap(){
			console.log('catchtap')
		},
    onNewsAction(e) {
      // 神策埋点业务逻辑
      getApp().sensors.track("userClick", {
        pageName: this.data?.sensorsData?.fromTab ?? "所属页面null",
        content_name: this.data?.data?.content ?? "内容标题null",
        cardName: this.data?.sensorsData?.cardName ?? "位置区域null",
        labelName: this.data?.data?.labelName ?? "资讯形式null",
        sequenceId: this.data?.sensorsData?.ordinal + "",
        button: "文章",
      });
  
      const {
        dataset: { item = {} },
      } = e.currentTarget || {};

      const {
        data,
        cName = "",
        cId = "",
        mName = "",
        isShowMore,
      } = this.data || {};
      const { realm = "" } = item || {};

      if (item.type == 0) {
        this.setData({
          isShowMore: !isShowMore,
        });
      } else {
        const passParams = {
          ...item,
          action:`action://share/${BROKER_REPORT[item.type].action}`,
          cName,
          cId,
          mName,
          realm: BROKER_REPORT[item.type].realm,
        };

        console.log("====== news passParams >>>", passParams);
        //提供给事件监听函数
        this.triggerEvent("onItemClick", passParams, {
          bubbles: true,
          composed: true,
        });
      }
    },
    onShowMore(e) {
      const { isShowMore } = this.data;
      this.setData({
        isShowMore: !isShowMore,
      });
      // 神策埋点业务逻辑
      // console.log('== sensors_is_show 展开收起 ==',e?.currentTarget?.dataset);
      // console.log('== sensors_is_show 展开收起 ==',geStorageUnionId()??'用户ID_null');
      // console.log('== sensors_is_show 展开收起 ==',e?.currentTarget?.dataset?.sensorsdata?.fromTab??'所属页面null');
      // console.log('== sensors_is_show 展开收起 ==',e?.currentTarget?.dataset?.message?.title??'内容标题null');
      // console.log('== sensors_is_show 展开收起 ==',e?.currentTarget?.dataset?.sensorsdata?.cardName??'位置区域null');
      // console.log('== sensors_is_show 展开收起 ==',e?.currentTarget?.dataset?.message?.labelName??'资讯形式null');
      // console.log('== sensors_is_show 展开收起 ==',e?.currentTarget?.dataset?.sensorsdata?.ordinal+'');
      getApp().sensors.track("userClick", {
        pageName:
          e?.currentTarget?.dataset?.sensorsdata?.fromTab ?? "所属页面null",
        content_name:
          e?.currentTarget?.dataset?.message?.title ?? "内容标题null",
        cardName:
          e?.currentTarget?.dataset?.sensorsdata?.cardName ?? "位置区域null",
        labelName:
          e?.currentTarget?.dataset?.message?.labelName ?? "资讯形式null",
        sequenceId: e?.currentTarget?.dataset?.sensorsdata?.ordinal + "",
        button: isShowMore ? "收起" : "展开",
      });
    },
    toSpecial(e) {
      // 神策埋点业务逻辑
      // console.log('== sensors_is_show 分享 ==',e?.currentTarget?.dataset);
      // console.log('== sensors_is_show 分享 ==',geStorageUnionId()??'用户ID_null');
      // console.log('== sensors_is_show 分享 ==',e?.currentTarget?.dataset?.sensorsdata?.fromTab??'所属页面null');
      // console.log('== sensors_is_show 分享 ==',e?.currentTarget?.dataset?.item?.title??'内容标题null');
      // console.log('== sensors_is_show 分享 ==',e?.currentTarget?.dataset?.sensorsdata?.cardName??'位置区域null');
      // console.log('== sensors_is_show 分享 ==',e?.currentTarget?.dataset?.item?.labelName??'资讯形式null');
      // console.log('== sensors_is_show 分享 ==',e?.currentTarget?.dataset?.sensorsdata?.ordinal+'');
      getApp().sensors.track("userClick", {
        pageName:
          e?.currentTarget?.dataset?.sensorsdata?.fromTab ?? "所属页面null",
        content_name: e?.currentTarget?.dataset?.item?.title ?? "内容标题null",
        cardName:
          e?.currentTarget?.dataset?.sensorsdata?.cardName ?? "位置区域null",
        labelName: e?.currentTarget?.dataset?.item?.labelName ?? "资讯形式null",
        sequenceId: e?.currentTarget?.dataset?.sensorsdata?.ordinal + "",
        button: "分享",
      });

      const {
        dataset: { item = {} },
      } = e.currentTarget || {};
      let params = {
        token: getToken(),
        openid: getOpenId(),
        unionid: geStorageUnionId(),
        wechatInfoId: getWechatInfoId(),
      };
      // if(item.topicId){
      //   params.perfix = `${wbs.gfH5}/share/advSpecialTopic`
      //   params.value = item.topicId
      //   params.title = `${item.topicName}专题页`
      //   params.pageType = 'specialDetail'
      //   return wx.navigateTo({
      //     url: '/pages/common/webview/webPage',
      //     success(res) {
      //       res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params)
      //       setWebPageShowWay(1)
      //     }
      //   });
      // }else{
      //   getApp().event.emit("SHOWMODAL",item)
      // }
      if (item.topicId) {
        let userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);
        const hasLogin = !LOGIN_VISITOR.includes(userRole * 1);
        if(!hasLogin){
          return wx.navigateTo({
            url: '/pages/loginAndRegist/login/login',
            success(res) {}
          })
        }
        params.perfix = `${wbs.gfH5}/share/advSpecialTopic`;
        params.value = item.topicId;
        params.title = `${item.topicName}页`;
        params.pageType = "specialDetail";
      } else {
        params.perfix = `${wbs.gfH5}/share/advNewsShare`;
        params.value = item.id;
        params.title = item.label;
        params.pageType = "flashShare";
        params.cardTitle = item.cName;
      }
      const hasLogin = getUserLoginStatus();
      if(!hasLogin){
        return breakIn({name:BREAK_FUNC_NAME.goToLoginPage})
      }
      return wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
    },

    toProductDatail(e){
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      const passParams = {
        ...item,
        action: `action://share/advFundProduct`,
      }

      // console.log('====== news passParams >>>', passParams)
      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, { bubbles: true, composed: true })
    },

    installCardInfo(data = {}) {
			let temData = data
			if(temData.indexList&&temData.indexList.length>0){
				temData.indexList.forEach(el=>{
						el.changepctStr = parseFloat(el.changepct).toFixed(2)+'%'
				})
			}
      if(temData?.quickMessageTopicList){
        temData?.quickMessageTopicList?.map(value=>{
          if(value.indexList){
            value.indexList[0].changepctCode = parseFloat(value.indexList[0].changepct).toFixed(2)
          }
        })
      }
      this.setData({
        data:temData
      })
    },
    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal);
    },
  },
});
