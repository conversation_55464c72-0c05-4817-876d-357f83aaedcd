.box{
  width: 700rpx;
  height: 227rpx;
  background: linear-gradient(163deg, #ffecec80 0%, #FFFFFF 20%);
  border-radius: 8rpx;
  margin: 20rpx 24rpx 0;
}
.title-box{
  padding: 20rpx 27rpx 0;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
}
.title-line{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.title-line-box{
  /* width: 500rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; */
  width: 550rpx;
}
.title-img{
  width: 40rpx;
  height: 40rpx;
}
.title-img-right{
  width: 16rpx;
  height: 26rpx;
  margin-left: 20rpx;
}
.title-text{
  font-size: 34rpx;
  font-weight: 500;
  color: #333333;
  white-space: nowrap; /* 不换行 */
  overflow: hidden; /* 超出部分隐藏 */
  text-overflow: ellipsis; /* 使用省略号表示截断部分 */
}
.code{
  font-size: 26rpx;
  font-weight: 400;
  color: #999999;
  margin-top: 1rpx;
  margin-left: 27rpx;
}
.bottom-box{
  margin-top: 17rpx;
  margin-left: 27rpx;
  padding-left: 20rpx;
  padding-right: 26rpx;
  width: 674rpx;
  height: 74rpx;
  background: linear-gradient(270deg, #fffdfd00 0%, #fffaf975 46%, #FFF6F5 100%);
  border-radius: 8rpx;
  justify-content: space-between;
}
.clock-img{
  width: 22rpx;
  height: 22rpx;
  margin-right: 12rpx;
}
.bottom-title{
  font-size: 22rpx;
  font-weight: 400;
  color: #333333;
}
.range-num{
  font-size: 24rpx;
  font-weight: 500;
  color: #E32833;
}
.no-range-num{
  font-size: 22rpx;
  font-weight: 500;
  color: #999999;
}
.change-remind{
  width: 112rpx;
  height: 44rpx;
  border: 2rpx solid #666666;
  border-radius: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
  color: #666666;
}
.set-remind{
  width: 112rpx;
  height: 44rpx;
  background: #E81C1C;
  border: 2rpx solid #E32833;
  border-radius: 22rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
  color: #fff;
}