 <view class="box">
  <view class="title-box">
    <view class="title-line title-line-box" bind:tap="onNewsAction" data-item="{{showData}}">
      <view class="title-text">{{showData.showName}}</view>
      <image src="/imgs/icon/bd1.png" class="title-img-right" mode="aspectFill"/>
    </view>
    <image src="/imgs/icon/start-selected.png" mode="heightFix" class="title-img" bindtap="changeSelected"/>
  </view>
  <view class="code">{{showData.fundCode}}</view>
  <view class="title-line bottom-box" bindtap="clickRemind">
    <view class="title-line ">
      <image src="/imgs/icon/clock.png" class="clock-img"/>
      <view class="bottom-title">涨幅提醒：
        <text wx:if="{{showData.rangeNum}}" class="range-num"><text class="bottom-title">当前已设置</text>{{showData.rangeNum}}<text class="bottom-title">条提醒</text></text>
        <text wx:else class="no-range-num">未设置</text>
    </view>
    </view>
    <!-- wx:if="{{!showData.isNewFund}}" -->
    <view>
        <view wx:if="{{showData.rangeNum}}" class='change-remind'>
          修改提醒
        </view>
        <view wx:else class="set-remind">
          设置提醒
        </view>
    </view>
  </view>
</view>