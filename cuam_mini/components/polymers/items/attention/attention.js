import {enums, format, global, storage,wbs,eventName} from "../../../../common/index.js";

import {
  geStorageUnionId,
  getCardRefreshMoment,
  getOpenId,
  getToken,
  getWechatInfoId,
  setWebPageShowWay
} from "../../../../common/utils/userStorage";

const {
  SEND_WEBVIEW_OPTIONS,
  SEND_REGISTER_OPTIONS,
  SEND_EVENT_TO_POLYMERS,
  SEND_REFRESH_TO_POLYMERS,
  REFRESH_PAGE_DATA
} = eventName

const {PAGE_INFO_REALM} = enums

const moment = require("../../../../lib/monment/index")
moment.locale('en', {
  longDateFormat: {
    l: "YYYY-MM-DD",
    L: "YYYY-MM-DD HH:mm"
  }
});

const NewFundType = [0,-1,999]

Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    realmType: {
      type: String,
      value: ''
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    },
    showBorder:{
      type:Boolean,
      value:true
    },
    sortList:{
      type: Array,
      value:[]
    }
  },
  data: {
    showData:[]
  },

  attached() {
    let {data = {},} = this.properties || {}
    // console.log('====== Attention _data >>>>', data)
    return this.installCardInfo(data)
  },

  methods: {
    onNewsAction(e) {
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      const passParams = {
        ...item,
        action: `action://share/advFundProduct`,
      }

      // console.log('====== news passParams >>>', passParams)
      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, { bubbles: true, composed: true })
    },

    clickRemind(e){
      const {showData} = this.data
      // 新发基金也可以设置提醒
      // if(showData.isNewFund)
      //   return;
      //提供给事件监听函数
      this.triggerEvent('onClickRemind', showData, { bubbles: true, composed: true })
    },

    changeSelected(e){
      const {showData} = this.data
      //提供给事件监听函数
      this.triggerEvent('onCancelCard', showData, { bubbles: true, composed: true })
    },

    installCardInfo(data = {},sortList=[]) {
      let temData = data
      temData.showName = temData.fundName.length > 16 ? temData.fundName.slice(0,16) + '...': temData.fundName
      temData.isNewFund = NewFundType.includes(data.fundStatus)
      this.setData({
        showData:temData,
      })
    },
    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },
  }
});
