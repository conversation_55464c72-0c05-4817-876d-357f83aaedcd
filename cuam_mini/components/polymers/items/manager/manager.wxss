@keyframes fadeIn {
  0% {
    opacity: 0;
  }

  100% {
    opacity: 1;
  }
}

.card {
  background-color: #fff;
}

.card-box {
  display: flex;
  flex-direction: column;
  margin: 14px 14px 0 14px;
  padding-bottom: 30rpx;
  /* align-items: center; */
  border-bottom: 1rpx solid #eaeaea;
}

.top_view {
  display: flex;
  flex: 1;
  width: 100%;
  flex-direction: row;
  align-items: center;
}

.img_header {
  width: 128rpx;
  height: 128rpx;
  border-radius: 12rpx;
  /* background-color: blue; */
}

.header_name {
  /* font-family: PingFang-Bold; */
  font-weight: 500;
  font-size: 32rpx;
  color: #333333;
  letter-spacing: 0;
  line-height: 29rpx;
  margin-right: 20rpx;
  display: block;
}

.top_name_view {
  display: flex;
  align-items: center;
  width: 100%;
  flex-direction: row;
}

.tag {
  /* font-family: PingFang-Bold; */
  font-weight: 400;
  font-size: 20rpx;
  color: #E8340F;
  letter-spacing: 0;
  line-height: 36rpx;
  padding: 4rpx 10rpx 4rpx 10rpx;
  background-color: rgba(232, 52, 15, 0.15);
  border-radius: 8rpx;
  margin-left: 24rpx;
}

.header_desc {
  /* font-family: PingFang-Bold; */
  font-weight: 400;
  font-size: 26rpx;
  color: #666666;
  letter-spacing: 0;
  text-align: justify;
  line-height: 40rpx;
  margin-top: 16rpx;
}
.header_desc--degree {
  
  width: 100%;
  display: flex;
  justify-content: space-between;
  
}
.header_degree {
  /* width: 70%; */
  height: 32rpx;
  line-height: 32rpx;
  font-size: 26rpx;
  color: #999999;
  margin: 16rpx 0 16rpx 0;
}
.header_product {
  height: 40rpx;
  width: 100%;
  display: flex;
  justify-content: space-between;
  background: linear-gradient(90deg, rgba(255,239,228,100), rgba(255,255,255,100));
  padding: 0rpx 9rpx ;
  font-size: 26rpx;
}
.header_desc--product view:first-child{
  width: 70%;
  color: #333;
}
.header_desc--product view:last-child{
  width: 30%;
  text-align: right;
  color: #999;
}

.center {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-top: 28rpx;
  flex-wrap: wrap;
  /* margin-bottom: 28rpx; */
}

.center_text {
  font-weight: 400;
  font-size: 24rpx;
  color: #999;
  letter-spacing: 0;
  text-align: center;
  line-height: 28rpx;
  width: 50%;
  height: 40rpx;
  display: flex;
  align-items: flex-start;
}
.center_text text:nth-child(1) {
  text-align: left;
  width: 140rpx;
  /* text-indent: 20rpx; */
}
.center_text text:nth-child(2) {
  flex: 1;
  text-align: left;
}
.bottom {
  display: flex;
  flex: 1;
  background-color: #FFF8F3;
  padding: 12rpx 20rpx;
  border-radius: 10rpx;
  margin-top: 18rpx;
  align-items: flex-start;
}

.bottom_img {
  width: 30rpx;
  height: 30rpx;
  margin-right: 18rpx;
}

.bottom_text {
  display: flex;
  flex: 1;
  font-weight: 400;
  font-size: 20rpx;
  line-height: 30rpx;
  color: #B36428;
  letter-spacing: 0;
  /* padding: 5rpx 0rpx 5rpx 0rpx; */
  border-radius: 5rpx;
  vertical-align:bottom;
}
.text-two-line{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  text-overflow: ellipsis;
}
.text-one-line{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  text-overflow: ellipsis;
}
.tag-scroll{
  display: flex;
  align-items: center;
  width: 70%;
  white-space:nowrap;
}
.tag-item{
  line-height: 28rpx;
  font-weight: 400;
  font-size: 20rpx;
  color: rgba(255, 31, 31, 0.8);
  /* letter-spacing: 5rpx; */
  padding: 0rpx 8rpx;
  background-color: rgba(255,240,239,1);
  border-radius: 8rpx;
  margin-right: 18rpx;
  word-spacing: 2rpx;
}
.img_header_right{
  margin-left: 28rpx;
  width: calc(100% - 156rpx);
}