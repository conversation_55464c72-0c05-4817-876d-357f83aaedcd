import {enums, format, global, storage} from "../../../../common/index.js";

const {PAGE_INFO_REALM} = enums

const moment = require("../../../../lib/monment/index")
moment.locale('en', {
  longDateFormat: {
    l: "YYYY-MM-DD",
    L: "YYYY-MM-DD HH:mm"
  }
});

Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    realmType: {
      type: String,
      value: ''
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    }
    ,
    showBorder:{
      type:Boolean,
      value:true
    }
  },
  data: {
    imgAnimate: 'fade_null',
    index:0,
    isLast:false,
    showData:[]
  },

  attached() {
    // console.log('====== MANAGER props >>>>', this.properties)
    let {data = {}} = this.properties || {}
    // console.log('====== NEWS _data >>>>', data)
    return this.installCardInfo(data)
  },

  methods: {
    onNewsAction(e) {
      console.log('= sensors_is_show manager> 埋点',this.data.data)
      // console.log('= sensors_is_show manager> sensorsdata数据',this);
      // // 神策埋点业务逻辑
      // console.log('sensors_is_show 用户',wx.getStorageSync('unionid')??'用户ID_null');
      // console.log('sensors_is_show 页面',e.currentTarget.dataset.item?.sensorsdata?.fromTab??'所属页面null');
      // console.log('sensors_is_show 位置',e.currentTarget.dataset.item?.sensorsdata?.secondaryTitle || this.__data__?.mName || '二级标题null');
      // console.log('sensors_is_show 页面',e.currentTarget.dataset.item?.sensorsdata?.ordinal+'');
      // console.log('sensors_is_show 页面',this.__data__?.mName || e.currentTarget.dataset?.item?.sensorsdata?.moduleTitle);
      // console.log('sensors_is_show 页面',e.currentTarget.dataset.item?.fundMgrName || e.currentTarget.dataset.item?.sensorsdata?.contentTitle);
      
      getApp().sensors.track('userClick',{
        pageName: e.currentTarget.dataset.item?.sensorsdata?.fromTab??'所属页面null',
        cardName: this.__data__?.cName || e.currentTarget.dataset?.item?.sensorsdata?.moduleTitle || '位置区域null',
        sequenceId: e.currentTarget.dataset.item?.sensorsdata?.ordinal+'',
        sub_position: this.__data__?.mName || e.currentTarget.dataset.item?.sensorsdata?.secondaryTitle || '二级标题null',
        manager_name:  e.currentTarget.dataset.item?.fundMgrName || e.currentTarget.dataset.item?.sensorsdata?.contentTitle,
        button: '基金经理',
        content_name: this.data.data?.content_name || 'Null',
        content_title: this.data.data?.secondaryTitle || 'Null',
        page_title: this.data.data?.tabTitle || 'Null',
      })
      // console.log('===== onNewsAction e >>> ', e)
      // console.log('===== onNewsAction this.data >>> ', this.data)
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}

      const {data, cName = '', cId = '', mName = ''} = this.data || {}
      const {realm = ''} = item || {}

      const passParams = {
        ...item,
        action: `action://share/${'advFundManager'}`,
        cName,
        cId,
        mName,
      }

      // console.log('====== news passParams >>>', passParams)
      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})
    },
    installCardInfo(data = {}) {
      if(data == null){
        return this.setData({showData:null})
      }
      let temData =  data
      // let temData = data
      temData.showManageScale = parseFloat(temData.manageScale/100000000).toFixed(2)
      temData.investDateNum = data?.investDateNum || data?.fundMgr?.investDateNum || ''
      temData.entranceReturn =  data?.entranceReturn || data?.fundMgr?.entranceReturn || ''
      // temData.goodFundCode =  data?.goodFundCode || data?.fundMgr?.goodFundCode || ''
      temData.fundMgrTag = temData?.fundMgrOperateTag?.split(',')
      temData.dateOfWorking = temData?.dateOfWorkingNum>1? data.dateOfWorkingNum +'年':data.dateOfWorkingNum<1?'不足一年':'-'
      // todo 任职回报 查询
      this.setData({
        index:data?.index || 0,
        showData: temData,
        isLast:data?.isLast || false 
      })
    },
    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    }
  }
});
