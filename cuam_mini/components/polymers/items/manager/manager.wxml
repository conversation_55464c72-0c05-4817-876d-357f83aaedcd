<view class="card"  wx:if="{{showData != null}}"  style="border-top-left-radius:{{index==0?16:0}}rpx; border-top-right-radius:{{ index==0?16:0}}rpx; border-bottom-right-radius:{{isLast?16:0}}rpx; border-bottom-left-radius:{{isLast?16:0}}rpx;">
  <view class="card-box" data-item="{{showData}}" bind:tap="onNewsAction" 
  style="margin-top:{{(signalBlock && index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && showData.index==0)?12:(isFromCard&&showData.index!= 0)?14:0}}px;border-bottom-color: {{isLast || !showBorder?'#fff':'#eaeaea'}}">
    <view class="top_view">
      <image class="img_header" src="{{showData.avatar}}"/>
      <view class="img_header_right">
        <view class="top_name_view">
          <view class="header_name">{{showData.fundMgrName}}</view>
          <scroll-view wx:if="{{showData.fundMgrOperateTag}}" scroll-x="true" class="tag-scroll">
            <view style='display:flex;flex-direction:row'>
              <view wx:for="{{showData.fundMgrTag}}">
               <view class="tag-item">{{item}}</view>
              </view>
            </view>
          </scroll-view>
        </view>
        <view class="header_degree text-one-line">{{showData.educationalBackground || '-'}}</view>
        <view class="header_product">
          <view class="text-one-line">代表产品：{{showData.goodFundName}}</view>
          <view style="color: #999;font-size: 22rpx;">{{showData.goodFundCode}}</view>
        </view>
      </view>
    </view>
    <view class="center">
      <view class="center_text">
        <text>{{'从业年限：'}}</text>
        <text style="color: #333;font-weight:500">{{  showData.dateOfWorking}}</text>
      </view>
      <view class="center_text">
        <text>{{'投资年限：'}}</text>
        <text style="color: #333;font-weight:500">{{showData.investDateNum ? (showData.investDateNum + '年') : '不足一年'}}</text>
      </view>
      <view class="center_text">
        <text>{{'管理规模：'}}</text>
        <text style="color: #FF1F1F;font-weight:500">{{showData.showManageScale}}亿元</text>
      </view>
      <view class="center_text">
        <text>{{'任职回报：'}}</text>
        <text style="color: {{showData.entranceReturn>0?'#FF1F1F':showData.entranceReturn<0?'#009802':'#999'}};font-weight:500">{{showData.entranceReturn>0?'+':''}}{{showData.entranceReturn || '0.00'}}%</text>
      </view>
    </view>
    <view class="bottom" wx:if="{{showData.fundMgrReview}}">
      <image src="/imgs/tips.png" class="bottom_img" />
      <view class="bottom_text text-two-line">{{showData.fundMgrReview}}</view>
    </view>
  </view>
</view>