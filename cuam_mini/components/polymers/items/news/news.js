import {enums, format, global, storage,wbs,eventName} from "../../../../common/index.js";
import {
  geStorageUnionId,
  getCardRefreshMoment,
  getOpenId,
  getToken,
  getWechatInfoId,
  setWebPageShowWay
} from "../../../../common/utils/userStorage";
const {
  SEND_WEBVIEW_OPTIONS,
  SEND_REGISTER_OPTIONS,
  SEND_EVENT_TO_POLYMERS,
  SEND_REFRESH_TO_POLYMERS,
  REFRESH_PAGE_DATA
} = eventName

const {PAGE_INFO_REALM,LABEL_NAME, ARTICLE_TYPE, BROKER_REPORT} = enums

const moment = require("../../../../lib/monment/index")
moment.locale('en', {
  longDateFormat: {
    l: "YYYY-MM-DD",
    L: "YYYY-MM-DD HH:mm"
  }
});

Component({
  options: {
    addGlobalClass: true
  },
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    realmType: {
      type: String,
      value: ''
    },
    isFromCard: {
      type: Boolean,
      value: false
    },
    signalBlock: {
      type: Boolean,
      value: false
    },
    cId: {
      type: String | Number,
      value: ''
    },
    cName: {
      type: String,
      value: ''
    },
    mName: {
      type: String,
      value: ''
    },
    newsType:{
      type: String,
      value: 'ARTICLE'
    },
    cardName:{
      type: String,
      value: ''
    },
    showBorder:{
      type:Boolean,
      value:true
    },
    hasLogin:{
      type:Boolean,
      value:true
    }
  },
  data: {
    imgAnimate: 'fade_null',
    index:0,
    isLast:false,
    newsType:'',
    cardName:''
  },

  attached() {
    // console.log('====== NEWS props >>>>', this.properties)
    let {data = {},newsType = '',cardName='',hasLogin} = this.properties || {}
    this.data.newsType = newsType
    this.data.cardName = cardName
    this.setData({
      index:data?.index || 0,
      isLast:data?.isLast || false,
      hasLogin
    })
    return this.installCardInfo(data)
  },

  methods: {
    onNewsAction(e) {
      // console.log('== sensors_is_show_message == news',e.currentTarget.dataset.item?.sensorsdata??'sensorsdata数据丢失---》》》》');
      // console.log('== sensors_is_show == news数数据',e.currentTarget.dataset);
      // console.log('== sensors_is_show == news数数据-this',this.data.data);
      // console.log('== sensors_is_show ==',geStorageUnionId()??'用户ID_null');
      // console.log('== sensors_is_show ==',e.currentTarget.dataset?.item?.sensorsdata?.fromTab || this.__data__.data?.page_name ||'所属页面_null');
      // console.log('== sensors_is_show ==',e.currentTarget.dataset?.item?.sensorsdata?.contentTitle || e.currentTarget.dataset?.item?.title || '内容标题_null');
      // console.log('== sensors_is_show == cardName',e.currentTarget.dataset?.item?.cName || this.__data__.cName || e.currentTarget.dataset?.item?.sensorsdata?.moduleTitle || this.__data__.data?.title_name || '位置区_null');
      // console.log('== sensors_is_show ==',e.currentTarget.dataset?.item?.sensorsdata?.ordinal+'');
      // console.log('== sensors_is_show ==',e.currentTarget.dataset?.item?.mName || this.__data__.data?.categoryName || e.currentTarget.dataset?.item?.sensorsdata?.secondaryTitle || '二级标题null');
      // 神策埋点业务逻辑
      getApp().sensors.track('userClick',{
        pageName: e.currentTarget.dataset?.item?.sensorsdata?.fromTab || this.__data__.data?.page_name ||'所属页面_null',
        content_name: e.currentTarget.dataset?.item?.title || e.currentTarget.dataset?.item?.sensorsdata?.contentTitle || '内容标题_null',
        cardName: e.currentTarget.dataset?.item?.cName || this.__data__.cName || e.currentTarget.dataset?.item?.sensorsdata?.moduleTitle || this.__data__.data?.title_name || '位置区_null',
        sequenceId: e.currentTarget.dataset?.item?.sensorsdata?.ordinal+'',
        button: '文章',
        exposed: '资讯文章',
        sub_position: e.currentTarget.dataset?.item?.mName || this.__data__.data?.categoryName || e.currentTarget.dataset?.item?.sensorsdata?.secondaryTitle || '二级标题null',
        title: this.data.data?.content_name || 'Null',
        content_title: this.data.data?.secondaryTitle || 'Null',
        page_title: this.data.data?.tabTitle || 'Null',
      })
      // console.log('===== onNewsAction e >>> ', e)
      // console.log('===== onNewsAction this.data >>> ', this.data)
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}

      const {data, cName = '', cId = '', mName = '',newsType=''} = this.data || {}
      const {realm = ''} = item || {}
      console.log('realm===',realm)

      const passParams = {
        ...item,
        action: realm ?`action://share/${PAGE_INFO_REALM[realm]}`:item.type?`action://share/${BROKER_REPORT[item.type].action}`:`action://share/AppAdvNewsDetail`,
        cName,
        cId,
        mName,
        realm : realm || BROKER_REPORT[item.type]?.realm || 'news',
      }

      // console.log('====== news passParams >>>', passParams)
      //提供给事件监听函数
      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})
    },
    toSpecial(e){
      const {hasLogin} = this.data
      if(!hasLogin){
        return wx.navigateTo({
          url: '/pages/loginAndRegist/login/login',
          success(res) {}
        })
      }
      const {
        dataset: {
          item = {}
        }
      } = e.currentTarget || {}
      let params = {
        token: getToken(),
        openid: getOpenId(),
        unionid: geStorageUnionId(),
        wechatInfoId: getWechatInfoId(),
      }
      params.perfix = `${wbs.gfH5}/share/advSpecialTopic`
      params.value = item.topicId || item.item.topicId
      params.title = `${item.topicName || item.item.topicName}页`
      params.pageType = 'specialDetail'
      return wx.navigateTo({
        url: '/pages/common/webview/webPage',
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params)
          setWebPageShowWay(1)
        }
      });
    },
    toPermission(e){
      console.log('eeee',e)
    },

    toList(){
      const { showData } = this.data
      let id = showData.fundIds.join(',')
      return wx.navigateTo({
        url: `/pages/home/<USER>/list?type=fund&categoryIds=${id}&name=产品推荐&isAboutProductList=true`
      })
    },

    installCardInfo(allParams = {}) {
      // if(allParams.dateTime == null && this.properties.isFromCard && allParams.realm != 'broker_week_finesse'){
      //   return this.setData({showData:null})
      // }
      // if(allParams.item){
      //   allParams.item.cardTitle = allParams.cardTitle
      // }
      // let params = allParams?.item || allParams
      // let params = allParams
      // if(params.realm == 'broker_week_finesse'){
      //   params = allParams.headlineModel || {}
      //   params.realm = allParams.realm
      // }
      let params = ''
      if(allParams.realm == ARTICLE_TYPE.ARTICLE || !allParams.realm){
        params = allParams
      }else if(allParams.realm == ARTICLE_TYPE.DEPTH_REPORT){
        params = allParams.selectedResearchReportModel
      }else if(allParams.realm == ARTICLE_TYPE.MEET_ESSENCE){
        params = allParams.morningBriefingModel
      }else if(allParams.realm == ARTICLE_TYPE.DEPTH_REPORT_MODULE){
        params = allParams.depthReportModel
      }else if(allParams.realm == ARTICLE_TYPE.BROKER_WEEK_FINESSE){
        params = allParams.dataInterpretationModel
      }
      if(!params){
        return
      }
      params.realm = allParams.realm
      params.labelName = params.labelType?LABEL_NAME[params.labelType]?.name:params.copyright?LABEL_NAME[params.copyright]?.name:''
      params.labelColor = params.labelType?LABEL_NAME[params.labelType]?.textColor:params.copyright?LABEL_NAME[params.copyright]?.textColor:''
      params.labelBGColor = params.labelType?LABEL_NAME[params.labelType]?.bgColor:params.copyright?LABEL_NAME[params.copyright]?.bgColor:''
      params.showTime = params.timePublished?params?.timePublished?.slice(0,10) : params.timeCreatedStr || params.shortDesc?.slice(0,10) || params?.timeCreated?.slice(0,10) ||'-'
      params.orgCName = params.orgName?params.orgName.length > 6?params.orgName.slice(0,6)+'...':params.orgName:''
      params.authorName = params.author? params.author.length>7?params.author.slice(0,7)+'...':params.author :''
      if(allParams.linkType == 'DEPTH_REPORT'){
        params.showTime = allParams.shortDesc || allParams.timeCreatedStr
      }
      this.setData({
        showData:params
      })
    },
    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },
  }
});
