<view class="card"
      wx:if="{{showData != null}}" 
      style="border-top-left-radius:{{signalBlock && index===0?16:0}}rpx; border-top-right-radius:{{signalBlock &&index==0?16:0}}rpx; border-bottom-right-radius:{{isLast?16:0}}rpx; border-bottom-left-radius:{{isLast?16:0}}rpx;">
  <view 
      wx:if="{{showData.linkType == 'DEPTH_REPORT' || cardName == 'securitiesCompaniesReport' || showData.realm == 'depth_report' || showData.realm == 'broker_week_finesse' || showData.realm == 'depth_report_module' || showData.realm == 'meet_essence'}}"
      class="card-box"
      data-item="{{showData}}"
      bind:tap="onNewsAction"
      style="margin-top:{{(signalBlock && index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && index==0)?12:(isFromCard&& index!= 0)?14:0}}px;border-bottom-color: {{isLast || !showBorder?'#fff':'#eaeaea'}};">
    <view class="card-left">
      <view class="card-left-title text-two-line"><text class="card-left__title no-padding">{{showData.title ||  showData.label ||showData.comment || '-'}}</text></view>
    </view>
    <view wx:if="{{showData.summary || showData.subTitle}}" class='second-title text-two-line'>
      {{showData.summary || showData.subTitle}}
    </view>
    <view class="card-row-box">
        <view class="org-name" wx:if="{{showData.orgName}}">
          {{showData.orgCName}}
        </view>
       <view class="card-left-time">
        {{showData.authorName}} {{showData.author?'    |    ':''}}  {{showData.showTime}}
          <!-- <view data-item="{{showData}}" catchtap="{{'toPermission'}}" wx:if="{{cardName == 'securitiesCompaniesReport'}}">
            <image src="../../../../imgs/home_share.png" class='share-img'/>            
          </view> -->
        </view>
        <scroll-view  wx:if="{{showData.quickMessageTopicList.length || showData.topicList.length}}"  scroll-x="true" class="tags">
          <view class="topic-line">
            <view  wx:for="{{showData.quickMessageTopicList || showData.topicList}}" wx:key="id">
            <view  data-item="{{item}}" 
                class='topicName'  
                style="color:{{item.status === 'PUBLISHED'?'#F04E41':'#999999'}}" 
                catchtap="{{item.status === 'PUBLISHED'?'toSpecial':''}}">{{item.status === "PUBLISHED"?'#':'//'}}{{item.topicName}} <text wx:if="{{index != showData.quickMessageTopicList.length - 1 && index != showData.topicList.length - 1}}" class="parting-line">|</text>
            </view>
          </view>
          </view>
        </scroll-view>


    </view>
  </view>
  <view
      wx:else
      class="card-box"
      data-item="{{showData}}"
      data-sensorsdata="{{ sensorsdata }}"
      bind:tap="onNewsAction"
      style="margin-top:{{(signalBlock && index==0)?-6:(isFromCard?0:14)}}px;padding-top:{{(signalBlock && index==0)?12:(isFromCard&& index!= 0)?14:0}}px;border-bottom-color: {{isLast || !showBorder?'#fff':'#eaeaea'}}">

    <view class="card-left">
      <view class="card-left-title text-two-line">
        <text wx:if="{{showData.labelName&&showData.labelName != '无'&& showData.type != 2}}" class="copyright-txt" style='color:{{showData.labelColor}};background:{{showData.labelBGColor}}'>{{showData.labelName}}</text>
        <text class="{{(showData.labelName&&showData.labelName != '无' && showData.type != 2) ? 'card-left__title' : 'card-left__title no-padding'}}">{{showData.title || showData.comment || '-'}}</text>
      </view>
    </view>

    <view class="card-row-box">
        <!-- <text wx:if='{{showData.topicName}}' data-item="{{showData}}" class='topicName'  style="color:{{showData.topicStatus === 'PUBLISHED'?'#F04E41':'#999999'}}" catchtap="{{showData.topicStatus === 'PUBLISHED'?'toSpecial':''}}">{{showData.topicStatus === "PUBLISHED"?'#':'//'}}{{showData.topicName}}</text>
        <text></text> -->
        <view class="card-left-time text-two-line ">
          {{showData.showTime}}
        </view>
        <view  wx:if="{{showData.quickMessageTopicList.length || showData.topicList.length}}" style="max-width: {{showData.existFund?300:400}}rpx;">
          <scroll-view  scroll-x="true" class="tags" >
            <view class="topic-line">
              <view  wx:for="{{showData.quickMessageTopicList || showData.topicList}}" wx:key="id">
              <view class="topic-line" style="align-items: center;">
                <view  data-item="{{item}}" 
                    class='topicName'  
                    style="color:{{item.status === 'PUBLISHED'?'#F04E41':'#999999'}}" 
                    catchtap="{{item.status === 'PUBLISHED'?'toSpecial':''}}">{{item.status === "PUBLISHED"?'#':'//'}}{{item.topicName}} 
                </view>
                <view wx:if="{{index != showData.quickMessageTopicList.length - 1  && index != showData.topicList.length - 1}}" class="parting-line"></view>
              </view>
            </view>
            </view>
          </scroll-view>
        </view>
        <view wx:if="{{showData.existFund == true ||  showData.existFund == 'true'}}" class="about-product" catchtap="{{'toList'}}" style="margin-left:{{showData.quickMessageTopicList.length || showData.topicList.length?20:0}}rpx">相关产品推荐</view>
    </view>
  </view>
</view>
