@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.card{
    background-color: #fff;
}
.card-box{
    display: flex;
    flex-direction: column;
    margin: 14px 14px 0 14px;
    padding-bottom: 14px;
    /* align-items: center; */
    border-bottom: 1rpx solid #eaeaea;
}

.card-row-box{
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
    /* justify-content: space-between; */
    /* align-self: flex-end; */
    margin-top: 26rpx;
}

.card-left{
    display: flex;
    flex: 1;
    align-items: flex-start;
}

.text-two-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-one-line{
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.card-subtitle-txt{
    font-size: 24rpx;
    color: #999;
    font-weight: 400;
    margin-top: 6rpx;
    /* font-family: PingFang-Bold; */
}

.card-left-title{
    font-size: 30rpx;
    color: #333;
    /* font-weight: 500; */
    /* font-family: PingFang-Bold; */ 
    letter-spacing: 0;
    line-height: 40rpx;
    letter-spacing: 0;
    position: relative;
}

.card-left-time{
    font-size: 24rpx;
    font-weight: 400;
    color: #969696;
    display: flex;
    margin-right: 26rpx;
    display: flex;
    flex-direction: row;
    align-items: center; 
    line-height: 32rpx;
}
.card-left__title {
  /* padding-left: 110rpx; */
    font-size: 30rpx;
    font-weight: 500;
    color: #333;
    line-height: 40rpx;
    padding-left: 95rpx;
}

.image{
    width: 200rpx;
    height: 150rpx;
    border-radius: 12rpx;
    margin-left: 40rpx;
}

.card-news-bottom{
    display: flex;
    flex-direction: row;
    align-items: flex-end;
    flex: 1
}
.icon-time{
    width: 28rpx;
    height: 28rpx;
    padding: 0;
    margin: 0;
    margin-right: 12rpx;
}

.fade_in {
    animation: fadeIn 1.2s both;
}


.card-bottom-title{
    font-size: 24rpx;
    color:#F04E41;
    font-weight: 400;
    line-height: 32rpx;
}
.copyright-txt{
    font-size: 18rpx;
    padding: 2rpx 6rpx;
    /* margin-right: 5px; */
    border-radius: 8rpx 0 8rpx 0;
    vertical-align: middle;
    position: absolute;
    left: 0;
    top: 6rpx;
    /* height: 27rpx;
    width: 88rpx; */
    /* padding: 4rpx 8rpx; */
    text-align: center;
    line-height: 27rpx;
 }
 .topicName{
    font-size: 22rpx;
    font-weight: 400;
    line-height: 32rpx;
}
.share-img{
    width: 24rpx;
    height: 24rpx;
    margin-left: 28rpx;
}
.no-padding {
  padding-left: 0;
}
.second-title{
    font-size: 26rpx;
    color:#6F6F6F;
    font-weight: 400;
    margin-top: 8rpx;
}
.org-name{
    border-radius: 4rpx;
    font-size: 20rpx;
    line-height: 22rpx;
    color:#F3392E;
    padding: 4rpx 10rpx;
    background-color: #FFF0EF;
    margin-right: 20rpx;
}
.topic-line{
  display: flex;
  flex-direction: row;
}
.tags {
  display: flex;
  flex-direction: row;
  white-space: nowrap;
}
.parting-line{
  width: 1rpx;
  height: 15rpx;
  background-color: #DCDCDC;
  margin-left:  12rpx;
  margin-right: 12rpx;
}
.about-product{
  width: 137rpx;
  height: 29rpx;
  border-radius: 6rpx;
  background-color: #F3392E;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 20rpx;
  color: #fff;
}