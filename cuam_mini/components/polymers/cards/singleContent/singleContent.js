import regeneratorRuntime from '../../../../lib/regenerator-runtime/runtime.js'
import {util, storage, eventName} from "../../../../common/index.js";
import {getCardRefreshMoment} from "../../../../common/utils/userStorage";
import * as enums from "../../../../common/const/enum";
const {
  CHANGE_POCUDT_CARE
} = eventName

const {
  CHANGE_REALM,
  CARD_SURROUND
} = enums

const reviewFilter = [
  'live',
  'multimedia'
]

const {
  isEmptyObject
} = util

const app = getApp()

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    TabCur: 0,  // 一级分类idx
    contentInfo: [],
    titleInfo: {},
    currList: [],
    signalCard: false,
    signalBlock: false,

    showViolations: true
  },

  attached() {
    // console.log('==== MIXTURE props >>>', this.properties)
    const {data = {}} = this.properties || {}

    this.setData({
      showViolations: !!(storage.getStorage("SHOW_VIOLATIONS_PHONE") == '18513390120')
    }, () => {
      return this.installCardInfo(data)
    })

    let _this = this
    getApp().event.on('PRODUCT_SELECT_SELECT', (e) => {
      const { currList } = _this.data
      if (currList[0]?.realm == 'fund') {
        let newlist = currList.map((item, index) => {
          if (item?.fund?.fundCode == e.fundCode) {
            let fund = { ...item.fund, selectname: e.name, selectnumber: e?.number == null ? '--' : Number(e?.number).toFixed(2) }
            return { ...item, fund }
          }
          return item
        })
        _this.setData({ currList: newlist })
      }
    })
    getApp()?.event?.on(CHANGE_POCUDT_CARE, (e) => {
      const { currList } = _this.data
      let newlist = currList.map((item, index) => {
        if (item?.realm == 'fund') {
          if (item?.fund?.fundCode == e?.fundCode&&e?.fundCode) {
            item.fund.isCare = !e.isCare
            item.fund.rangeNum = 0
          }else if(item?.fund?.fundCode&&item?.fund?.fundCode == e?.code){
            item.fund.isCare = e.isCare
            if(e.rangeNum){
              item.fund.rangeNum = e.rangeNum
            }
          }
          return item
        }
        return item
      })
      _this.setData({ currList: newlist })

    })
  },

  methods: {
    // review 过滤
    filterHideCategory(param,_cardName) {
      // console.log('====== MIXTURE filterHideCategory param >>', param)
      let contentRes = []

      for (let i = 0; i < param.length; i++) {
        let {name = '', content: dataList = []} = param[i] || {}
        if (this.data.showViolations){
          dataList = dataList.filter((fItem) => {
            const {realm = ''} = fItem || {}
            return !(reviewFilter.includes(`${realm}`))
          })
        }

        // console.log('====== MIXTURE dataList >>>', dataList)
        let _dataList = []
        if (dataList && dataList.length){
          dataList.forEach((dItem, dIndex) => {
            if(dItem.fundMgr){
              dItem.fundMgr.index = dIndex
              dItem.fundMgr.isLast = dIndex === dataList.length - 1
            }
            if(dItem.fund){
              dItem.fund.index = dIndex
              dItem.fund.isLast = dIndex === dataList.length - 1
            }
            const props = {
              ...dItem,
              signalBlock: true,
              index: dIndex,
              isLast: dIndex === dataList.length - 1,
              cName:_cardName
            }

            _dataList.push(props)
          })
        }

        const filterProps = {
          name,
          dataList: _dataList || dataList
        }
        contentRes.push(filterProps)
      }

      return contentRes
    },

    // 切换一级 tab
    tabSelect(e) {
      // console.log('=== MIXTURE tabSelect e >>', e)
      const {contentInfo} = this.data || {}
      const {
        index = 0,
        value = 0,
      } = e.detail || {}

      let temCurrList = contentInfo[index].dataList
      console.log("temCurrList===",temCurrList);
      let sensorsdata = {...this.properties.data} // 神策埋点数据
      delete sensorsdata.conf // 神策埋点数据
      delete sensorsdata.finalConf // 神策埋点数据
      temCurrList.forEach((item,i)=>{
        item.sensorsdata = sensorsdata;
        item.sensorsdata.secondaryTitle = contentInfo[index]?.name;  // 二级标题赋值
        item.sensorsdata.contentTitle = contentInfo[index]?.dataList[i]?.secondName;  // 内容标题赋值
        item.sensorsdata.moduleTitle = this.data?.cardInfo?.title?.text;  // 模块标题赋值
      })
      this.setData({
        TabCur: index,
        currList: temCurrList
      })

      // console.log('=== sensors_is_show >>>>','切换tab2',);
      // console.log('=== sensors_is_show >>>>pageName',this.data?.data?.fromTab ||'所属页面_null')
      // console.log('=== sensors_is_show >>>>cardName',this.data?.cardInfo?.title?.text || '一级标题_null')
      // console.log('=== sensors_is_show >>>>sub_position',contentInfo[index]?.name || '二级标题null',)
      // 神策埋点业务逻辑
      getApp().sensors.track('userClick',{
        pageName: this.data?.data?.fromTab ||'所属页面_null',
        cardName: this.data?.cardInfo?.title?.text || '一级标题_null',
        button: '切换tab',
        sub_position: contentInfo[index]?.name || '二级标题null',
      })
    },

    installCardInfo(data = {}) {
      const {cardName = '', aggRecCardIds = '', conf = '{}', finalConf = '{}'} = data || {}
      const {cardId, TabCur = 0} = this.data || {}
      const cardInfo = JSON.parse(finalConf)
      // console.log('===== SingleContent cardInfo >>>>', cardInfo)
      let list = []
      let sensorsdata = {...this.properties.data} // 神策埋点数据
      delete sensorsdata.conf // 神策埋点数据
      delete sensorsdata.finalConf // 神策埋点数据
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        area = 1,
        imageUrl = '',
        titleBaseimageUrl = ''
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)){
        const {data = []} = dataSource || {}
        list = [].concat(data)
      }
      let currList = []
      let contentInfo = this.filterHideCategory(list,_cardName)
      if (contentInfo && contentInfo.length){
        currList = contentInfo[this.data.TabCur].dataList || []

        currList = currList.map((item, index) => {
          if(item?.item) {
            item.item.sensorsdata = sensorsdata;
          } else {
            item.sensorsdata = sensorsdata;
          }
          // console.log(' singleContent传数据前的sensorsdata',item?.item.sensorsdata??item?.sensorsdata)
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
            mName: contentInfo[TabCur]?.name || ''
          }
        })
      }

      // console.log('===== MIXTURE currList  >>>>', currList)
      const titleInfo = {
        sensorsdata,
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        titleAreaVisible,
        cardSurround: CARD_SURROUND[area * 1],
        hasIcon: imageUrl,
        contentHasScrollTab: contentInfo && contentInfo.length > 1,
        titleBaseimageUrl,
        cName:title.text
      }

      this.setData({
        aggRecCardIds,
        cardName,
        currList,
        cardInfo,
        titleInfo,
        contentInfo,
        signalCard: titleAreaVisible == 2
      })
    },

    // 刷新监听
    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal || {}
      const {finalConf: oldFC = ''} = oldVal || {}
      let doRefresh = getCardRefreshMoment()

      let diffVal = newFC === oldFC
      if (!diffVal && doRefresh){
        return this.installCardInfo(newVal)
      }
    }
  }
});
