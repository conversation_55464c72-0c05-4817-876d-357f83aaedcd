import {getCardRefreshMoment} from "../../../../common/utils/userStorage";

function isEmptyObject(obj) {
  for (let t in obj) {
    return false;
  }
  return true;
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },

  data: {
    placeholder: '',
    template: '',
    titleAreaVisible: 0,
    cardName: '',
    cardType: '',
    aggRecCardIds: '',
    channelIds: [],
    searchInfo: {}
  },

  attached() {
    const {data = {}} = this.properties || {}
    // console.log('======= SEARCH props >>>', this.properties)
    return this.installCardInfo(data)
  },

  methods: {
    doSearch(e) {
      const {channelIds, data = {},placeholder = "",cardName = ""} = this.data || {}
      const {id} = data || {}
      const passProps = {
        channelIds,
        id,
        placeholder
      }

      //提供给事件监听函数
      this.triggerEvent('onSearchAction', passProps, {bubbles: true, composed: true})
    },

    installCardInfo(params = {}) {
      const {
        finalConf = '',
        aggRecCardIds = '',
        cardName = '',
        cardType = ''
      } = params || {}

      const searchInfo = JSON.parse(finalConf)
      console.log('searchInfo===',searchInfo)
      let _channelIds = [], _title = ''
      const {
        dataSource = {},
        template = '',
        titleAreaVisible = ''
      } = searchInfo || {}

      if (!isEmptyObject(dataSource)){
        const {channelIds = [], title = ''} = dataSource || {}
        _channelIds = [].concat(channelIds)
        _title = title
      }

      this.setData({
        placeholder: _title,
        channelIds: _channelIds,
        template,
        titleAreaVisible,
        cardName,
        cardType,
        aggRecCardIds,
        searchInfo
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal || {}
      const {finalConf: oldFC = ''} = oldVal || {}
      let doRefresh = getCardRefreshMoment()

      let diffVal = newFC === oldFC

      // console.log('========== SEARCH newFC >>>', newFC)
      // console.log('========== SEARCH oldFC >>>', oldFC)
      // console.log('========== SEARCH !diffVal && doRefresh >>>', !diffVal && doRefresh)
      if (!diffVal && doRefresh){
        return this.installCardInfo(newVal)
      }
    }
  }
});
