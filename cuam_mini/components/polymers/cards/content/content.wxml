<nb-card wx:if="{{list.length>0}}">
  <titleBar data="{{titleInfo}}" isHeaderBlock="{{isHeaderBlock}}" />
  <view class="content-nav" style="margin-top: {{signalCard ? 40 : 0}}rpx">
    <scrollable-tabview wx:if="{{contentInfo && contentInfo.length>1}}" class=".current-item {{index==TabCur?'text-black':''}}" tabs='{{contentInfo}}' bind:tabNavClick='tabSelect' backgroundColor="#fff" activeColor="#333" color="#999" showTabLine="{{true}}" barBgColor="{{$state.themeColor}}" currentIndex="{{TabCur}}" />
  </view>

  <view class="subNav" wx:if="{{initNextIdxs[TabCur].idx!==-1 && contentInfo[TabCur].list && contentInfo[TabCur].list.length>1}}" style="border-top-left-radius: 8px;border-top-right-radius: 8px">
    <!--  <view class="subNav" wx:if="{{initNextIdxs[TabCur].idx!==-1}}">-->
    <category tabs="{{contentInfo[TabCur].list}}" bind:tabBarClick='clickCategory' currentIndex="{{initNextIdxs[TabCur].idx}}" activeColor="#E80F14" color="#666666"/>
  </view>

  <view class="content-list" wx:if="{{currList.length>0}}" wx:for="{{currList}}" wx:for-item="item" wx:for-index="id" wx:key="id">
    <!-- <template is="{{item.realm}}" data="{{item}}">
    </template> -->

    <news wx:if="{{item.realm=='news'  || item.realm == 'depth_report' || item.realm == 'broker_week_finesse' || item.realm == 'depth_report_module' || item.realm == 'meet_essence'}}" data="{{item}}" cName="{{item.cName}}" cId="{{item.cId}}" mName="{{item.mName}}" isFromCard="true" signalBlock="{{item.signalBlock}}" />

    <liveItem wx:if="{{item.realm=='live'}}" data="{{item}}" cName="{{item.cName}}" cId="{{item.cId}}" mName="{{item.mName}}" signalBlock="{{item.signalBlock}}" />

    <marketPlan wx:if="{{item.realm=='marketPlan'}}" data="{{item}}" cName="{{item.cName}}" cId="{{item.cId}}" mName="{{item.mName}}" isFromCard="true" signalBlock="{{item.signalBlock}}" />

    <multimedia wx:if="{{item.realm=='multimedia'}}" data="{{item}}" cName="{{item.cName}}" cId="{{item.cId}}" mName="{{item.mName}}" isFromCard="true" signalBlock="{{item.signalBlock}}" />

    <manager wx:if="{{item.realm=='fund_manager'}}" data="{{item.fundMgr}}" cName="{{item.cName}}" cId="{{item.cId}}" mName="{{item.mName}}" isFromCard="true" signalBlock="{{item.signalBlock}}" />

    <product fromTab="{{data.fromTab}}" wx:if="{{item.realm=='fund'}}" data="{{item.fund}}" cName="{{item.cName}}" cId="{{item.cId}}" mName="{{item.mName}}" isFromCard="true" signalBlock="{{item.signalBlock}}" />
  </view>
  <view wx:if="{{initNextIdxs[TabCur].idx===-1 || !currList ||!currList.length}}">
    <emptyBlock tips="暂无内容" isContent="{{true}}}" />
    <vertical-space />
  </view>
</nb-card>