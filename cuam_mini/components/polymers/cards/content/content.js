import regeneratorRuntime from '../../../../lib/regenerator-runtime/runtime.js'
import * as enums from '../../../../common/const/enum.js'
import {getContentCategoryFilter} from "../../../../common/nb/home";
import {util, storage,eventName} from "../../../../common/index.js";
import {getCardRefreshMoment} from "../../../../common/utils/userStorage";
const {
  CHANGE_POCUDT_CARE
} = eventName

const {
  CHANGE_REALM,
  CARD_SURROUND,
  ARTICLE_TYPE,
  RealmType
} = enums

const reviewFilter = [
  'live',
  'multimedia'
]

const {
  isEmptyObject
} = util

const app = getApp()

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    TabCur: 0,  // 一级分类idx
    initNextIdxs: [], // 二级分类idx

    cardInfo: {},
    cardName: '',
    aggRecCardIds: '',
    list: [],
    contentInfo: [],
    titleInfo: {},
    currList: [],
    signalCard: false,
    signalBlock: false,
    showViolations: true
  },

  attached() {    
    // console.log('========= CONTENT this.properties >>>', this.properties)
    const {data = {}} = this.properties || {}
    this.setData({
      showViolations: !!(storage.getStorage("SHOW_VIOLATIONS_PHONE") == '18513390120')
    }, () => {
      return this.installCardInfo(data)
    })
    let _this = this
    getApp().event.on('PRODUCT_SELECT_SELECT', (e) => {
      const { currList } = _this.data
      if (currList[0]?.realm == 'fund') {
        let newlist = currList.map((item, index) => {
          if (item?.fund?.fundCode == e.fundCode) {
            let fund = { ...item.fund, selectname: e.name, selectnumber: e?.number == null ? '--' : Number(e?.number).toFixed(2) }
            return { ...item, fund }
          }
          return item
        })
        _this.setData({ currList: newlist })
      }
    })
    getApp()?.event?.on(CHANGE_POCUDT_CARE, (e) => {
      const { currList } = _this.data
      if (currList[0]?.realm == 'fund') {
        let newlist = currList.map((item, index) => {
          if (item?.fund?.fundCode == e?.fundCode) {
            item.fund.isCare = !e.isCare
            item.fund.rangeNum = 0
          }else if(item?.fund?.fundCode&&item?.fund?.fundCode == e?.code){
            item.fund.isCare = e.isCare
            if(e.rangeNum){
              item.fund.rangeNum = e.rangeNum
            }
          }
          return item
        })
        _this.setData({ currList: newlist })
      }
    })
  },

  methods: {
    async filterHideCategory(param,_cardName) {
      // console.log('===== CONTENT filterHideCategory param >>>', param)
      const {showViolations = true} = this.data || {}
      // console.log('===== CONTENT filterHideCategory showViolations >>>', showViolations)
      let dataSource = []

      for (const item of param) {
        let key = []
        let showListIds = []
        let isBrokerResearchReport = false
        const {category = [], name = ''} = item || {}
        if (category && category.length){
          category.forEach((cItem, cIndex) => {
            const {realm = '', id = ''} = cItem || {}
            if(realm == RealmType.BROKER_RESEARCH_REPORT){
              //key = []
              isBrokerResearchReport = true
            }else{
              key.push({
                key: CHANGE_REALM[realm],
                value: id
              })
            }
          })
        }

      if(key.length){
        // console.log('====== CONTENT getContentCategoryFilter  key >>>>', key)
        let {code, data, success, msg = ''} = await getContentCategoryFilter({entity: key})
        // console.log('====== CONTENT getContentCategoryFilter  data >>>>', data)
        if (!success){
          return wx.showToast({
            title: msg || '',
          })
        }

        if (data && data.length){
          data.forEach((sItem, sIndex) => {
            const {status = '', id = '', name = ''} = sItem || {}
            // 过滤显示的一级分类
            if (status == '1' || status === 'PUBLISHED'){
              showListIds.push(id)
              category[sIndex].secondName = name
            }
          })
        }
      }
        
        let filerList = []
        // console.log('========= CONTENT showListIds >>>>', showListIds)
        if (showListIds.length || isBrokerResearchReport){
          let sensorsdata = {...this.properties.data} // 神策埋点数据
          delete sensorsdata.conf // 神策埋点数据
          delete sensorsdata.finalConf // 神策埋点数据
          category.forEach((fItem, fIndex) => {
            // console.log('========= CONTENT fItem >>>>', fItem)
            const {id = '', realm: mRealm = '', dataList = []} = fItem || {}
            if (showListIds.includes(id + '') || fItem.realm == RealmType.BROKER_RESEARCH_REPORT){
              let props = {
                ...fItem,
                secondName:fItem.secondTabName,
                dataList: dataList.map((mItem, mIndex) => {
                  if(mItem.fundMgr){
                    mItem.fundMgr.index = mIndex
                    mItem.fundMgr.isLast = mIndex === dataList.length - 1
                  }
                  if(mItem.fund){
                    mItem.fund.index = mIndex
                    mItem.fund.isLast = mIndex === dataList.length - 1
                  }
                  return {
                    ...mItem,
                    realm:  fItem.realm == RealmType.BROKER_RESEARCH_REPORT? ARTICLE_TYPE[mItem.realmType]:mRealm,
                    index: mIndex,
                    isLast: mIndex === dataList.length - 1,
                    categoryId: id,
                    cName :_cardName,
                    sensorsdata, // 神策埋点数据
                  }
                })
              }

              // console.log('========= CONTENT review >>>>', showViolations && reviewFilter.includes(mRealm))
              // TODO for review
              if (showViolations && reviewFilter.includes(mRealm)){
                props.dataList = []
              }

              // if (reviewFilter.includes(mRealm)){
              //   console.log('========= CONTENT props >>>>', props)
              // }

              filerList.push(props)
            }
          })
        }

        // console.log('========= CONTENT filerList >>>>', filerList)
        const props = {
          name,
          list: filerList
        }
        dataSource.push(props)
      }

      return dataSource
    },

    // 切换一级 tab
    tabSelect(e) {
      // console.log('===CONTENT e >>', e)
      const {initNextIdxs, contentInfo} = this.data || {}
      const {
        index = 0,
        value = 0,
      } = e.detail || {}
      // console.log('===  CONTENT index,value >>', index, value)
      let tIdx = initNextIdxs && initNextIdxs[index] && initNextIdxs[index].idx || 0
      // console.log('=== CONTENT tIdx >>', tIdx)
      if (tIdx !== -1){
        this.data.currList = contentInfo[index].list[tIdx].dataList
        // 神策埋点数据处理
        for (var i=0; i<this.data.currList.length; i++){
          this.data.currList[i].sensorsdata.secondaryTitle = this.data.contentInfo[value]?.name;  // 二级标题赋值
          this.data.currList[i].sensorsdata.contentTitle = this.data.contentInfo[value]?.list[0]?.dataList[i]?.title;  // 内容标题赋值
          this.data.currList[i].sensorsdata.moduleTitle = this.data?.cardInfo?.title?.text;  // 模块标题赋值
        }

        this.setData({
          TabCur: index,
          currList: this.data.currList
        })
      } else {
        this.setData({
          TabCur: index,
          currList: []
        })
      }
      console.log(
        '=== sensors_is_show >>>>',
        '切换tab1',
        this.data?.data?.fromTab ||'所属页面_null',
        this.data?.cardInfo?.title?.text || '一级标题_null',
        this.data.contentInfo[value]?.name || '二级标题null'
      );
      // 神策埋点业务逻辑
      getApp().sensors.track('userClick',{
        pageName: this.data?.data?.fromTab ||'所属页面_null',
        cardName: this.data?.cardInfo?.title?.text || '一级标题_null',
        button: '切换tab',
        sub_position: this.data.contentInfo[value]?.name || '二级标题null',
      })
    },

    // 切换二级 tab
    clickCategory(e) {
      // console.log('======== CONTENT clickCategory e >>>', e)
      const {TabCur = 0, contentInfo} = this.data || {}
      const {
        index = 0
      } = e.detail || {}

      this.data.initNextIdxs[TabCur].idx = index
      this.data.currList = contentInfo[TabCur].list[index].dataList
      this.setData({
        initNextIdxs: this.data.initNextIdxs,
        currList: this.data.currList
      })
    },

    upDateSubTagToTab(list = []) {
      let resList = []
      const {category = []} = list[0] || {}
      // console.log('========== CONTENT upDateSubTagToTab  category >>', category, category && category.length)

      let mCategory = Array.from(category)
      if (mCategory && mCategory.length){
        for (let i = 0; i < mCategory.length; i++) {
          const {secondTabName = ''} = mCategory[i] || {}
          const upTag = {
            name: secondTabName,
            category: [].concat(mCategory[i])
          }
          resList.push(upTag)
        }
      }
      return resList
    },

    async installCardInfo(data = {}) {
      const {cardName = '', aggRecCardIds = '', conf = '{}', finalConf = '{}'} = data || {}
      const {TabCur = 0, cardId = ''} = this.data || {}
      const cardInfo = JSON.parse(finalConf)
      console.log('===== CONTENT cardInfo >>>>', cardInfo)
      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        area = 1,
        imageUrl = '',
        titleBaseimageUrl
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)){
        const {data = []} = dataSource || {}
        list = [].concat(data)
      }
      let contentInfo = []
      // console.log('===== CONTENT list >>>>', list, list && list.length <= 1)
      if (list && list.length <= 1){
        list = this.upDateSubTagToTab(list)
      }

      contentInfo = await this.filterHideCategory(list,_cardName)
      // console.log('======CONTENT contentInfo >>>> ', contentInfo)
      const initNextIdxs = []
      if (contentInfo && contentInfo.length){
        contentInfo.forEach((cItem, cIndex) => {
          const {name = '', list} = cItem || {}
          const props = {
            name,
            idx: list && list.length ? 0 : -1 // -1为没有二级分类
          }
          initNextIdxs.push(props)
        })
      }

      // console.log('======= CONTENT initNextIdxs >>>> ', initNextIdxs)
      // console.log('======= CONTENT contentInfo >>>> ', contentInfo)
      let currList = []
      if (initNextIdxs[TabCur]?.idx !== -1){
        const fList = contentInfo[TabCur]?.list || []
        currList = fList[0]?.dataList || []
        currList = currList.map((dItem, dIndex) => {
          // console.log('======= CONTENT contentHasScrollTab >>>> ', contentInfo && contentInfo.length > 1)
          // console.log('======= CONTENT contentHasSubNav >>>> ', initNextIdxs[this.data.TabCur].idx !== -1 && contentInfo[this.data.TabCur].list && contentInfo[this.data.TabCur].list.length > 1)
          return {
            ...dItem,
            isLast: dIndex == currList.length - 1,
            signalBlock: !(contentInfo && contentInfo.length > 1) && !(initNextIdxs[TabCur]?.idx !== -1 && contentInfo[TabCur]?.list?.length > 1),
            cName: _cardName,
            cId: cardId,
            mName: initNextIdxs[TabCur]?.name || ''
          }
        })
      }

      let sensorsdata = {...this.properties.data} // 神策埋点数据
      delete sensorsdata.conf // 神策埋点数据
      delete sensorsdata.finalConf // 神策埋点数据

      const titleInfo = {
        sensorsdata,
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        titleAreaVisible,
        cardSurround: CARD_SURROUND[area * 1],
        hasIcon: imageUrl,
        contentHasScrollTab: contentInfo && contentInfo.length > 1,
        contentHasSubNav: initNextIdxs[TabCur]?.idx !== -1 && contentInfo[TabCur]?.list?.length > 1,
        titleBaseimageUrl,
        cName:title.text
      }

      console.log('======= CONTENT currList >>>> ', currList)
      this.setData({
        aggRecCardIds,
        cardName,
        list,
        cardInfo,
        contentInfo,
        titleInfo,
        initNextIdxs,
        currList,
        signalCard: titleAreaVisible == 2
      })
    },

    // 刷新监听
    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal || {}
      const {finalConf: oldFC = ''} = oldVal || {}
      let doRefresh = getCardRefreshMoment()

      let diffVal = newFC === oldFC
      if (!diffVal && doRefresh){
        return this.installCardInfo(newVal)
      }
    }
  },
});
