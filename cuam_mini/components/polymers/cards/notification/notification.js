import {getCardRefreshMoment,getVersionInfo} from "../../../../common/utils/userStorage";

function isEmptyObject(obj) {
  for (let t in obj) {
    return false;
  }
  return true;
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },

  data: {
    text:'',
    bgcolor:'',
    color:'',
    bgImg:'',
  },

  attached() {
    const {data = {}} = this.properties || {}
    console.log('======= notificationInfo props >>>', this.properties)
    return this.installCardInfo(data)
  },

  methods: {
    installCardInfo(params = {}){
      const {
        finalConf = '',
      } = params || {}

      const notificationInfo = JSON.parse(finalConf)
      // console.log('notificationInfo====',notificationInfo)
      const {informData = {},bgcolor = '',basepic = '',color = "",notice_with_drawings = '',bgImg=""} = notificationInfo.dataSource.data
      const {record='',toDayrecord='',totalCash = '',totalTakeProfit = ''} = informData
      this.setData({
        text:`今日已有${toDayrecord}笔定投录入，截至今日累计有${record}笔扣款，参考累计扣款${totalCash}万元，累计止盈${totalTakeProfit}人次!`,
        bgcolor:bgcolor || '#F04E41',
        bgImg:basepic || bgImg || '',
        color: color || '#fff',
        notice_with_drawings
      })
    },
    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },
  } 
});
