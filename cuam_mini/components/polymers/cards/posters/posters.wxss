.poster-nav{
    width: 100%;
    z-index: 9999;
}

.nav{
    color: #999;
    font-size: 32rpx;
    background-color: #fff;
    white-space: nowrap;
    display: flex;
    flex-direction: row;
    border-bottom: 1px solid #eee;
    border-top-left-radius: 12rpx;
    border-top-right-radius: 12rpx;
}

.nav .current-item{
    display: flex;
    flex: 1;
    flex-direction: column;
    height: 100rpx;
    justify-content: center;
    align-items: center;
    position: relative;
}

.nav .current-item .underline{
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 50rpx;
    height: 4rpx;
}

.display-none{
    display: none;
}

.text-black{
    font-size: 16Px;
    color: #333;
    font-weight: 500;
    /* font-family: PingFang-Bold; */
}

.subNav{
    background-color: #fff;
    width: 100%;
    padding: 0;
    margin-top: -24rpx;
    /*margin-top: 100rpx;*/
    /*position: fixed;*/
    /*z-index: 9999;*/
}

.content-list{
    display: flex;
    flex-direction: column;
    width: 100%;
}

.poster-view-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-evenly;
    background-color: #fff;
    padding: 14rpx 0 28rpx 0;
    border-radius: 0 0 12rpx 12rpx;
}

.poster-line-less{
    justify-content: flex-start;
    padding-left: 28rpx;
}

.poster-line-more{
    justify-content: space-evenly;
}

.poster-block{
    white-space: nowrap;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: #fff;
    padding: 14rpx 0 28rpx 0;
    border-radius: 0 0 12rpx 12rpx;
}

.poster-item{
    display: inline-block;
    width: calc((100% - 64rpx) / 4);
    height: 228rpx;
    background-color: #fff;
}

.poster-image{
    width: 160rpx;
    height: 228rpx;
    border-radius: 8rpx;
}

.poster-temp-block{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    width: 160rpx;
    height: 228rpx;
    border-radius: 12rpx;
    background-color: #ccc;
}

.poster-empty-img{
    width: 60rpx;
    height: 60rpx;
    border-radius: 8rpx;
}

.poster-empty-block{
    display: flex;
    /*height: 228rpx;*/
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    padding: 28rpx 0;
    border-radius: 0 0 12rpx 12rpx;
}

.poster-empty-item{
    display: flex;
    width: 160rpx;
    height: 228rpx;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    margin-right: 20rpx;
}

.empty-img{
    width: 22vw;
    height: 22vw;
}

.empty-tips-txt{
    font-size: 14Px;
    color: #999999;
    font-weight: 400;
    margin-top: 20rpx;
    /* font-family: PingFang-Bold; */
}
