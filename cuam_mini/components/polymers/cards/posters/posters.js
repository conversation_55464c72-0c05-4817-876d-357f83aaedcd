import regeneratorRuntime from '../../../../lib/regenerator-runtime/runtime.js'
import {getMarketPosterList, getPosterList, getH5PosterList} from "../../../../common/nb/home";
import {enums, eventName, global, interaction, qs, storage} from "../../../../common/index.js";
import {getCardRefreshMoment} from "../../../../common/utils/userStorage";

const {CARD_SURROUND, LOGIN_VISITOR} = enums
const {SET_SCROLL_TO_TARGET} = eventName

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    intoView: {
      type: String,
      value: 'poster0',
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    cardInfo: {},
    cardName: '',
    aggRecCardIds: '',
    list: [],
    contentInfo: [],
    titleInfo: {},
    titleAreaVisible: '',
    template: '',
    isSignalCard: false,

    currList: [],
    TabCur: 0,  // 一级分类idx
    initNextIdxs: [], // 二级分类idx
    nextIndex: 0, // 二级分类idx
    posterLine: 'poster-line-more'
  },

  attached() {
    const {data = {}} = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    async getPosterList(params) {
      let posterRes = []
      const {success, msg, param, code} = await getMarketPosterList(params)
      // console.log('===== POSTERS param >>>>', param)
      if (!success){
        interaction.showToast(msg || '')
        return posterRes
      }

      // action://share/AppAdvInvestorTrainCom?value=3094776719325704&shareConfig=true&categoryId=3095560693464058
      const {content = []} = param || {}
      if (content && content.length){
        content.forEach((pItem, pIndex) => {
          const {categoryId, channel, groupId, id} = pItem || {}
          const params = {
            categoryId,
            channel,
            groupId,
            id,
          }
          const props = {
            ...pItem,
            posterIndex: pIndex,
            action: `action://share/AppAdvPosterInfo?${qs.stringify(params)}`
          }

          posterRes.push(props)
        })
      }

      return posterRes
    },

    onPosterClick(e) {
      // console.log('===== onPosterClick e >>>>', e)
      const {
        currentTarget: {
          dataset: {
            item = {}
          }
        }
      } = e || {}

      // console.log('===== onPosterClick item >>>>', item)
      //提供给事件监听函数
      this.triggerEvent('onItemClick', {
        ...item,
        action: 'action://share/AppAdvPosterInfo'
      }, {bubbles: true, composed: true})
    },

    // scroll(e) {
    //   // console.log('=== poster e >>', e)
    // },

    // 切换一级 tab
    tabSelect(e) {
      // console.log('===POSTERS e >>', e)
      const {initNextIdxs, contentInfo} = this.data || {}
      // console.log('======== POSTERS tabSelect this.data  >>> ', this.data)
      const {index = 0} = e.detail || {}

      let tIdx = contentInfo && contentInfo[index]?.category[0]?.idx || 0
      // console.log('=== POSTERS tIdx >>', tIdx)
      if (tIdx !== -1){
        this.data.currList = contentInfo[index]?.category[tIdx]?.content || []
        this.data.initNextIdxs = contentInfo[index]?.category || []

        this.setData({
          TabCur: index,
          currList: this.data.currList,
          initNextIdxs: this.data.initNextIdxs,
          nextIndex: tIdx,
          posterLine: this.data.currList.length === 4 ? 'poster-line-more' : 'poster-line-less',
        })
      } else {
        this.setData({
          TabCur: index,
          currList: [],
          nextIndex: tIdx,
        })
      }
    },

    // 切换二级 tab
    clickCategory(e) {
      // console.log('======== POSTERS clickCategory e >>>', e)
      // console.log('======== POSTERS clickCategory  >>>', this.data)
      const {initNextIdxs} = this.data || {}
      const {index = 0} = e.detail || {}

      let nextIndex = index
      this.data.initNextIdxs[index].idx = index
      this.setData({
        nextIndex,
        initNextIdxs: this.data.initNextIdxs,
        currList: initNextIdxs[index]?.content || [],
        posterLine: initNextIdxs[index]?.content?.length === 4 ? 'poster-line-more' : 'poster-line-less',
      }, () => {
        this.setScrollBottom()
      })
    },

    setScrollBottom() {
      // console.log('====== POSTERS setScrollBottom >>>', this.data)
      const {
        data: {
          cardLast,
          fromTab
        }
      } = this.data || {}

      // console.log('======= SET_SCROLL_TO_TARGET >>>',fromTab)
      if (cardLast){
        // getApp().event.emit(SET_SCROLL_TO_TARGET, fromTab)
      }
    },

    async getPosterListByTag(list = []) {
      const userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE)
      const hasLogin = !LOGIN_VISITOR.includes(userRole * 1)
      console.log('======  POSTER  hasLogin >>>', hasLogin)


      let _reInstall = []
      if (list && list.length){
        for (const item of list) {
          // console.log('======== POSTERS getPosterListByTag item  >>> ', item)
          let _tubList = []
          const {category = []} = item || {}
          if (category && category.length){
            for (let i = 0; i < category.length; i++) {
              const {id = ''} = category[i] || {}
              const params = {
                categoryId: id,
                page: 0,
                pageSize: 4
              }
              let posterApi = hasLogin ? getPosterList : getH5PosterList
              // console.log('======== POSTERS getPosterListByTag params  >>> ', params)
              const {success, param = {}, msg = ''} = await posterApi(params)
              // console.log('======== POSTERS getPosterList success,param  >>> ', success, param)

              let _content = []
              if (success){
                const {content = []} = param || {}
                _content = [].concat(content)
              }

              // console.log('======== POSTERS getPosterList _content  >>> ', _content)
              const rInsItem = {
                ...category[i],
                content: _content,
                idx: _content && _content.length ? 0 : -1 // -1为没有二级分类
              }
              _tubList.push(rInsItem)
            }
          }

          const tubParam = {
            ...item,
            category: _tubList
          }
          // console.log('======== POSTERS getPosterListByTag tubParam  >>> ', tubParam)
          _reInstall.push(tubParam)
        }
      }

      return _reInstall
    },

    async installCardInfo(data = {}) {
      const {cardName = '', aggRecCardIds = '', conf = '{}', finalConf = '{}'} = data || {}
      const {TabCur = 0, nextIndex = 0, cardId = ''} = this.data || {}
      const cardInfo = JSON.parse(finalConf)
      // console.log('===== poster cardInfo >>>>', cardInfo)

      let _pList = []
      const {
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        area = 1,
        imageUrl = '',
        dataSource: {
          data: pList = []
        },
        titleBaseimageUrl
      } = cardInfo || {}
      let _cardName = title?.text || ''

      console.log('========= POSTERS posterList >>>', pList)
      if (pList && pList.length && Object.keys(pList[0]).length){
        _pList = [].concat(pList)
      }

      if (_pList && _pList.length){
        let currList = []
        _pList = await this.getPosterListByTag(_pList)
        // console.log('========= POSTERS getPosterListByTag _pList >>>', _pList)

        const initNextIdxs = []
        if (_pList && _pList.length){
          _pList.forEach((iItem, iIndex) => {
            if (TabCur === iIndex){
              const {category = []} = iItem || {}

              category.forEach((caItem, caIndex) => {
                const props = {
                  ...caItem,
                }
                initNextIdxs.push(props)
              })
            }
          })

          const fList = _pList[TabCur]?.category || []
          currList = fList[nextIndex]?.content || []
          // console.log('========= POSTERS currList >>>', currList)

          currList = currList.map((dItem, dIndex) => {
            return {
              ...dItem,
              isLast: dIndex == currList.length - 1,
              signalBlock: false,
              cName: _cardName,
              cId: cardId,
              mName: fList[nextIndex]?.name || ''
            }
          })
        }

        const titleInfo = {
          interaction,
          subTitle,
          title,
          template,
          cardType: cardName,
          titleAreaVisible,
          cardSurround: CARD_SURROUND[area * 1],
          hasIcon: imageUrl,
          contentHasScrollTab: _pList && _pList.length > 1,
          contentHasSubNav: true,
          titleBaseimageUrl
        }

        this.setData({
          list: currList || [],
          currList,
          posterLine: currList?.length === 4 ? 'poster-line-more' : 'poster-line-less',
          aggRecCardIds,
          template,
          titleInfo,
          titleAreaVisible,
          contentInfo: _pList,
          initNextIdxs,
          isSignalCard: titleAreaVisible == 2
        })
        return
      }
      // console.log('========= POSTERS 兼容原有海报  _pList 为空>>>', _pList)

      let params = {
        page: 0,
        pageSize: 4,
        recommendation: 'RECOMMENDATION',
        status: 'PUBLISHED'
      }
      let posterRes = await this.getPosterList(params)
      posterRes = posterRes.map((item, index) => {
        return {
          ...item,
          cName: _cardName,
          cId: cardId,
          mName: ''
        }
      })
      // console.log('===== POSTERS _res >>>', posterRes)

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        titleAreaVisible,
        cardSurround: CARD_SURROUND[area * 1],
        hasIcon: imageUrl,
        contentHasScrollTab: true
      }

      this.setData({
        list: posterRes || [],
        currList: posterRes || [],
        posterLine: posterRes?.length === 4 ? 'poster-line-more' : 'poster-line-less',
        aggRecCardIds,
        template,
        titleInfo,
        titleAreaVisible,
        isSignalCard: titleAreaVisible == 2
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal || {}
      const {finalConf: oldFC = ''} = oldVal || {}
      let doRefresh = getCardRefreshMoment()

      let diffVal = newFC === oldFC
      if (!diffVal && doRefresh){
        return this.installCardInfo(newVal)
      }
    }
  }
});
