@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}

.block-box{
    width: 100%;
    /* display: block;
    overflow: hidden; */
}

.carousel-card{
    display: block;
    overflow: hidden;
    margin-left: 28rpx;
    margin-right: 28rpx;
    margin-bottom: 52rpx;
    border-radius: 8px;
    background-color: #fff;
    box-shadow: 0 0 15px 0 rgba(187, 187, 187, 0.2);
}
.content-box{
    margin-top: -36rpx;
    margin-left: 22rpx;
    margin-bottom: 48rpx;
    width: 800rpx;
}
.content-box-card{
  background-color: #fff;
  border-radius: 14rpx;
  /* box-shadow: 0 10rpx 30rpx 0 #bbbbbb1a; */
  width: 698rpx;
  padding-top: 2rpx;
}
.top-box{
    position: relative;
    border-radius: 14rpx;
}
.top-bg{
   width: 698rpx;
   height: 54rpx; 
   border-radius: 14rpx;
}
