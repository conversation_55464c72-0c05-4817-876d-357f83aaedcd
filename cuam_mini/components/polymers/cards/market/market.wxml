<view  class="carousel-card" style="margin-left: 28rpx;margin-right: 28rpx;" >
    <titleBar data="{{titleInfo}}"/>
    <view class="content-box-card">
        <view class="top-box">
            <image src="/imgs/card/market-top-bg.png" class="top-bg"/>
        </view>
        <view class="content-box">
            <view wx:key="index"
            wx:for-index="index"
            wx:for-item="model"
            wx:for="{{quickMessageArticleListModels}}">
            <marketnews data="{{model}}" sensorsData="{{data}}"
                bind:onspecialDetailAction = 'onspecialDetailAction'
            />
            </view>
        </view>
    </view>

</view>
