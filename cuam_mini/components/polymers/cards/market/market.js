import {global, md5, storage,util, enums} from "../../../../common/index.js";
import {getCardRefreshMoment,getVersionInfo} from "../../../../common/utils/userStorage";

const {
  isEmptyObject
} = util
const {PAGE_INFO_REALM,LABEL_NAME} = enums

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
  },
  data: {
    list: [],
    imgAnimate: 'fade_null',
    indexRealTimeData:[],
    quickMessageArticleListModels:[],
    pic:'',//title 的背景图片
    bgImg:'',//通知栏--板块表现的背景图片
  },

  attached() {
    // console.log('========= marketOpportunities this.properties', this.properties)
    const {data = {}} = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    async installCardInfo(data = {}) {
      const {cardName = '', aggRecCardIds = '', conf = '{}', finalConf = '{}'} = data || {}
      const {TabCur = 0, cardId = ''} = this.data || {}
      const cardInfo = JSON.parse(finalConf)
      console.log('===== marketOpportunities cardInfo >>>>', cardInfo)
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        area = 1,
        imageUrl = '',
        titleBaseimageUrl = ''
      } = cardInfo || {}
      let _cardName = title?.text || ''
      // console.log('_cardName====',_cardName)
      const {indexRealTimeData = [],quickMessageArticleListModels=[],pic = '',basepic = ''} = dataSource.data
      indexRealTimeData?.map(item=>{
        item.name = item.objname
        let codeI = item.obj.indexOf('.')
        item.code = item.obj.slice(0,codeI)
        item.num = parseFloat(item.changepct).toFixed(2)
      })
      quickMessageArticleListModels?.map((item,index)=>{
        item.day = item.timeCreated.slice(5,10)
        item.time = item.timeCreated.slice(11,16)
        item.isLast = false
        item.isFirst = false
        item.cName = _cardName
        if(index === quickMessageArticleListModels.length -1){
          item.isLast = true
        }
        if(index == 0){
          item.isFirst = true
        }

        if(item.type != 2){
          item.labelName = `${LABEL_NAME[item.copyright]?.name}` || ''
          item.labelColor = `${LABEL_NAME[item.copyright]?.textColor}` || ''
          item.labelBGColor = `${LABEL_NAME[item.copyright]?.bgColor}` || ''
        }
        item.titleImg = item.copyright == 'CLSD'?'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2022-10-26/277c5d2d-d10f-413b-9c5f-59609d26642a.png':data.copyright == 'TFKB'?'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2022-10-27/0fbca3a7-9868-477d-88ec-5751fd4024e4.png':''
        item.timeCreated = item?.timeCreated?.replace("T","  ") || ''
      })
      console.log('quickMessageArticleListModels====',quickMessageArticleListModels)

      let sensorsdata = {...this.properties.data} // 神策埋点数据
      delete sensorsdata.conf // 神策埋点数据
      delete sensorsdata.finalConf // 神策埋点数据

      const titleInfo = {
        sensorsdata,
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        titleAreaVisible,
        // cardSurround: CARD_SURROUND[area * 1],
        hasIcon: imageUrl,
        contentHasScrollTab: true,
        // contentHasSubNav: initNextIdxs[TabCur]?.idx !== -1 && contentInfo[TabCur]?.list?.length > 1
        titleBaseimageUrl,
        cName:title.text
      }

      // console.log('======= CONTENT titleInfo >>>> ', titleInfo)
      this.setData({
        // aggRecCardIds,
        // cardName,
        // list,
        // cardInfo,
        // contentInfo,
        titleInfo,
        // initNextIdxs,
        // currList,
        indexRealTimeData,
        quickMessageArticleListModels,
        pic,
        bgImg:basepic
      })
    },

    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },

    tempScroll(e) {

    },
  }
});
