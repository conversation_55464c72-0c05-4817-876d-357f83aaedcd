import {enums, util} from "../../../../common/index.js";
import {getCardRefreshMoment} from "../../../../common/utils/userStorage";

const {CARD_SURROUND} = enums

const {isEmptyObject} = util

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    list: [],
    gridInfo: [],
    titleInfo: {},
    aggRecCardIds: '',
    template: '',
    itemInfo: {}
  },

  attached() {
    const {data = {}} = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    onHandleGridAction(e) {
      // console.log('====== onHandleGridAction e >>>>', e)
      getApp().sensors.track("userClick", {
        pageName:
          e?.currentTarget?.dataset?.message?.fromTab ?? "所属页面null",
        content_name: e?.detail?.title ?? "内容标题null",
        cardName:
          e?.currentTarget?.dataset?.message?.cardName ?? "位置区域null",
        sequenceId: e?.currentTarget?.dataset?.message?.ordinal + "",
        exposed: "icon功能区",
      });
    },

    installCardInfo(params = {}) {
      const {cardName = '', aggRecCardIds = '', conf = '{}', finalConf = '{}',fromTab='',titleInside=true} = params || {}
      const {cardId = ''} = this.data || {}
      const cardInfo = JSON.parse(finalConf)
      // console.log('===== GRID cardInfo >>>>', cardInfo)

      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        area = 1,
        titleAreaVisible = 1,
        imageUrl = '',
        titleBaseimageUrl = "",
        cardbaseimgVisible = 1,//1展示  2不展示
        cardbaseimg = '',
        titleBaseimgVisible = 1
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)){
        const {data} = dataSource || {}
        list = [].concat(data)
        list = list.map((item, index) => {
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
            mName:item?.title
          }
        })
      }

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        hasIcon: imageUrl,
        titleAreaVisible,
        cardSurround: CARD_SURROUND[area * 1],
        contentHasScrollTab: true,
        titleBaseimageUrl,
        fromTab,
        titleInside:titleBaseimgVisible == 1//标题底图展示
      }

      const itemInfo = {
        list,
        cardbaseimgVisible,
        cardbaseimg
        // signalCard: titleAreaVisible == 2
      }

      // console.log('===== grid list >>>>', list)
      this.setData({
        list,
        aggRecCardIds,
        titleInfo,
        template,
        gridInfo: cardInfo,
        itemInfo,
      })

    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal || {}
      const {finalConf: oldFC = ''} = oldVal || {}
      let doRefresh = getCardRefreshMoment()

      let diffVal = newFC === oldFC
      if (!diffVal && doRefresh){
        return this.installCardInfo(newVal)
      }
    }
  }
});
