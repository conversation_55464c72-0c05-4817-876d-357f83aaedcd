import {enums, util} from "../../../../../common/index.js";

const {CARD_SURROUND} = enums

const {isEmptyObject} = util

const tempTurn = {
  template1: "mine3",
  template2: "mine4",
}

Component({
  properties: {
    data: {
      type: Object,
      value: {}
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    list: [],
    gridMineInfo: [],
    titleInfo: {},
    aggRecCardIds: '',
    template: '',
    itemInfo: {},
  },

  attached() {
    const {data = {}} = this.properties || {}
    const {cardName = '', aggRecCardIds = '', conf = '{}', finalConf = '{}'} = data || {}
    const {cardId = ''} = this.data || {}

    const cardInfo = JSON.parse(finalConf)
    // console.log('===== mine cardInfo >>>>', cardInfo)

    let list = []
    const {
      dataSource = {},
      interaction = {},
      subTitle = {},
      title = {},
      template = '',
      area = 1,
      titleAreaVisible = 1,
      cardbaseimgVisible = 1,//1展示  2不展示
      cardbaseimg = '',
    } = cardInfo || {}

    let _cardName = title?.text || ''
    if (!isEmptyObject(dataSource)){
      const {data} = dataSource || {}
      list = [].concat(data)
      list = list.map((item, index) => {
        return {
          ...item,
          cName: _cardName,
          cId: cardId,
        }
      })
    }

    const titleInfo = {
      interaction,
      subTitle,
      title,
      template: tempTurn[template],
      cardType: cardName,
      titleAreaVisible,
      cardSurround: CARD_SURROUND[area * 1],
    }

    const itemInfo = {
      list,
      signalCard: titleAreaVisible == 2,
      cardbaseimgVisible,
      cardbaseimg
    }

    // console.log('===== mine list >>>>', list)
    // console.log('===== mine template >>>>', tempTurn[template])
    this.setData({
      list,
      aggRecCardIds,
      titleInfo,
      template: tempTurn[template],
      gridMineInfo: cardInfo,
      itemInfo
    })

  },

  methods: {
    onHandleGridAction(e) {
      // console.log('====== onHandleGridAction e >>>>', e)

    }
  }
});
