.grid-template2{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    /*justify-content: space-between;*/
    /*justify-content: space-around;*/
    width: 100%;
    border-radius: 16rpx;
    padding: 12rpx;
    background-color: #fff;
    margin-bottom: 14rpx;
}

.grid-supplement{
  padding: 0 12rpx;
  background-color: transparent;
}

.grid-temp2-item{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 20.5vw;
    margin: 28rpx calc((12vw - 52rpx) / 6);
    /*height: 18vw;*/
}

.grid-item4-block{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.grid-item-txt{
    /*width: 10vw;*/
    font-size: 24rpx;
    /* font-weight: 500; */
    text-overflow: ellipsis;
    display: -webkit-box; /** 对象作为伸缩盒子模型显示 **/
    -webkit-box-orient: vertical; /** 设置或检索伸缩盒对象的子元素的排列方式 **/
    /*-webkit-line-clamp: 2; !** 显示的行数 **!*/
    overflow: hidden; /** 隐藏超出的内容 **/
    /*font-size: 40rpx;*/
    /* font-family: PingFang-Bold; */
    color: #3B3B3B;
}

.grid-item-icon{
    width: 84rpx;
    height: 84rpx;
    margin-bottom: 18rpx;
    border-radius: 8rpx;
}
