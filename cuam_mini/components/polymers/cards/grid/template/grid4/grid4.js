// import { logtoLocal } from "../../../../../../common/utils/logForTranslate.js"
import { formatTime } from "../../../../../../common/utils/format.js"
Component({
  properties: {
    data: {
      type: Array || Object,
      value: [],
      observer: 'onHandleRefresh'
    },
    startTime: {
      type: Number,
      default: 0
    },
    isSignalCard: {
      type: Boolean,
      value: false
    },
    cardbaseimgVisible: {
      type: Number,
      value: 1
    },
    cardbaseimg: {
      type: String,
      value: ''
    }
  },
  data: {
    list: [],
  },

  attached() {
    const {data = []} = this.properties || {}
    let list = data.map(v => {
      return {
        ...v,
        icon: v.icon + `?t=${formatTime(new Date(), '', 'yyyymmddhh')}`
      }
    })
    this.setData({
      list: list
    })
  },

  methods: {
    handleImageOnload(e) {
      // console.log('iconImageOnload', +new Date() - wx.getStorageSync('imageTime'))
      if (+new Date() - wx.getStorageSync('imageTime') > 1500) {
        getApp().sensors.track('iconImageOnload', {
          pageName: "首页",
          content_name: "icon区域",
          hsd_exposed_1: e.currentTarget.dataset.item.icon,
          hsd_exposed_2: +new Date() - wx.getStorageSync('imageTime')
        })
      }
    },
    handleImageError(e) {
      const { errMsg } = e.detail
      // logtoLocal('iconImageError****************'+errMsg)
      getApp().sensors.track('iconImageError', {
        pageName: "首页",
        content_name: "Icon区域",
        hsd_exposed_1: errMsg
      })
    },
    onGridClick(e) {
      const {
        currentTarget: {
          dataset: {
            item = {},
            index = ''
          }
        }
      } = e || {}

      item.kIndex = index

      //提供给事件监听函数
      this.triggerEvent('onItemClick', item, {bubbles: true, composed: true})
    },

    installCardInfo(list = []) {
      if (list && list.length){
        this.setData({
          list
        })
      }
    },

    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    }
  }
});
