<block wx:if="{{list && list.length}}">
  <view class="grid-template2 {{cardbaseimgVisible===2?'grid-supplement':''}} gird4-item" style="margin-top: {{isSignalCard?40:0}}rpx;background-image: url({{cardbaseimgVisible===1?cardbaseimg:''}});background-size: 100% 100%;">
    <view class="grid-temp2-item"
          wx:for="{{list}}"
          wx:key="index"
          wx:for-item="item"
          wx:for-index="index"
          data-index="{{index}}"
          data-item="{{item}}"
          bind:tap="onGridClick">
      <image class="grid-item-icon" data-item="{{item}}" lazy-load src="{{item.icon}}" mode="aspectFill" binderror="handleImageError" bindload="handleImageOnload"/>
      <view class="grid-item4-block">
        <text class="grid-item-txt">{{item.title || '-'}}</text>
      </view>
    </view>
  </view>
</block>
