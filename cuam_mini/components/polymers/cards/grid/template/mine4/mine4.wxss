.grid-mine4{
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    align-items: center;
    width: 100%;
    border-radius: 8rpx;
    padding: 12rpx;
    background-color: #fff;
}

.grid-supplement{
  padding: 0 12rpx;
  background-color: transparent;
}

.grid-temp4{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 20.5vw;
    margin: 14rpx calc((12vw - 52rpx) / 6);
    padding: 16rpx 0;
}

.grid-item-icon{
    width: 84rpx;
    height: 84rpx;
    border-radius: 8rpx;
    margin-bottom: 10rpx;
}

.grid-tips{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.grid-item-txt{
    font-size: 16Px;
    font-weight: bold;
    text-overflow: ellipsis;
    display: -webkit-box; /** 对象作为伸缩盒子模型显示 **/
    -webkit-box-orient: vertical; /** 设置或检索伸缩盒对象的子元素的排列方式 **/
    overflow: hidden; /** 隐藏超出的内容 **/
}


