<block wx:if="{{list && list.length}}">
  <view class="grid-mine4 {{cardbaseimgVisible===2?'grid-supplement':''}}" style="margin-top: {{isSignalCard?40:0}}rpx;background-image: url({{cardbaseimgVisible===1?cardbaseimg:''}});background-size: 100% 100%;">
    <view class="grid-temp4"
          wx:for="{{list}}"
          wx:key="index"
          wx:for-item="item"
          wx:for-index="index"
          data-item="{{item}}"
          bind:tap="onGridClick">
      <image class="grid-item-icon" src="{{item.icon}}" mode="aspectFill"/>
      <view class="grid-tips">
        <text class="grid-item-txt">{{item.title || '-'}}</text>
      </view>
    </view>
  </view>
</block>

