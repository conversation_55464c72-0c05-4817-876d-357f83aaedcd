Component({
  properties: {
    data: {
      type: Array || Object,
      value: []
    },
    isSignalCard: {
      type: Boolean,
      value: false
    },
    cardbaseimgVisible: {
      type: Number,
      value: 1
    },
    cardbaseimg: {
      type: String,
      value: ''
    }
  },
  data: {
    list: []
  },

  attached() {
    // console.log('======== mine3 this.props >>>', this.properties)
    const {data = []} = this.properties || {}
    this.setData({
      list: data
    })
  },

  methods: {
    onGridClick(e) {
      const {
        currentTarget: {
          dataset: {
            item = {}
          }
        }
      } = e || {}

      //提供给事件监听函数
      this.triggerEvent('onItemClick', item, {bubbles: true, composed: true})
    }
  }
});
