Component({
  properties: {
    data: {
      type: Array || Object,
      value: [],
      observer: 'onHandleRefresh'
    },
    isSignalCard: {
      type: Boolean,
      value: false
    },
    cardbaseimgVisible: {
      type: Number,
      value: 1
    },
    cardbaseimg: {
      type: String,
      value: ''
    }
  },
  data: {
    list: []
  },

  attached() {
    const {data = []} = this.properties || {}
    this.setData({
      list: data
    })
  },

  methods: {
    onGridClick(e) {
      const {
        currentTarget: {
          dataset: {
            item = {},
            index = ''
          }
        }
      } = e || {}
      item.kIndex = index

      //提供给事件监听函数
      this.triggerEvent('onItemClick', item, {bubbles: true, composed: true})
    },

    installCardInfo(list = []) {
      if (list && list.length){
        this.setData({
          list
        })
      }
    },

    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    }
  }
});
