<view class="grid">
  <nb-card card-item-class="grid-item-class" wx:if="{{list.length>0}}">
  <titleBar data="{{titleInfo}}" isHeaderBlock="{{isHeaderBlock}}"/>

  <template is="{{template}}" data="{{itemInfo}}">
  </template>

</nb-card>
</view>

    <!--产品 1行3列-->
<template name="template1" data="{{itemInfo}}">
  <grid3
      data="{{itemInfo.list}}"
      isSignalCard="{{itemInfo.signalCard}}"
      cardbaseimgVisible="{{itemInfo.cardbaseimgVisible}}"
      cardbaseimg="{{itemInfo.cardbaseimg}}"
      bind:onItemClick="onHandleGridAction"
  />
</template>

    <!--产品 1行4列-->
<template name="template2" data="{{itemInfo}}">
  <grid4
      gird4-item="gird4-item"
      data="{{itemInfo.list}}"
      isSignalCard="{{itemInfo.signalCard}}"
      cardbaseimgVisible="{{itemInfo.cardbaseimgVisible}}"
      cardbaseimg="{{itemInfo.cardbaseimg}}"
      bind:onItemClick="onHandleGridAction"
  />
</template>

    <!--产品 1行5列-->
<template name="template3" data="{{itemInfo}}">
  <grid5
      data="{{itemInfo.list}}"
      cardbaseimgVisible="{{itemInfo.cardbaseimgVisible}}"
      cardbaseimg="{{itemInfo.cardbaseimg}}"
      isSignalCard="{{itemInfo.signalCard}}"
      bind:onItemClick="onHandleGridAction"
  />
</template>
