<block wx:if="{{data.length>0}}">
  <view style="margin-top: {{isSignalCard?20:0}}rpx;">
    <!-- //cardbaseimgVisible 值为1 表示展示背景图，2不展示背景图 -->
    <swiper
        class="banner-block {{cardbaseimgVisible===1?'banner-block-background':''}}"
        style="margin: 0;padding: 0;background-image: url({{cardbaseimgVisible===1?cardbaseimg:''}});background-size: 100% 100%;"
        previous-margin="0"
        next-margin="0"
        circular="true"
        indicator-color="#ccc"
        indicator-active-color="rgba(255,255,255,1)"
        indicator-dots="{{false}}"
        autoplay="{{true}}"
        interval="{{15000}}"
        current="{{swiperCurrent}}"
        bindchange="swiperChange"
        duration="{{500}}">
      <block wx:for="{{data}}" wx:key="index">
        <swiper-item class="swiper-item">
          <image
              class="h100 image-swiper"
              style="border-radius:30rpx;height: 154px;"
              data-item="{{item}}"
              bind:tap="handleClickBanner"
              src="{{item.pic}}?x-oss-process=image/resize,m_fill,h_308,w_670"
              mode="aspectFill {{imgAnimate}}"
          />
        </swiper-item>
      </block>
    </swiper>

    <view class="dots" wx:if="{{data.length > 1}}">
      <block wx:for="{{data}}" wx:key="unique">
        <view
            data-i='{{index}}'
            bindtap='fn'
            class="dot{{index == swiperCurrent ? ' active' : ''}}"
        />
      </block>
    </view>
  </view>
</block>
