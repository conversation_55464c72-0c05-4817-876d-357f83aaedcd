@keyframes fadeIn{
    0%{
        opacity: 0;
    }
    100%{
        opacity: 1;
    }
}

.banner-block{
    /*解决真机图片圆角问题*/
    transform: translateY(0);
    width: 100%;
    height: 248px;
}

.swiper-item{
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: unset;
    text-align: center;
    margin-top: -10px;
}

.image-swiper{
    width: 100%;
    z-index: 99;
}

.dots{
    position: absolute;
    left: 0;
    right: 0;
    /*bottom: 20rpx;*/
    margin-top: -60px;
    display: flex;
    justify-content: center;
    z-index: 100;
}

.dots .dot{
    margin: 0 8rpx;
    width: 14rpx;
    height: 14rpx;
    background: #c3c3c3;
    border-radius: 8rpx;
    transition: all .6s;
}

.dots .dot.active{
    /*width: 24rpx;*/
    width: 14rpx;
    background: #fff;
}

.search-bar{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    flex: 1;
    margin-left: 3vw;
    height: 62rpx;
    background-color: rgba(255, 255, 255, 0.3);
    border-radius: 36rpx;
    width: 94vw;
    position: absolute;
    left: 0;
    top: 0;

    /* border: 2rpx solid #dddddd; */
}

.search-tips{
    font-size: 12Px;
    color: #FFFFFF;
    margin-left: 18rpx;
}

.search-icon{
    width: 27rpx;
    height: 26rpx;
}

.fade_in{
    animation: fadeIn 1.2s both;
}

.fade_null{

}
