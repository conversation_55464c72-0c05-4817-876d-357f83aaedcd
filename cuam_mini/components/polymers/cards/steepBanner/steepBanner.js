import {util} from "../../../../common/index";
// import { logtoLocal } from "../../../../common/utils/logForTranslate.js"
import { formatTime } from "../../../../common/utils/format.js"

// import {
//   titleHeight,
//   titleHeightPx,
//   windowWidth
// } from '../../../../common/const/systeminfo.js'

const {
  windowWidth,
  model
} = getApp().appWindow;

const {
  isEmptyObject
} = util

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    swiperCurrent: 0,
    imgAnimate: 'fade_null',
    placeholder: '独家基金产品、基金经理',
    list: [],
    channelIds: [],
    template:'',
    photoHeight: windowWidth/375*268,
    scle:windowWidth/375,
    marginBottom:-28,
    startTime: +new Date()
  },

  attached() {
    // console.log('===== banner data >>>>', this.data)
    // console.log('===== banner this.properties >>>>', this.properties)
    // console.log('titleHeight===',this.data.photoHeight)

    const {data = {}} = this.properties || {}
    this.setData({
      imgAnimate: 'fade_in',
      marginTop:model.includes('iPhone 14 Pro<')?this.data.scle*88*2+12:this.data.scle*88*2
    })
    return this.installCardInfo(data)
  },
  methods: {
    handleImageError(e) {
      const { errMsg } = e.detail
      // logtoLocal('bannerImageError****************'+errMsg)
      getApp().sensors.track('imageError', {
        pageName: "首页",
        content_name: "banner区域",
        hsd_exposed_1: errMsg,
        hsd_exposed_2: +new Date() - this.data.startTime
      })
    },
    handleImageOnload(e) {
      // console.log('bannerImageOnload', +new Date() - wx.getStorageSync('imageTime'))
      if (+new Date() - wx.getStorageSync('imageTime') > 1500) { 
        getApp().sensors.track('bannerImageOnload', {
          pageName: "首页",
          content_name: "banner区域",
          hsd_exposed_1: e.currentTarget.dataset.item.showBanner,
          hsd_exposed_2: +new Date() - wx.getStorageSync('imageTime')
        })
      }
    },
    handleClickBanner(e) {
      // console.log(' sensors_is_show handleClickBanne',e,this.__data__.data)
      // 神策埋点，大banner
      getApp().sensors.track("userClick", {
        pageName:
          e?.currentTarget?.dataset?.message?.fromTab ?? "所属页面null",
        content_name:
          e?.detail?.title || e?.detail?.actionValue || "内容标题null",
        cardName:
          e?.currentTarget?.dataset?.message?.cardName ?? "位置区域null",
        sequenceId: e?.currentTarget?.dataset?.message?.ordinal + "",
        exposed: "大banner",
      });

      const {
        currentTarget: {
          dataset: {
            item = {},
            index = ''
          }
        }
      } = e || {}
      item.kIndex = index
      // console.log('====== item >>>>', item)
      const {cardName = ''} = this.data || {}
      const passProps = {
        cardName,
        ...item
      }

      //提供给事件监听函数
      this.triggerEvent('onItemClick', passProps, {bubbles: true, composed: true})
      // bubbles: true, composed: true ===> 绑定穿透
    },

    imagePath(item) {
      return new Promise(function (resolve, reject) {
        wx.getImageInfo({
          src: item.showBanner,
          success(res) {
            resolve({ ...item, path: res.path })
          }
        })
      });
    },
    
    async installCardInfo(data = {}) {
      const {finalConf = '{}',fromTab = ''} = data || {}
      const {cardId = ''} = this.data || {}
      const cardInfo = JSON.parse(finalConf)

      // console.log('======= BANNER cardInfo >>> ', cardInfo)
      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        imageUrl = ''
      } = cardInfo || {}

      let _cardName = title?.text || ''
      let isOss = 'x-oss-process'
      if (!isEmptyObject(dataSource)){
        const {data, channelIds = [], title: placeholder} = dataSource || {}
        list = [].concat(data)
        
        list = list.map((item, index) => {
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
            showBanner: item.pic.includes(isOss)?item.pic:`${item.pic}?x-oss-process=image/resize,m_fill,h_536,w_750`
          }
        })
        
        // let newList = await Promise.all(list.map(async v => {
        //   return await this.imagePath(v)
        // }))

        // console.log('======= BANNER list >>> ', newList)
        this.setData({
          list: list,
          placeholder,
          channelIds,
          template,
          photoHeight:fromTab == 'HOME'?windowWidth/375*268:windowWidth/375*188,
          marginBottom:fromTab == 'HOME'?-28:-48
        })
      }
    },

    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },

    fn(e) {
      const {
        dataset: {
          i = 0
        }
      } = e.currentTarget || {}
      this.setData({
        swiperCurrent: i
      })
    },

    swiperChange: function(e) {
      // console.log('======= swiperChange e >>>>', e)
      let {current, source} = e.detail || {}
      if (source === 'autoplay' || source === 'touch'){
        //根据官方 source 来进行判断swiper的change事件是通过什么来触发的，autoplay是自动轮播。
        // touch是用户手动滑动。
        // 其他的就是未知问题。
        // 抖动问题主要由于未知问题引起的，所以做了限制，只有在自动轮播和用户主动触发才去改变current值，达到规避了抖动bug
        this.setData({
          swiperCurrent: current
        })
      }
    },

    doSearch(e) {
      // console.log('===== doSearch e >>>', e,this)
      // 神策埋点，搜索入口
      let companyName = wx.getStorageSync('wechat.wbs.global.wechat.info')?.companyName || 'companyName_null';
      let fullName = wx.getStorageSync('wechat.wbs.global.wechat.info')?.fullName || 'fullName_null';
      let orgName = wx.getStorageSync('wechat.wbs.global.wechat.info')?.orgName || 'orgName_null';
      let jobTitle = wx.getStorageSync('wechat.wbs.global.wechat.info')?.jobTitle || 'jobTitle_null';
      // console.log('== sensors_is_show == 人员信息',companyName,fullName,orgName,jobTitle);
      // console.log('== sensors_is_show == 人员信息',this.__data__.data?.cardName,this.__data__.data?.fromTab);
      getApp().sensors.track('userClick',{
        pageName: this.__data__.data?.fromTab || '所属页面null',
        cardName: this.__data__.data?.cardName || '位置区域null',
        exposed: '搜索框'
      })
      const {channelIds, data = {}, placeholder = "", cardName = ""} = this.data || {}
      const {id} = data || {}
      const passProps = {
        channelIds,
        id,
        placeholder,
        isFromSearchCard:true
      }

      //提供给事件监听函数
      this.triggerEvent('onSearchAction', passProps, {bubbles: true, composed: true})
    }
  }
});
