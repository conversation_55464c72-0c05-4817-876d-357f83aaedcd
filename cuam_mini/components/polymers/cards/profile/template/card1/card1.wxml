<block wx:if="{{!isEmptyObject(data)}}">
  <view class="profile-card1-block" data-item="{{data}}" bind:tap="onProfileClick"
        style="margin-top: {{isSignalCard?40:0}}rpx;">

    <block wx:if="{{hasLogin}}">
      <image
          src="{{data.avatar ||data.avatarUrl}}"
          mode="aspectFill"
          class="profile-avatar"
      />
    </block>
    <block wx:else>
      <image
          src="../../../../../../imgs/icon/<EMAIL>"
          mode="aspectFill"
          class="profile-avatar"
      />
    </block>

    <view class="profile-card1-content" wx:if="{{hasLogin}}">
      <text class="profile-name">{{data.userName || data.name || data.nickName || '-'}}</text>
      <text class="profile-channel-name">{{data.orgName || data.companyName || '-'}}</text>
    </view>
    <view class="profile-card1-content" wx:else>
      <text class="profile-name">{{'请登录后使用'}}</text>
    </view>


    <van-icon wx:if="{{showArrow || !hasLogin}}" size="20rpx" color="#999" class="profile-icon" name="arrow"/>
  </view>

  <view wx:if="{{showVersion}}" class="version-block" bind:tap="onCopyInfo">
    <view class="version-tips" wx:if="{{version}}">
      {{'版本号: ' + version}}
    </view>
  </view>
</block>

