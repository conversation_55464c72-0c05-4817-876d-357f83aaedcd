import regeneratorRuntime from '../../../../../../lib/regenerator-runtime/runtime.js'
import {getVersionInfo} from "../../../../../../common/utils/userStorage";

function isEmptyObject(obj) {
  for (let t in obj) {
    return false;
  }
  return true;
}

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    showArrow: {
      type: Boolean,
      value: false
    },
    isSignalCard: {
      type: Boolean,
      value: false
    },
    showVersion: {
      type: Boolean,
      value: false
    },
    hasLogin: {
      type: Boolean,
      value: false
    }
  },

  data: {
    version: '',
    envVersion: '',
    appId: '',
  },

  attached() {
    // console.log('========= PROFILE Card this.properties >>>>', this.properties)
    const {showVersion = false} = this.properties || {}
    if (showVersion){
      return this.getCurrentVersion()
    }
  },

  methods: {
    onProfileClick(e) {
      const {hasLogin} = this.data || {}
      let {
        currentTarget: {
          dataset: {
            item = {}
          }
        }
      } = e || {}

      item = {
        ...item,
        hasLogin
      }

      //提供给事件监听函数
      this.triggerEvent('onProfileAction', item, {bubbles: true, composed: true})
    },

    installCardInfo(data = {}) {
      if (!isEmptyObject(data)){
        this.setData({
          data
        })
      }
    },

    getCurrentVersion() {
      const _versionInfo = getVersionInfo()
      if (_versionInfo && !isEmptyObject(_versionInfo)){
        const {appId = '', envVersion = '', version = ''} = _versionInfo || {}
        this.setData({
          version,
          envVersion,
          appId,
        })
      }
    },

    onCopyInfo(e) {
      // console.log('===== onCopyInfo e >>>', e)
      const {version, envVersion, appId,} = this.data || {}
      const {data} = this.properties || {}
      const copyInfo = {
        version,
        envVersion,
        appId,
        ...data,
      }

      return wx.setClipboardData({
        data: JSON.stringify(copyInfo),
        success(res) {
          // console.log('==== res >>>', res)
        }
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      return this.installCardInfo(newVal)
    }
  }
});
