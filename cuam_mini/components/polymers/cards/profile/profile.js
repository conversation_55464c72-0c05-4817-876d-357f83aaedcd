import regeneratorRuntime from '../../../../lib/regenerator-runtime/runtime.js'
import {breakIn, enums, eventName, global, interaction, storage, util, userStorage} from "../../../../common/index";
import {checkLogin, getAdvStaffCardInfo} from "../../../../common/nb/home";

const {
  geStorageUnionId,
  getCardRefreshMoment,
  getOpenId,
  getUserLoginStatus
}= userStorage

const {CARD_SURROUND, DEV_PHONE, LOGIN_VISITOR, BREAK_FUNC_NAME} = enums

const {REFRESH_PAGE_DATA} = eventName

const {isEmptyObject} = util

const app = getApp()

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    hasLogin: {
      type: <PERSON>olean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },

  data: {
    list: [],
    titleInfo: {},
    aggRecCardIds: '',
    template: 'template1',
    titleAreaVisible: 1,
    wInfo: {},
    itemInfo: {},
    hasLogin: false,
  },

  async attached() {
    // console.log('========= PROFILE props >>>', this.properties)
    const {data = {}} = this.properties || {}
    const hasLogin = getUserLoginStatus()
    this.setData({hasLogin})
    return this.installCardInfo(data)
  },

  methods: {
    onHandleProfileAction(e) {
      // console.log('======= onHandleProfileAction e >>>>', e)
    },

 /**
  * 替换为业务函数 breakin/getUserInfo
    async getWechatUserInfo() {
      const _param = {
        unionid: geStorageUnionId(),
        openid: getOpenId(),
      }

      const _resCheckLogin = await checkLogin(_param)
      // console.log('========= getWechatUserInfo doCheckLogin  >>>', _param, _resCheckLogin)
      if (_resCheckLogin && _resCheckLogin?.success){
        const {code, success, data} = _resCheckLogin || {}
        storage.setStorage(global.STORAGE_GLOBAL_USER_ROLE, code * 1)
        for (let [key, value] of Object.entries(data)) {
          storage.setStorage(key, value)
        }
      }

      const {success, data, msg, code} = await getAdvStaffCardInfo({
        timeStamp: new Date().getTime()
      })

      // console.log('========== getUserInfo success, data, msg, code >>>>', success, data, msg, code)
      let _wInfo = storage.getStorage(global.STORAGE_GLOBAL_WECHAT_INFO) || {}

      if (success){
        const {orgId = ''} = data || {}
        storage.setStorage('orgId', orgId)

        _wInfo = {
          ..._wInfo,
          ...data
        }
        storage.setStorage(global.STORAGE_GLOBAL_WECHAT_INFO, _wInfo)
      }

      return _wInfo
    },*/

    async installCardInfo(params = {}) {
      const {hasLogin, cardId = ''} = this.data || {}
      const {
        finalConf = '',
        aggRecCardIds = '',
        cardName = '',
      } = params || {}

      app.globalData.emitter.on(REFRESH_PAGE_DATA, async (data) => {
        console.log('======= PROFILE REFRESH_PAGE_DATA data >>>>  ', data)
        if (!isEmptyObject(data)){
          const {isRefresh, registerRouter = false} = data || {}
          const {
            titleInfo: {
              titleAreaVisible = 1
            }
          } = this.data || {}
          // console.log('====== PROFILE REFRESH_PAGE_DATA  this.data >>>>',this.data)

          if (isRefresh){
            const wInfo = await breakIn({name:BREAK_FUNC_NAME.getUserInfo})

            // console.log('======= PROFILE REFRESH_PAGE_DATA wInfo >>>>  ', wInfo)
            // const userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE)
            // console.log('======= PROFILE REFRESH_PAGE_DATA userRole >>>>  ', userRole)
            // const hasLogin = !LOGIN_VISITOR.includes(userRole * 1)
            // console.log('======= PROFILE REFRESH_PAGE_DATA hasLogin >>>>  ', hasLogin)

            const itemInfo = {
              wInfo,
              hasLogin,
              signalCard: (titleAreaVisible == 2) || registerRouter
            }

            this.setData({
              itemInfo,
            })

            return true
          }
        }
      })

      const cardInfo = JSON.parse(finalConf)
      // console.log('===== PROFILE cardInfo >>>>', cardInfo)
      let phone = ''

      const wInfo = await breakIn({name:BREAK_FUNC_NAME.getUserInfo})
      // console.log('====== PROFILE wInfo >>>>', wInfo)
      if (wInfo && Object.keys(wInfo).length){
        const {phone: nPhone = ''} = wInfo || {}
        phone = nPhone
      }

      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        area = 1,
        titleAreaVisible = 1,
        imageUrl = '',
        titleBaseimageUrl
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)){
        const {data} = dataSource || {}
        list = [].concat(data)
        list = list.map((item, index) => {
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
          }
        })
      }

      const titleInfo = {
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        hasIcon: imageUrl,
        cardSurround: CARD_SURROUND[area],
        titleAreaVisible,
        contentHasScrollTab: true,
        titleBaseimageUrl
      }

      const itemInfo = {
        wInfo,
        hasLogin,
        signalCard: titleAreaVisible == 2,
        showVersion: DEV_PHONE.includes(`${phone}`)
      }

      this.setData({
        list,
        aggRecCardIds,
        template: template || 'template1',
        titleInfo,
        titleAreaVisible,
        wInfo,
        itemInfo
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      const {finalConf: newFC = ''} = newVal || {}
      const {finalConf: oldFC = ''} = oldVal || {}
      let doRefresh = getCardRefreshMoment()

      let diffVal = newFC === oldFC
      if (!diffVal && doRefresh){
        return this.installCardInfo(newVal)
      }
    }
  }
});
