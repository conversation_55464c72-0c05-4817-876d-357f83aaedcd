<view wx:if="{{list.length>0}}" class="card-box" style="margin-left: 28rpx;margin-right: 28rpx;" >
  <titleBar data="{{titleInfo}}"/>

   <scroll-view
      class="carousel-temp3-block"
      scroll-x
      scroll-with-animation="true"
      bind:scroll="tempScroll"
      style="width: 100%;">
    <view
        class="carousel-template3"
        wx:key="idx"
        wx:for-index="idx"
        wx:for-item="item"
        wx:for="{{list}}">
      <view wx:if="{{item.indexRealTimeData.length>0}}" class="temp3-image" data-item='{{item}}' data-index="{{idx}}" style="background-image: url({{item.num>0?'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2022-10-20/e9458692-e1a6-4f39-8ae2-a6fbf32e3e22.png':'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2022-10-20/cf648004-2687-46f2-98f3-7b957e6e1463.png'}});background-size: 100% 100%;"
      bind:tap="onCarouselClick" data-sensorsData="{{data}}" data-titleInfo="{{titleInfo}}">
        <text class='txt-name' style="color:#333333">{{item.name}}</text>
        <text class="txt-code" style="color:{{item.num>0?'#E8340F':'#00B53D'}}">{{item.code}}</text>
        <text class="txt-num" style="color:{{item.num>0?'#E8340F':'#00B53D'}}">{{item.num}}%</text>
      </view>
    </view>
  </scroll-view>
</view>
