@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}

.carousel-card{
    border-radius: 0;
    display: block;
    overflow: hidden;
    margin-left: 28rpx;
    margin-right: 28rpx;
    margin-bottom: 28rpx;
    background-color: #fff;
    padding: 14px;
    border-radius: 8px;
}

.carousel-temp3-block{
    white-space: nowrap;
    display: flex;
    flex-direction: row;
    align-items: center;
    margin-right: 14px;
    background-color: #fff;
    padding: 14px 0 14px 14px;
    border-radius: 8px;
    border-top: 1px solid #EAEAEA;
}

.carousel-template3{
    display: inline-block;
}

.temp3-image{
    display: flex;
    flex-direction: column;
    padding: 14px;
    min-width: 134px;
    height: 90px;
    margin-right: 14px;
}
.txt-name{
   font-size: 16px;
   font-weight: 500;
   line-height: 24px; 
}
.txt-code{
    font-size: 12px;
    line-height: 14px;
    font-weight: 400;
    margin-top: 2px;
}
.txt-num{
    font-size: 14px;
    line-height: 16px;
    font-weight: 500;
    margin-top: 6px;
}
.card-box{
    /* border:1px solid #fff; */
    margin-bottom: 52rpx;
    box-shadow: 0 0 15px 0 rgba(187, 187, 187, 0.2);
    border-radius: 8px;
    /* background-color: #fff; */
    padding-top: 2px;
    position: relative;
}

