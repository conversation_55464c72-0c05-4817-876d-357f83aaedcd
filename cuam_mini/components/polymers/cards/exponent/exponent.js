import {global, md5, storage} from "../../../../common/index.js";

Component({
  properties: {
    data: {
      type:  Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    intoView: {
      type: String,
      value: 'poster0',
    },
    isSignalCard: {
      type: Boolean,
      value: false
    },
    cardId:{
      type:String,
      value:''
    }
  },
  data: {
    list: [],
    imgAnimate: 'fade_null',
    titleInfo:{},
  },

  attached() {
    console.log('========= indexRealTime this.properties', this.properties)
    const {data = {}} = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    onCarouselClick(e) {
      // console.log('====== onCarouselClick e >>>', e)
      // 神策埋点业务逻辑
      console.log('sensors_is_show 用户',wx.getStorageSync('unionid')??'用户ID_null');
      console.log('sensors_is_show 页面',e.currentTarget.dataset?.sensorsdata?.fromTab??'所属页面null');
      console.log('sensors_is_show 内容 content_name',e.currentTarget.dataset?.item?.interaction?.actionValue??'内容标题null');
      console.log('sensors_is_show 标题',e.currentTarget.dataset?.item?.name??'位置区域null');
      console.log('sensors_is_show 位置',e.currentTarget.dataset?.sensorsdata?.ordinal+'');
      console.log('sensors_is_show bototn',e.currentTarget.dataset?.titleinfo?.title.text??'主标题null');
      getApp().sensors.track('userClick',{
        pageName: e.currentTarget.dataset?.sensorsdata?.fromTab??'所属页面null',
        content_name: e.currentTarget.dataset?.item?.interaction?.actionValue??'内容标题null',
        cardName: e.currentTarget.dataset?.item?.name??'位置区域null',
        sequenceId: e.currentTarget.dataset?.sensorsdata?.ordinal+'',
        button: e.currentTarget.dataset?.titleinfo?.title.text??'主标题null',
        exposed: '小banner',
      })

      const {
        currentTarget: {
          dataset: {
            item = {},
            index = ''
          }
        }
      } = e || {}
      const passParams = {
        ...item.interaction,
        kIndex:index,
        cId: this.properties.cardId
      }
      this.triggerEvent('onItemClick', passParams, {bubbles: true, composed: true})

      //提供给事件监听函数
      // this.triggerEvent('onCarouselAction', item, {bubbles: true, composed: true})
    },

    installCardInfo(params = {}) {
      const {
        finalConf = '',
        aggRecCardIds = '',
        cardName = '',
        cardType = ''
      } = params || {}

      const exponentInfo = JSON.parse(finalConf)
      console.log('exponentInfo====',exponentInfo)
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        area = 1,
        imageUrl = '',
        titleBaseimageUrl = ''
      } = exponentInfo || {}
      const {data = []} = dataSource
      let list = []
      data.map(item=>{
        let showData = item.indexRealTimeData[0]
        if(!showData){
          return
        }
        let codeI = showData?.obj.indexOf('.')
        item.name = showData?.objname
        item.code = showData?.obj.slice(0,codeI)
        item.num = parseFloat(showData?.changepct).toFixed(2)
        // list.push({'name':showData?.objname,'code':showData?.obj.slice(0,codeI),"num":parseFloat(showData?.changepct).toFixed(2)})
      })
      let sensorsdata = {...this.properties.data} // 神策埋点数据
      delete sensorsdata.conf // 神策埋点数据
      delete sensorsdata.finalConf // 神策埋点数据
      const titleInfo = {
        sensorsdata,
        interaction,
        subTitle,
        title,
        template,
        cardType: cardName,
        titleAreaVisible,
        // cardSurround: CARD_SURROUND[area * 1],
        hasIcon: imageUrl,
        contentHasScrollTab: true,
        titleBaseimageUrl,
        titleStyle:'black'
        // contentHasSubNav: initNextIdxs[TabCur]?.idx !== -1 && contentInfo[TabCur]?.list?.length > 1
      }

      // console.log('======= CONTENT titleInfo >>>> ', titleInfo)
      this.setData({
        // aggRecCardIds,
        // cardName,
        // list,
        // cardInfo,
        // contentInfo,
        titleInfo,
        // initNextIdxs,
        // currList,
        list:data,
        
      })
    },

    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },

    tempScroll(e) {

    }
  }
});
