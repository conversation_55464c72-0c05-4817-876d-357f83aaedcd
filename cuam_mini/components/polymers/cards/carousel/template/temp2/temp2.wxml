<block wx:if="{{list && list.length}}">
  <view class="carousel-template2  {{cardbaseimgVisible===1?'carousel-template2-background':''}}" style="margin-top: {{isSignalCard?40:0}}rpx;background-image: url({{cardbaseimgVisible===1?cardbaseimg:''}});background-size: 100% 100%;">
    <view class="carousel-temp2-item"
          wx:for="{{list}}"
          wx:key="index"
          wx:for-item="item"
          wx:for-index="index"
          data-item="{{item}}"
          bind:tap="onCarouselClick">
      <image class="carousel-item {{imgAnimate}}" src="{{item.pic}}" mode="aspectFill"/>
    </view>
  </view>
</block>
