@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}

.carousel-template2{
    display: flex;
    flex-direction: row;
    align-items: center;
    width: 100%;
    border-radius: 0;
}
.carousel-template2-background{
  background-color: #fff;
}

.carousel-temp2-item{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    width: 100%;
}

.carousel-item{
    width: 100%;
}

