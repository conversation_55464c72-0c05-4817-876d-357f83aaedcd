import {global, md5, storage} from "../../../../../../common/index";

Component({
  properties: {
    data: {
      type: Array || Object,
      value: [],
      observer: 'onHandleRefresh'
    },
    sensorsdata: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isSignalCard: {
      type: Boolean,
      value: false
    },
    cardbaseimgVisible: {
      type: Number,
      value: 1
    },
    cardbaseimg: {
      type: String,
      value: ''
    }
  },
  data: {
    list: [],
    imgAnimate: 'fade_null',
  },

  attached() {
    // console.log('========= TEMP1 this.properties', this.properties)
    const {data = []} = this.properties || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    let tempId1 = md5.hexMD5(JSON.stringify(data))
    // console.log('======== hashPool >>>>', !hashPool.includes(tempId1))
    if (!hashPool.includes(tempId1) && !!tempId1){
      this.setData({
        imgAnimate: 'fade_in',
      })
      hashPool.push(tempId1)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }
    this.setData({
      list: data,
    })
  },

  methods: {
    onCarouselClick(e) {
      console.log('== temp3 == sensors_is_show =='); // 神策埋点
      console.log("sensors_is_show userId",wx.getStorageSync('unionid')??'用户ID_null');
      console.log("sensors_is_show pageName",this.properties?.sensorsdata?.fromTab??'所属页面null');
      console.log("sensors_is_show content_name",e?.currentTarget.dataset.item?.actionValue??'内容标题null');
      console.log("sensors_is_show cardName",this.properties?.sensorsdata?.cardName??'位置区域null');
      console.log("sensors_is_show sequenceId",this.properties?.sensorsdata?.ordinal + '');
      getApp().sensors.track('userClick',{
        pageName: this.properties?.sensorsdata?.fromTab??'所属页面null',
        content_name: e?.currentTarget.dataset.item?.actionValue??'内容标题null',
        cardName: this.properties?.sensorsdata?.cardName??'位置区域null',
        sequenceId: this.properties?.sensorsdata?.ordinal + '',
        exposed: '中banner'
      })
      // console.log('====== onCarouselClick e >>>', e)
      const {
        currentTarget: {
          dataset: {
            item = {},
            index = ''
          }
        }
      } = e || {}
      item.kIndex = index

      //提供给事件监听函数
      this.triggerEvent('onCarouselAction', item, {bubbles: true, composed: true})
    },

    installCardInfo(list = []) {
      if (list && list.length){
        this.setData({
          list
        })
      }
    },

    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    }
  }
});
