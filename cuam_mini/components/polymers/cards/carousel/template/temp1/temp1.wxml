<block wx:if="{{list && list.length}}">
<!-- //cardbaseimgVisible 值为1 表示展示背景图，2不展示背景图 -->
  <view class="carousel-template1 {{cardbaseimgVisible===1?'carousel-template1-background':''}}" 
  style="margin-top: {{cardbaseimgVisible !==1?0:isSignalCard?40:10}}rpx;background-image: url({{cardbaseimgVisible===1?cardbaseimg:''}});background-size: 100% 100%;">
    <view class="carousel-temp1-item"
          wx:for="{{list}}"
          wx:key="index"
          wx:for-item="item"
          wx:for-index="index"
          data-item="{{item}}"
          data-index="{{index}}"
          bind:tap="onCarouselClick">
      <image class="carousel-item {{imgAnimate}}" src="{{item.pic}}" style="border-radius:16rpx" mode="widthFix"/>
    </view>
  </view>
</block>
