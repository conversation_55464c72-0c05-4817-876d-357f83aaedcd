<block wx:if="{{list && list.length}}">
<!-- //cardbaseimgVisible 值为1 表示展示背景图，2不展示背景图 -->
  <scroll-view
      class="carousel-temp3-block {{showTitleBar && cardbaseimgVisible===1?'titlebar-box':''}}"
      scroll-x
      scroll-with-animation="true"
      bind:scroll="tempScroll"
      style="width: 100%; margin-top: {{isSignalCard?40:0}}rpx;background-image: url({{cardbaseimgVisible===1?cardbaseimg:''}});background-size: 100% 100%;"
      scroll-into-view="{{intoView}}">
    <view
        class="carousel-template3"
        style="margin-right: {{idx===list.length-1?0:30}}rpx"
        wx:key="idx"
        wx:for-index="idx"
        wx:for-item="item"
        data-index="{{idx}}"
        wx:for="{{list}}">
      <image
          class="temp3-image {{imgAnimate}}"
          src="{{item.pic}}"
          mode="heightFix"
          data-item="{{item}}"
          data-index="{{idx}}"
          bindtap="onCarouselClick"
      />
    </view>
  </scroll-view>
</block>
