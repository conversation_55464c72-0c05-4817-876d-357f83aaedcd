import {global, md5, storage} from "../../../../../../common/index.js";

Component({
  properties: {
    data: {
      type: Array || Object,
      value: [],
      observer: 'onHandleRefresh'
    },
    sensorsdata: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    intoView: {
      type: String,
      value: 'poster0',
    },
    isSignalCard: {
      type: Boolean,
      value: false
    },
    showTitleBar:{
      type: Boolean,
      value: false
    },
    cardbaseimgVisible: {
      type: Number,
      value: 1
    },
    cardbaseimg: {
      type: String,
      value: ''
    }
  },
  data: {
    list: [],
    imgAnimate: 'fade_null',
    showTitleBar:false
  },

  attached() {
    // console.log('========= TEMP3 this.properties', this.properties)
    const {data = [],showTitleBar=false} = this.properties || {}

    let hashPool = storage.getStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST)
    if (!hashPool){
      hashPool = []
    }

    let tempId3 = md5.hexMD5(JSON.stringify(data))
    // console.log('======== hashPool >>>>', !hashPool.includes(tempId3))
    if (!hashPool.includes(tempId3) && !!tempId3){
      this.setData({
        imgAnimate: 'fade_in',
      })
      hashPool.push(tempId3)
      storage.setStorage(global.STORAGE_GLOBAL_HASH_POOL_LIST, hashPool)
    }

    this.setData({
      list: data,
      showTitleBar:showTitleBar
    })
  },

  methods: {
    onCarouselClick(e) {
      console.log('== temp3 == sensors_is_show ==',this.properties); // 神策埋点
      console.log("sensors_is_show userId",wx.getStorageSync('unionid')??'用户ID_null');
      console.log("sensors_is_show pageName",this.properties?.sensorsdata?.fromTab??'所属页面null');
      console.log("sensors_is_show content_name",e?.currentTarget.dataset.item?.actionValue??'内容标题null');
      console.log("sensors_is_show cardName",this.properties?.sensorsdata?.cardName??'位置区域null');
      console.log("sensors_is_show sequenceId",this.properties?.sensorsdata?.ordinal + '');
      getApp().sensors.track('userClick',{
        pageName: this.properties?.sensorsdata?.fromTab??'所属页面null',
        content_name: e?.currentTarget.dataset.item?.actionValue??'内容标题null',
        cardName: this.properties?.sensorsdata?.cardName??'位置区域null',
        sequenceId: this.properties?.sensorsdata?.ordinal + '',
        exposed: '中banner'
      })
      // console.log('====== onCarouselClick e >>>', e)
      const {
        currentTarget: {
          dataset: {
            item = {},
            index = ''
          }
        }
      } = e || {}
      item.kIndex = index
      //提供给事件监听函数
      this.triggerEvent('onCarouselAction', item, {bubbles: true, composed: true})
    },

    installCardInfo(list = []) {
      if (list && list.length){
        this.setData({
          list
        })
      }
    },

    onHandleRefresh(newVal = [], oldVal = []) {
      return this.installCardInfo(newVal)
    },

    tempScroll(e) {

    }
  }
});
