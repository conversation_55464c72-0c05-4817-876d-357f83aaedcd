@keyframes fadeIn {
    0% {opacity: 0;}
    100% {opacity: 1;}
}

.fade_in {
    animation: fadeIn 1.2s both;
}

.fade_null{

}

.carousel-template4{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    width: 100%;
    border-radius: 0;
}

.carousel-template4-background{
  background-color: red;
}

.carousel-temp4-item{
    display: flex;
    /*flex: 1;*/
    width: 48.5%;
    flex-direction: column;
    align-items: center;
    justify-content: space-between;
}

.carousel-item{
    width: 100%;
}


