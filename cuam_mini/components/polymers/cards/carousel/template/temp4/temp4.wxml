<block wx:if="{{list && list.length}}">
<!-- //cardbaseimgVisible 值为1 表示展示背景图，2不展示背景图 -->
  <view class="carousel-template4 {{cardbaseimgVisible===1?'carousel-template4-background':''}}" style="margin-top: {{isSignalCard?40:0}}rpx;background-image: url({{cardbaseimgVisible===1?cardbaseimg:''}});background-size: 100% 100%;">
    <view class="carousel-temp4-item"
          wx:for="{{list}}"
          wx:key="index"
          wx:for-item="item"
          wx:for-index="index"
          data-item="{{item}}"
          bind:tap="onCarouselClick">
      <image class="carousel-item {{imgAnimate}}" src="{{item.pic}}" mode="widthFix"/>
    </view>
  </view>
</block>
