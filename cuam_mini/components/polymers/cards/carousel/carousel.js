import {util} from "../../../../common/index";
import {CHANNEL_REALM} from "../../../../common/const/enum";
import {getCardRefreshMoment} from "../../../../common/utils/userStorage";

const carouselTemp = {
  template1: 'carousel1', // 单图大图
  template3: 'carousel2', // 单图小图
  template5: 'carousel3', // 轮播大图
  template7: 'carousel4', // 双图小图
  template8: 'Banner' // 轮播
}

// BI看板模块
const BI_BLOCK = 'AppAdvBiBlock';

const {isEmptyObject} = util

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    },
    cardId: {
      type: String | Number,
      value: ''
    }
  },
  data: {
    list: [],
    carouselInfo: {},
    titleInfo: {},
    aggRecCardIds: '',
    template: '',
    titleAreaVisible: 0,
    itemInfo: {},
    isMarginTop:false
  },

  attached() {
    const {data = {}} = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    onHandleCarouselAction(e) {
      // console.log('====== onHandleCarouselAction e >>>', e)
      const {detail = {}} = e || {}
      const {actionObject = {}} = detail || {}
      let passProps = {
        ...detail
      }

      if (!isEmptyObject(actionObject)) {
        const {categoryType = ''} = actionObject || {}
        passProps = {
          ...detail,
          pageType: CHANNEL_REALM[categoryType]
        }
      }

      this.triggerEvent('onItemClick', passProps, {bubbles: true, composed: true})
    },

    installCardInfo(data = {}) {
      const {cardName = '', aggRecCardIds = '', conf = '{}', finalConf = '{}'} = data || {}
      const {cardId = ''} = this.data || {}
      const cardInfo = JSON.parse(finalConf)
      // console.log('===== CAROUSEL cardInfo >>>>', cardInfo)

      let list = []
      const {
        dataSource = {},
        interaction = {},
        subTitle = {},
        title = {},
        template = '',
        titleAreaVisible = 1,
        imageUrl = '',
        titleBaseimageUrl = '',
        cardbaseimgVisible = 1,//1展示  2不展示
        cardbaseimg = '',
        titleBaseimgVisible = 1, //1展示，2隐藏
      } = cardInfo || {}

      let _cardName = title?.text || ''
      if (!isEmptyObject(dataSource)) {
        const {data} = dataSource || {}
        list = [].concat(data)
        list = list.map((item, index) => {
          const {actionValue = ''} = item || {}
          return {
            ...item,
            cName: _cardName,
            cId: cardId,
            isBiBlock: actionValue.includes(BI_BLOCK)
          }
        })
      }

      let sensorsdata = {...this.properties.data}  // 神策埋点
      delete sensorsdata.conf   // 神策埋点
      delete sensorsdata.finalConf   // 神策埋点

      // console.log('===== CAROUSEL list >>>>', list)
      const titleInfo = {
        sensorsdata,
        interaction,
        subTitle,
        title,
        template: carouselTemp[template],
        cardType: cardName,
        cardSurround: false,
        titleAreaVisible,
        hasIcon: imageUrl,
        contentHasScrollTab: true,
        titleBaseimageUrl,
        titleInside:titleBaseimgVisible == 1//标题底图展示
      }

      const itemInfo = {
        list,
        sensorsdata,
        // signalCard: titleAreaVisible == 2,
        showTitleBar:_cardName?true:false,
        cardbaseimgVisible,
        cardbaseimg,
      }
      
      /**
       * 单图BI看板调整
       */
      if (template === 'template1') {
        const {data: tData = []} = dataSource || {}
        const {actionValue = ''} = tData && tData[0] || {}
        if (actionValue.includes('AppAdvBiBlock') || actionValue.includes('AppAdvActivity')) {
          itemInfo.signalCard = false
        }
      }

      this.setData({
        titleInfo,
        list,
        template: carouselTemp[template],
        aggRecCardIds,
        carouselInfo: cardInfo,
        itemInfo
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      // console.log('========== CAROUSEL newVal,oldVal >>>', newVal, oldVal)
      const {finalConf: newFC = ''} = newVal || {}
      const {finalConf: oldFC = ''} = oldVal || {}
      let doRefresh = getCardRefreshMoment()

      // console.log('========== CAROUSEL newFC >>>',typeof newFC)
      // console.log('========== CAROUSEL oldFC >>>',typeof oldFC)
      // console.log('========== CAROUSEL !diffVal && doRefresh >>>', !diffVal && doRefresh)

      let diffVal = newFC === oldFC
      if (!diffVal && doRefresh) {
        return this.installCardInfo(newVal)
      }
    }
  }
});
