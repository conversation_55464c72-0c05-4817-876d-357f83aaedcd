<block wx:if='{{titleInside}}'>
  <view wx:if="{{visibleTitleBar}}">
      <view class="title-block"
        style="margin-left:{{noNbCard?28:0}}rpx;margin-right:{{noNbCard?28:0}}rpx;margin-bottom: {{contentHasScrollTab?-6:0}}px;margin-top: {{isHeaderBlock?40:0}}rpx;background-image: url({{titleBaseimageUrl?titleBaseimageUrl:'https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2022-11-16/b500fadf-ac69-4ff6-8d4e-f1d01b6be8d7.png'}});background-size: 100% 100%;)">
      <view class="title-left-block">
        <block wx:if="{{hasIcon}}">
          <image class="title-icon" src="{{hasIcon}}" mode="aspectFill"></image>
        </block>

        <text class="title-txt" style="margin-left:{{hasIcon?14:0}}rpx;color:{{titleStyle==='black'?'#333333':''}};">{{title || ''}}</text>
        <text wx:if="{{subTitle}}" class="subTitle-txt">{{subTitle || ''}}</text>
      </view>

      <view class="title-right-block" bind:tap="onMoreAction" data-info="{{data}}" data-sensorsData="{{sensorsData}}" wx:if="{{!!moreTxt}}">
        <text class="more-txt" style="color:{{titleStyle==='black'?'#333333':''}};">{{moreTxt || '全部'}}</text>
        <van-icon size="24rpx" color="{{titleStyle==='black'?'#333333':'#FFAAAA'}};" class="title-next-icon" name="arrow"/>
      </view>
    </view>
  </view>
</block>

<block wx:elif="{{!visibleTitleBar}}">

</block>

<!--卡片外-->
<block wx:else>
  <!-- <block wx:if="{{contentHasScrollTab}}"> -->
    <view
        class="float-title-block2"
        style="margin-top:0rpx;">
      <view class="title-left-block"
            style="margin-left:{{noNbCard?28:0}}rpx;margin-right:{{noNbCard?28:0}}rpx;">
        <block wx:if="{{hasIcon}}">
          <image class="title-icon" src="{{hasIcon}}" mode="aspectFill"></image>
        </block>

        <text class="title-txt" style="margin-left:{{hasIcon?10:0}}rpx;color:#333333;">{{title || ''}}</text>
        <text wx:if="{{subTitle}}" class="subTitle-txt" style='color:#666666'>{{subTitle || ''}}</text>
      </view>

      <view class="title-right-block" bind:tap="onMoreAction" data-info="{{data}}" data-sensorsData="{{sensorsData}}" wx:if="{{!!moreTxt}}">
        <text class="more-txt" style="color:#333333;">{{moreTxt || '全部'}}</text>
        <van-icon size="24rpx" color="#333333" class="title-next-icon" name="arrow"/>
      </view>
    </view>
  </block>

  <!-- <block wx:else>
    <view class="float-title-block" wx:if="{{visibleTitleBar}}"
          style="margin-bottom: 28rpx;margin-top: {{isHeaderBlock?40:20}}rpx">
      <view class="title-left-block"
            style="margin-left:{{noNbCard?28:0}}rpx;margin-right:{{noNbCard?28:0}}rpx;">
        <block wx:if="{{hasIcon}}">
          <image class="title-icon" src="{{hasIcon}}" mode="aspectFill"></image>
        </block>

        <text class="title-txt" style="margin-left:{{hasIcon?10:0}}rpx;">{{title || ''}}</text>
        <text wx:if="{{subTitle}}" class="subTitle-txt">{{subTitle || ''}}</text>
      </view>

      <view class="title-right-block" bind:tap="onMoreAction" data-info="{{data}}" wx:if="{{!!moreTxt}}">
        <text class="more-txt">{{moreTxt || '全部'}}</text>
        <van-icon size="24rpx" color="#999" class="title-next-icon" name="arrow"/>
      </view>
    </view>
  </block>
</block> -->
