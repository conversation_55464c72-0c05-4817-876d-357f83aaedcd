import {enums} from "../../../../common/index.js";

const {CARD_SURROUND} = enums

Component({
  properties: {
    data: {
      type: Object,
      value: {},
      observer: 'onHandleRefresh'
    },
    noNbCard: {
      type: Boolean,
      value: false
    },
    isHeaderBlock: {
      type: Boolean,
      value: false
    }
  },
  data: {
    title: '',
    subTitle: '',
    moreTxt: '',
    icon: '',
    next: '',
    cardSurround: false,
    hasIcon: false,
    visibleTitleBar: false,
    contentHasScrollTab: false,
    contentHasSubNav: false,
    titleBaseimageUrl: '',
    titleInside: true,
    titleStyle: 'white'
  },

  attached() {
    // console.log('====== TITLE_BAR props >>>>', this.properties)
    const {data = {}} = this.properties || {}
    return this.installCardInfo(data)
  },

  methods: {
    onMoreAction(e) {
      // 神策埋点业务逻辑
      console.log(this.properties,'== sensors_is_show == 全部数据')
      console.log(e.currentTarget.dataset.info.sensorsdata,'== sensors_is_show == ')
      console.log(wx.getStorageSync('unionid')??'用户ID_null','== sensors_is_show == idididididi');
      console.log(e.currentTarget.dataset?.info?.sensorsdata?.fromTab??'所属页面null','== sensors_is_show == 所属页面null');
      console.log(e.currentTarget.dataset?.info?.title?.text??'所属页面null','== sensors_is_show == 内容标题null');
      console.log(e.currentTarget.dataset?.info?.sensorsdata?.cardName??'所属页面null','== sensors_is_show == 位置区域null');
      console.log(e.currentTarget.dataset?.info?.sensorsdata?.ordinal??'所属页面null','== sensors_is_show == 位置区域null');
      getApp().sensors.track('userClick',{
        pageName: e.currentTarget.dataset?.info?.sensorsdata?.fromTab??'所属页面null',
        cardName: e.currentTarget.dataset?.info?.title?.text??'位置区域null',
        sequenceId: e.currentTarget.dataset?.info?.sensorsdata?.ordinal + '',
        button: '更多'
      })
      
      // console.log('====== onMoreAction >>>>', e)
      const {
        dataset: {
          info
        }
      } = e.currentTarget || {}

      const {interaction = {}} = info
      const passInfo = {
        ...info,
        ...interaction
      }

      //提供给事件监听函数 bubbles: true, composed: true ===> 绑定穿透
      this.triggerEvent('onItemClick', passInfo, {bubbles: true, composed: true})
    },

    installCardInfo(data = {}) {
      // console.log("=========== TITLE installCardInfo data >>>>", data)
      const {
        cardType = '',
        interaction = {},
        subTitle = {},
        title = {},
        cardSurround = false,
        hasIcon = false,
        titleAreaVisible = 1,
        contentHasScrollTab = false,
        contentHasSubNav = false,
        titleBaseimageUrl = '',
        titleInside =  true,
        titleStyle = 'white'
      } = data || {}

      this.setData({
        title: title?.text || "",
        subTitle: subTitle?.text || "",
        moreTxt: interaction?.text || "",
        cardType,
        action: interaction?.action || '',
        cardSurround,
        hasIcon,
        visibleTitleBar: CARD_SURROUND[titleAreaVisible * 1],
        contentHasScrollTab,
        contentHasSubNav,
        titleBaseimageUrl,
        titleInside,
        titleStyle
      })
    },

    onHandleRefresh(newVal = {}, oldVal = {}) {
      // console.log('========== TITLE_BAR newVal,oldVal >>>', newVal, oldVal)
      return this.installCardInfo(newVal)
    }
  }
});
