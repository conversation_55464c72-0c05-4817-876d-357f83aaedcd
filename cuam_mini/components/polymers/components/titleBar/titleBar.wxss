.title-block{
    display: flex;
    flex-direction: row;
    /* align-items: center; */
    justify-content: space-between;
    flex: 1;
    margin-top: 20rpx;
    background-color: #fff;
    border-top-right-radius: 8px;
    border-top-left-radius: 8px;
    padding: 22rpx 24rpx 29rpx;
    margin-bottom: -6rpx;
}

.float-title-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    margin-top: 10rpx;
    /* margin-bottom: 20rpx; */
    padding: 0rpx 28rpx 32rpx;

}

.float-title-block2{
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: space-between;
    flex: 1;
    margin-top: 10rpx;
    /* margin-bottom: 20rpx; */
    padding: 0rpx 28rpx 16rpx 0;

}

.title-left-block{
    display: flex;
    flex-direction: row;
    align-items: center;
    flex: 1;
}

.title-right-block{
    display: flex;
    flex-direction: row;
    align-items: center;
}

.title-icon{
    width: 44rpx;
    height: 44rpx;
}

.title-txt{
    font-size: 34rpx;
    line-height: 44rpx;
    color: #fff;
    font-weight: 500;
    /* font-family: PingFang-Bold; */
}

.more-txt{
    font-size: 24rpx;
    color: #FFAAAA;
    font-weight: 400;
    margin-right: 10rpx;
    /* font-family: PingFang-Bold; */ 
}

.subTitle-txt{
    font-size: 10Px;
    color: #fff;
    font-weight: 400;
    margin-left: 8px;
    /* margin-bottom: -6rpx; */
    /* font-family: PingFang-Bold; */
}

.title-next-icon{
    margin-bottom: -4rpx;
}


