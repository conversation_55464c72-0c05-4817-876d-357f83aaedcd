<view class='container'>
  <scroll-view scroll-into-view="{{intoView}}" scroll-x scroll-with-animation>
    <block wx:for='{{tabs}}' wx:key='index'>
      <view id="tabbar{{index}}"
            class='tab'
            style='font-weight:{{currentIndex == index ? "500": "normal"}};border:{{currentIndex == index ?"2rpx solid #E80F14FF":0}};color: {{currentIndex == index ? activeColor : color}};background-color:{{currentIndex == index ? "#FFF5F4" : "#F0F0F0"}};margin-left: {{index===0?30:16}}rpx;margin-right: {{(index===tabs.length-1 && !moreOneLine)?28:10}}rpx'
            bindtap='clickTab'
            data-index='{{index}}'
            data-value="{{item.value ? item.value : index}}">
        {{item.secondName || item.name}}
      </view>
    </block>
  </scroll-view>
</view>
