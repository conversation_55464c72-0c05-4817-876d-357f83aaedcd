Component({
  properties: {
    tabs: {
      type: Array,
      value: []
    },
    currentIndex: {
      type: Number,
      value: 0
    },
    intoView: {
      type: String,
      value: 'tabbar0',
    },
    color: {
      type: String,
      value: '#999'
    },
    activeColor: {
      type: String,
      value: '#fff'
    },
    showBorder: {
      type: Boolean,
      value: false
    }
  },
  data: {
    moreOneLine: false,
    // displayCtrl: 'normal'
  },

  attached() {
    // console.log('==== sTabBar props >>>>', this.properties)
    // console.log('==== sTabBar data >>>>', this.data)
    this.countLineLength()
  },

  methods: {
    clickTab(e) {
      const {intoView: iView} = this.data || {}
      const {
        dataset: {
          index,
          value
        }
      } = e.currentTarget || {}

      this.triggerEvent('tabBarClick', {index, value}, {})
      // console.log(index, value, 'clickTab')
      let intoView = `${iView}`.startsWith('poster') ? 'poster' : 'tabbar'

      this.setData({
        currentIndex: index,
        intoView: `${intoView}${index ? index - 1 : index}`
      })
    },

    countLineLength() {
      const {tabs = []} = this.properties || {}
      let nameLength = ''
      if (tabs && tabs.length){
        tabs.forEach((tab, i) => {
          const {name} = tab || {}
          nameLength += name
        })

        // console.log('======= sTabBar nameLength.length >>>', nameLength.length >= 20)
        this.setData({
          moreOneLine: nameLength.length >= 20,
          // displayCtrl: nameLength.length >= 20 ? 'move' : 'normal'
        })
      }
    }
  }
})
