<view class="flex flex-wrap">
  <view class="upload-img-wrap" wx:for="{{files}}">
    <van-icon
        name="close"
        class="upload-icon"
        size="24px"
        data-idx="{{index}}"
        bind:tap="handleDel"
        wx:if="{{canDelete}}"/>
    <image src="{{item.fileUrl}}" class="w100 h100 upload-img" mode="aspectFill"/>
  </view>
  <view class="upload-btn" wx:if="{{files.length<max}}" bind:tap="handleUpload">
    <van-icon name="add" size="30px"/>
  </view>
</view>
