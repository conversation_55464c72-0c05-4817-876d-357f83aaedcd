import {userStorage, wbs} from '../../common/index'

// components/nb-upload/nb-upload.js
Component({
  options: {
    addGlobalClass: true
  },
  /**
   * 组件的属性列表
   */
  properties: {
    ordsn: String,
    scene: String,
    uploadUrl: String,
    uploadParams: String,
    files: {
      type: Array,
      value: []
    },
    max: {
      type: Number,
      value: 100
    },
    canDelete: {
      type: Boolean,
      value: true
    }
  },

  /**
   * 组件的初始数据
   */
  data: {},

  /**
   * 组件的方法列表
   */
  methods: {
    // 删除
    handleDel({currentTarget}) {
      this.data.files = []
      this.triggerEvent("change", this.data.files)
    },

    // 上传
    handleUpload() {
      // console.log('========= handleUpload state >>>>', this.data.files)
      const origin = wbs.gfApi
      // 'https://aio-api.hongkun-dev.com'
      const _that = this
      const {ordsn, scene, uploadUrl} = this.data
      // debugger
      wx.chooseImage({
        count: 1,
        sizeType: ['original', 'compressed'],
        sourceType: ['album', 'camera'],
        success(res) {
          wx.uploadFile({
            filePath: res.tempFilePaths[0],
            name: 'file',
            url: uploadUrl ? (origin + uploadUrl) : (origin + '/common/edoc/upload'),
            header: {
              token: userStorage.getToken(),
            },
            formData: uploadUrl ? {} : {
              scene: scene || '',
              pf: 'wx',
              ordsn: ordsn || 'ordsn'
            },
            success(res) {
              const data = JSON.parse(res.data)
              _that.data.files = []
              _that.data.files.push(data.data)
              _that.triggerEvent("change", _that.data.files)
            },
          })
        }
      })
    },
  }
})
