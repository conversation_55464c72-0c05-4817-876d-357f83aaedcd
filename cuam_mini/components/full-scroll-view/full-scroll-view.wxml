<!-- 加载更多模板 -->
<template name="load-more">
  <view class="loadMore-view x-common">
    <view>
      <image
          wx:if="{{!noMore}}"
          class="rotate loading"
          src="./images/loading-a.png"
      />
    </view>
    <view class="middle-view">
      <!-- 完成 -->
      <block wx:if="{{noMore}}">
      </block>
      <!-- 加载 -->
      <block wx:else>
        <text class="loading-text">{{loadMoreText}}</text>
      </block>
    </view>
    <view></view>
  </view>
</template>

<block wx:if="{{modePlat}}">
  <scroll-view
      id="scroll-view"
      style="position:fixed;width:100%;left:0;top:{{topFloat}};height:{{elementHeight}}px;bottom:{{loadMoreHeight}};margin-top: {{floatTopSize}}px"
      class="scroll-view-ios translateY {{pullDownStatus === 3 ? 'refresh' : ''}} {{pullDownStatus === 4 ? 'finish' : ''}}"
      scroll-y="true"
      bindscroll="onScroll_i"
      scroll-with-animation
      enable-back-to-top
      scroll-anchoring
      refresher-enabled
      enable-passive
      refresher-threshold="{{loadingHeight}}"
      lower-threshold="{{70}}"
      refresher-triggered="{{doRefreshing}}"
      refresher-default-style="none"
      refresher-background="#f7f7f7"
      bindrefresherpulling="onRefreshPulling"
      bindrefresherrefresh="onRefreshed"
      bindrefresherrestore="onRefreshStore"
      bindrefresherabort="onRefreshAbort"
      scroll-top="{{scrollTopNum}}">
    <!-- 如果没有内容，也要撑开足够的高度 -->
    <view style="min-height:100%;position: absolute;width:100%;">
      <slot></slot>
      <!-- 加载更多 -->
      <template
          wx:if="{{showLoadMore}}"
          is="load-more"
          data="{{noMore,noMoreText,loadMoreText}}"
      />
    </view>
  </scroll-view>
</block>

    <!-- 安卓或开发 -->
<block wx:if="{{!modePlat}}">
  <!-- 下拉刷新 -->
  <view class="refresh-block x-common" style="height:{{refreshHeight}}px;">
    <view style="flex:4;flex-direction:column;justify-content: center;align-items: flex-end;padding-right: 10px;">
      <block wx:if="{{!refreshFinished}}">
        <image
            wx:if="{{loading}}"
            class="rotate loading"
            src="{{ic_loading}}"
        />
        <image
            wx:else
            class="{{(pull?'':'pull')}} refresh"
            src="{{ic_arrow}}"
        />
      </block>
      <view wx:if="{{refreshFinished}}">
        <image
            class="loading loading-finish"
            mode="aspectFill"
            src="{{ic_finish}}"
        />
      </view>
    </view>
    <view style="flex:5;flex-direction: row;align-items: center;justify-content: flex-start;">
      <view class="refresh-tips-txt" style="color: {{loginTipsColor}};">{{refreshingText}}</view>
    </view>
  </view>
  <!-- slot -->
  <scroll-view
      class="scroll_container"
      scroll-y="true"
      style="position:fixed;width:100%;left:0;height:{{elementHeight}}px;top:{{topFloat}};bottom:{{loadMoreHeight}};margin-top: {{floatTopSize}}px"
      bindscroll="scroll"
      bindscrolltolower="onLoadMore"
      bindscrolltoupper="upper"
      bindtouchstart="start"
      bindtouchend="end"
      scroll-top="{{scrollTopNum}}"
  >
    <view style="min-height:100%;position: absolute;width:100%;" bindtouchmove="move">
      <slot></slot>
      <!-- 加载更多 -->
      <template
          wx:if="{{showLoadMore && loadMore}}"
          is="load-more"
          data="{{noMore,noMoreText,loadMoreText}}"
      />
    </view>
  </scroll-view>
</block>

