/***********************************
  公共样式
***********************************/

.x-common{
    font-size: 27rpx;
    display: flex;
    overflow: hidden;
    align-items: center;
    justify-content: center;
    z-index: 99999;
}

.x-common > view{
    display: flex;
    height: 48rpx;
    justify-content: center;
    align-items: center;
}

.x-common image.refresh{
    width: 48rpx;
    height: 48rpx;
}

.x-common image.loading{
    width: 32rpx;
    height: 32rpx;
}

.x-common .rotate{
    -moz-animation: rotate 1s infinite linear;
    -webkit-animation: rotate 1s infinite linear;
    animation: rotate 1s infinite linear;
}

.x-common .pull{
    transform: rotate(180deg) scale(1) translate(0%, 0%);
    transition: All 0.5s ease;
}

.x-common image.loading-finish{
    width: 30rpx;
    height: 30rpx;
    margin-bottom: -6rpx;
}


/* 加载更多 */
.loadMore-view{
    padding: 40rpx 0;
    margin-bottom: 10rpx;
    z-index: 99999;
}

.loadMore-view .middle-view{
    flex: 1.2;
}

.loading-left{
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: flex-end;
    flex: 1;
    max-width: 30vw;
}

.loading-middle{
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    justify-content: center;
    max-width: 50vw;
}

.refresh-tips-txt{
    font-size: 14Px;
    color: #fff;
}

@-webkit-keyframes rotate{
    0%{
        transform: rotate(0deg);
    }
    100%{
        transform: rotate(360deg);
    }
}

@keyframes rotate{
    0%{
        transform: rotate(0deg);
    }
    100%{
        transform: rotate(360deg);
    }
}


/***********************************
  ios
***********************************/

.scroll-view-ios{
    position: fixed;
    overflow: hidden;
    display: block;
    background: transparent;
}


/* 只有当设置了刷新，才需要设置Y */
.translateY{
    /*transform: translateY(-48px);*/
}

.scroll-view-ios.refresh{
    transform: translateY(0);
    transition: all 0.5s linear;
}

@keyframes finish{
    from{
        transform: translateY(0)
    }
    to{
        transform: translateY(-48px)
    }
}

.scroll-view-ios.finish{
    animation: finish 0.6s;
}

.scroll-view-ios .pulldown{
    height: 48px;
    width: 100%;
    line-height: 48px;
}


/***********************************
  安卓
***********************************/


/*下拉刷新布局*/
.refresh-block{
    position: relative;
    display: flex;
    flex-direction: row;
    align-items: center;
    background-color: transparent;
    color: #323232;
    width: 100%;
    height: 0;
}


/*上拉加载更多布局*/
.loadMore-block{
    z-index: 1;
    position: fixed;
    background-color: transparent;
    color: #323232;
    width: 100%;
    box-sizing: border-box;
    left: 0;
    right: 0;
    bottom: 0;
}
