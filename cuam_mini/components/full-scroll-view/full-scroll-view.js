// import {
//   getContentHeightPx,
//   isX,
//   platform,
//   titleHeightPx,
//   footHeight,
//   screenHeight,
//   screenHeightPx,
//   titleHeight,
// } from '../../common/const/systeminfo.js'
import {eventName, util} from "../../common/index.js";

const {
  getContentHeightPx,
  platform,
  titleHeightPx,
  footHeight,
  screenHeight,
  titleHeight,
} = getApp().appWindow;

const {
  DISMISS_TABBAR,
  SET_PULL_BAR,
  FULL_PAGE_SCROLL_LISTENER} = eventName || {}

const {
  rpx2px
} = util

const IS_X_FLOAT_SIZE = 48
const LOADING_HEIGHT = 136

/**
 * 1：首页类型 => 顶部到头，无导航栏，tabBar，有logo 【适用于有沉浸式banner的首页】
 * 2. 普通类型 => 顶部不到头，有白色导航栏，tabBar，有logo【适用于正常卡片的tab首页】
 * @type {{"1": string, "2": string}}
 */
const PAGE_TYPE = {
  'HOME': 1,
  'NORMAL_CONTENT': 2,
}

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 页面类型
    pageType: {
      type: Number,
      value: PAGE_TYPE['HOME'], // 默认HOME
    },
    /**
     * 默认启动下拉刷新
     */
    enableRefresh: {
      type: Boolean,
      value: true,
    },
    /**
     * 默认启动加载更多
     */
    enableLoadMore: {
      type: Boolean,
      value: true,
    },
    /**
     * 定位top
     */
    layerTop: {
      type: Number,
      value: 0
    },
    // 设置自定义下拉刷新阈值
    pullDownHeight: {
      type: Number,
      value: 60,
    },
    // 外部调用接口
    refreshing: {
      type: Boolean,
      value: false,
      observer: 'watchRefreshFinished',
    },
    loadMore: {
      type: Boolean,
      value: false,
      observer: 'watchLoadMoreFinished',
    },
    /**
     * 重置复位
     * 列表刷新的时候
     * 如果有加载更多的处理
     * 必须有溢出的数据
     */
    resetting: {
      type: Boolean,
      value: false,
      observer: 'watchReset',
    },
    /**
     * 数据上拉加载完毕
     * 没有更多的数据
     * 修改状态
     */
    noMore: {
      type: Boolean,
      value: false
    },
    /**
     * 向上滑动数
     */
    scrollTop: {
      type: Number,
      value: 0
    },
    /**
     * 刷新下拉间距指数
     */
    refreshTopDistance: {
      type: Number,
      value: 2
    },
    /**
     * 是否包含tabBar
     */
    hasTabBarInfo: {
      type: Boolean,
      value: false
    },

  },

  /**
   * 组件的初始数据
   */
  data: {
    /**
     * 1.devtools-android 与开发模式都使用 模式 安卓 开发板通过
     * 2.模式 ios独立使用，因为ios的反弹，重做
     */
    modePlat: platform === "ios",
    /**
     * 元素高度
     */
    elementHeight: getContentHeightPx(false, true, false),
    loadingHeight: LOADING_HEIGHT, //正在加载时高度
    loading: false, //是否在加载中
    topFloat: 0, // 贴顶高度
    pullIndex: 0.4, // 拉动指数
    loginTipsColor: '#8A8A8A',
    topOpacity: 0,

    isFullPage: 0,
    floatTopSize: 0,

    /**
     * 下拉刷新状态或者上拉加载更多状态
     * true 拉着
     * false 释放
     */
    pull: true,
    refreshingText: "下拉刷新",
    pullText: "下拉刷新",
    releaseText: "释放立即刷新",
    loadingText: "正在刷新...",
    finishText: "刷新完成",

    // 加载更多
    showLoadMore: false, //因为数据关系，下拉更多需要再lower才能出发
    loadMoreText: "正在加载...",
    noMoreText: "数据全部加载完毕",

    //ios
    pullDownStatus: 0,
    doRefreshing: false,

    // 刷新完成
    refreshFinished: false,

    //安卓
    refreshHeight: 0, //刷新布局高度
    loadMoreHeight: 0, //加载更多布局高度
    scrolling: false, //滚动中,不处理事件，只有头尾处理
    isUpper: true,
    isLower: false,
    downY: 0, //触摸时Y轴坐标
    end: true, //touchEnd
    animationData: {},

    ic_arrow: './images/arrow-i.png',
    ic_loading: './images/loading-i.png',
    ic_finish: './images/finish-i.png',
    scrollHeight:getContentHeightPx(false, false, false),
    scrollTop:0
  },

  attached() {
    console.log('======= FULL_SCROLL_VIEW this.properties >>>>>', this.properties)

    getApp().event.on(DISMISS_TABBAR, () => {
      this.doSetPageHeight()
    });

    getApp().event.on("TAB_CHANGE",()=>{
      this.setData({scrollTopNum:0})
    })

    getApp().event.on(FULL_PAGE_SCROLL_LISTENER, params => {
      const {type} = params || {}
      console.log('======= FULL_PAGE_SCROLL_LISTENER params >>>>>', params)

      switch (type) {
        case 'refreshStore': {
          this.setData({
            doRefreshing: false,
            pullDownStatus: 0,
          })
          return
        }

        default:
          break
      }
    })

    const {pageType} = this.properties || {}

    let pullIndex = 0.4
    let elementHeight = getContentHeightPx(false, true, false)
    let isFullPage = 0
    let floatTopSize = 0
    if (pageType === PAGE_TYPE['NORMAL_CONTENT']){
      elementHeight = rpx2px(screenHeight - footHeight - titleHeight)
      pullIndex = 0.45
      isFullPage = 1
      floatTopSize = LOADING_HEIGHT - IS_X_FLOAT_SIZE
    }

    this.setData({
      elementHeight,
      pullIndex,
      isFullPage,
      floatTopSize,
    })
  },

  ready() {
    let {loadingHeight, pageType} = this.data || {}
    //设置加载的高度，是当前页面包裹区域的0.8%
    loadingHeight = 136
    if (pageType === PAGE_TYPE['NORMAL_CONTENT']){
      loadingHeight += Math.floor(titleHeightPx * 0.2)
    }
    this.setData({
      loadingHeight
    })
  },

  /**
   * 组件的方法列表
   */
  methods: {
    //======================
    // 通用刷新后处理
    //======================
    /**
     * 下拉：刷新，请求
     */
    sendRefreshWatch() {
      this.properties.refreshing = true; //必须
      let timer = setTimeout(() => {
        clearTimeout(timer);
        this.triggerEvent('pulldownrefresh');
      }, 500);
    },

    /**
     * 上拉：加载更多，请求
     */
    sendLoadMoreWatch() {
      this.properties.loadmoring = true; //必须
      let timer = setTimeout(() => {
        clearTimeout(timer);
        this.triggerEvent('pulluploadmore');
      }, 500);
    },

    /**
     * 下拉更新后处理
     */
    watchRefreshFinished(newVal, oldVal) {
      const {modePlat} = this.data;
      if (oldVal === true && newVal === false){
        if (!modePlat){
          this.onRefreshFinished_a()
        }
      }
    },

    /**
     * 上拉加载更多
     */
    watchLoadMoreFinished(newVal, oldVal) {
      if (oldVal === true && newVal === false){
        this.properties.resetting = false
      }
    },

    /**
     * 观察数据重置
     */
    watchReset(newVal, oldVal) {
      if (newVal === true && oldVal === false){
        this.setData({
          showLoadMore: false
        })
      }
    },

    /**
     * 上拉  滚动条 滚动到底部时触发
     * @param {*} e
     */
    onLoadMore() {
      // console.log('======= FULL_SCROLL_VIEW onLoadMore e >>>>>', e)
      const {enableLoadMore, noMore} = this.data || {}
      this.data.end = true;
      this.data.isLower = true;
      this.data.scrolling = false;

      if (enableLoadMore){
        //每次刷新都还原，必须重新设置，因为数据的列表是可以变化的
        this.setData({
          showLoadMore: true
        })

        //上拉到越界的时候，才启动加载更多
        if (!noMore){
          this.sendLoadMoreWatch()
        }
      }
    },

    //======================
    // ios处理逻辑 ↓↓↓↓
    //======================
    onScroll_i(e) {
      this.triggerEvent("viewscroll", e.detail.scrollTop)
      if(e.detail.scrollTop != 0){
        this.scrollTop = e.detail.scrollTop
      }
      // console.log('ios scroll ===',e.detail.scrollTop)
      if (this.data.enableRefresh && this.data.isUpper){
        const status = this.data.pullDownStatus;
        if (status === 3 || status == 4) return;
        const scrollTop = e.detail.scrollTop;

        // 默认值
        let targetStatus = 1;
        let pull = false
        let refreshingText = this.data.pullText

        // 超过
        if (Math.abs(scrollTop) > this.data.loadingHeight){
          targetStatus = 2;
          pull = true
          refreshingText = this.data.releaseText
        }

        if (status != targetStatus){
          this.setData({
            pull: pull,
            refreshingText: refreshingText,
            pullDownStatus: targetStatus,
          })
        }
        this.data.isUpper = false
      }
    },
    //被下拉
    onRefreshPulling(event) {
      // console.log('====== onRefreshPulling event >>>', event, this.data)
      const {loadingHeight} = this.data || {}
      const {dy} = event.detail || {}

      // console.log('========onRefreshPulling__dy dy >>>>  ', dy)
      getApp().event.emit(SET_PULL_BAR, {type: 'pull', value: dy})
      // this.triggerEvent("viewscroll", dy)
      if (dy < loadingHeight){
        this.setData({
          pullDownStatus: 0,
        })
      } else {
        this.setData({
          pullDownStatus: 1,
        })
      }
    },

    //下拉刷新执行
    onRefreshed(event) {
      console.log('====== onRefreshed event >>>', event)
      const {dy} = event.detail || {}
      getApp().event.emit(SET_PULL_BAR, {type: 'refresh', value: dy})
      this.setData({
        pullDownStatus: 3,
        doRefreshing: true,
      }, () => {
        let doRefreshTimer = setTimeout(() => {
          clearTimeout(doRefreshTimer)
          getApp().event.emit(SET_PULL_BAR, {type: 'finished', value: dy})
          this.refreshingDown(500)
        }, 1500)
      })
    },

    //下拉刷新关闭了
    onRefreshStore(event) {
      console.log('====== onRefreshStore event >>>', event)
      const {dy} = event.detail || {}
      getApp().event.emit(SET_PULL_BAR, {type: 'store', value: dy})
      this.setData({
        doRefreshing: false,
        pullDownStatus: 0,
      })
    },

    // 自定义下拉刷新被中止 (拉一半自动弹回)
    onRefreshAbort(event) {
      console.log('====== onRefreshAbort event >>>', event)
      const {dy} = event.detail || {}
      getApp().event.emit(SET_PULL_BAR, {type: 'abort', value: dy})
      this.setData({
        pullDownStatus: 0,
      })
    },

    //======================
    // ios处理逻辑 ↑↑↑↑
    //======================

    /**
     * 滚动到顶部/左边，会触发 scrolltoupper 事件
     * 下拉  滚动条 滚动顶底部时触发
     * @param {*} e
     */
    upper() {
      // console.log('======= FULL_SCROLL_VIEW upper  e >>>>>', e)
      this.data.end = true;
      this.data.isUpper = true; //标记下拉越界
      this.data.scrolling = false; //标记不是自动滚动了
    },

    scroll(e) {
      // console.log('======= FULL_SCROLL_VIEW scroll >>>>>',e.detail.scrollTop)
      if(e.detail.scrollTop != 0){
        this.scrollTop = e.detail.scrollTop
      }
      const {scrollTop: sTop} = e.detail || {}
      const {end, isLower, isUpper} = this.data || {}
      this.triggerEvent("viewscroll", sTop)
      if (end && isLower){
        //如果快速拖动 然后释放 会在end后继续scroll
        //可能出现scroll到顶点后依然走scroll方法
        return;
      }
      if (end && isUpper){
        return;
      }
      this.data.scrolling = true;
      this.data.isUpper = false;
      this.data.isLower = false;
    },

    start(e) {
      // console.log('======= FULL_SCROLL_VIEW start e >>>>>', e)
      const {scrolling, loading} = this.data || {}
      this.data.end = false;
      if (scrolling || loading){
        return;
      }

      //触摸目标在视口中的y坐标。
      var clientY = e.touches[0].clientY;
      this.setData({
        downY: clientY,
        refreshHeight: 0,
        loadMoreHeight: 0,
        pull: true,
        refreshingText: this.data.pullText
      });
    },

    move(e) {
      const {
        enableRefresh,
        scrolling,
        loading,
        downY,
        loadingHeight,
        isUpper,
        refreshHeight,
        refreshingText,
        releaseText,
        pullText,
        pullIndex
      } = this.data || {}
      if (!enableRefresh || scrolling || loading){
        return
      }

      //表示自上次触摸以来发生了什么改变的Touch对象的数组。
      var movePoint = e.changedTouches[0];
      //降速,防抖
      var moveY = (movePoint.clientY - downY) * pullIndex;
      //上拉不需要
      if (moveY < 0){
        return
      }
      //高度的最大值，不能超过3倍加载view
      if (moveY > loadingHeight * 3){
        return;
      }

      //必须越界
      if (isUpper){
        // console.log('======= FULL_SCROLL_VIEW move isUpper >>>>>', isUpper)
        this.setData({
          refreshHeight: moveY
        })
        if (refreshHeight > loadingHeight){
          if (refreshingText !== releaseText){
            this.setData({
              pull: false,
              refreshingText: releaseText
            })
          }
        } else {
          if (refreshingText !== pullText){
            this.setData({
              pull: true,
              refreshingText: pullText
            })
          }
        }
      }
    },

    end() {
      // console.log('======= FULL_SCROLL_VIEW end e >>>>>', e)
      const {loadingHeight, refreshHeight, loadingText} = this.data || {}
      this.data.end = true;
      this.data.scrolling = false;
      //释放开始刷新
      var height = loadingHeight;
      if (refreshHeight > loadingHeight){
        this.setData({
          refreshHeight: height,
          loading: true,
          pull: false,
          refreshingText: loadingText
        });
        this.sendRefreshWatch()
      } else {
        //复位下拉刷新的属性
        // console.log('复位下拉刷新的属性')
        this.setData({
          refreshHeight: 0,
          loadMoreHeight: 0,
          loading: false,
          pull: true
        })
      }
    },

    /**
     * 设置高度
     */
    setTop(time, heightValue) {
      var animation = wx.createAnimation({
        duration: time,
        timingFunction: 'linear'
      })
      animation.top(heightValue).step()
      this.setData({
        animationData: animation.export()
      })
    },

    /**
     * 加载完毕
     */
    onRefreshFinished_a() {
      const {refreshHeight, finishText, layerTop} = this.data || {}
      if (refreshHeight){
        this.setData({
          refreshFinished: true,
          refreshingText: finishText
        });
        let timer1 = setTimeout(() => {
          clearTimeout(timer1);
          this.setTop(150, layerTop)
          let timer2 = setTimeout(() => {
            clearTimeout(timer2);
            this.setData({
              refreshFinished: false,
              refreshHeight: 0,
              loadMoreHeight: 0,
              loading: false,
              animationData: wx.createAnimation({
                duration: 0
              }).step()
            })
          }, 200)
        }, 600);
      }
    },

    doSetPageHeight() {
      // console.log('====== FULL_SCROLL_VIEW doSetPageHeight ')
    },

    doSetShowPageHeight(){
      // console.log('this.data.scrollTop===',this.data.scrollTop)
      this.setData({
        elementHeight:getContentHeightPx(false, true, false),
        scrollTopNum:this.scrollTop 
      })
    },

    refreshingDown(ms = 500) {
      console.log('====== FULL_SCROLL_VIEW refreshingDown ')

      let timer = setTimeout(() => {
        clearTimeout(timer)
        this.setData({
          doRefreshing: false,
          pullDownStatus: 1,
        })
      }, ms)
    }

  }
})
