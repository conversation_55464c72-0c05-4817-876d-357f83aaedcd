
<view class="full-page" style="{{containerBgColor && 'background-color:' + containerBgColor+';'}}">
  <nb-selectlist fromCard='{{true}}' selectList='{{selectList}}' bind:isShowModal='selectCallBack' showModal='{{showModal}}'  bind:chooseItem='chooseItem'/>
  <privacy/>
  <attention-model showStatus="{{showAttention}}" startStatus="{{startStatus}}"/>
  <!--logo-->
  <view
      wx:if="{{showPageLogo}}"
      class="header-block {{tBView}}"
      style="height: {{titleHeight}}rpx; min-height: {{titleHeight}}rpx">
    <image
        mode="heightFix"
        src="{{logoIcon}}"
        style="height:{{logoHeightSize}}px;margin-bottom: {{navBottomFloat}}px"
        class="logo-icon"/>
  </view>

  <!-- 下拉 -->
  <view wx:if="{{modePlat && loadingHeight}}" class="ios-refresh-bar" style="height: {{loadingHeight}}px">
    <view style="flex:4;flex-direction:column;justify-content: center;align-items: flex-end;padding-right: 10px;">
      <block wx:if="{{!refreshFinished}}">
        <image
            wx:if="{{loading}}"
            class="rotate loading"
            src="{{ic_loading}}"
        />
        <image
            wx:else
            class="{{(pull?'':'pull')}} refresh"
            src="{{ic_arrow}}"
        />
      </block>
      <view wx:if="{{refreshFinished}}">
        <image
            class="loading loading-finish"
            mode="aspectFill"
            src="{{ic_finish}}"
        />
      </view>
    </view>
    <view style="flex:5;flex-direction: row;align-items: center;justify-content: flex-start;">
      <view class="refresh-tips-txt" style="color: {{loginTipsColor}};">{{refreshingText}}</view>
    </view>
  </view>

  <scroll-view
      scroll-y="{{true}}"
      style='height:{{contentHeight}}px'>
    <slot></slot>
  </scroll-view>

  <!-- 底部TabBar -->
  <tabbar
      wx:if="{{showTabBar}}"
      currentSelectedType="{{tabBarType}}"
      bind:tabChange="onTabChange"
  />
</view>
