.full-page{
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
}

.header-block{
    width: 100%;
    position: fixed;
    top: 0;
    left: 0;
    display: flex;
    flex-direction: column;
    justify-content: flex-end;
    z-index: 99999;
    overflow: hidden;
    background-color: #FFFFFF;
}

.logo-icon{
    max-height: 68px;
    margin-left: 20rpx;
}

.full-list{

}

.full-view{
    background-color: transparent;
}

.content-view{
    background-color: #FFF;
}

.ios-refresh-bar{
    font-size: 27rpx;
    display: flex;
    position: absolute;
    width: 100%;
    top: 0;
    align-items: center;
    justify-content: center;
    z-index: 99999;
    /*background-color: antiquewhite;*/
}

.ios-refresh-bar > view{
    display: flex;
    height: 48rpx;
    justify-content: center;
    align-items: center;
}

.ios-refresh-bar image.refresh{
    width: 48rpx;
    height: 48rpx;
}

.ios-refresh-bar image.loading{
    width: 32rpx;
    height: 32rpx;
}

.ios-refresh-bar .rotate{
    -moz-animation: rotate 1s infinite linear;
    -webkit-animation: rotate 1s infinite linear;
    animation: rotate 1s infinite linear;
}

.ios-refresh-bar .pull{
    transform: rotate(180deg) scale(1) translate(0%, 0%);
    transition: All 0.5s ease;
}

.ios-refresh-bar image.loading-finish{
    width: 30rpx;
    height: 30rpx;
    margin-bottom: -6rpx;
}

.refresh-tips-txt{
    font-size: 14Px;
    color: #fff;
}
.wrapper {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
  }
  
  .block {
    width: 120px;
    height: 120px;
    background-color: #fff;
  }
