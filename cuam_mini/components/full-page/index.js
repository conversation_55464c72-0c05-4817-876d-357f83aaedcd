import { regeneratorRuntime, eventName, util, enums, storage, global, interaction } from '../../common/index.js'
import { setStatus } from '../../common/nb/home'

// import {
//   platform,
//   titleHeight,
//   navigationBarHeight,
//   navBottomFloat,
//   statusBarHeight,
//   footHeight,
//   screenHeight,
//   getContentHeight,
//   shareBottomSize,
//   getContentHeightPx
// } from '../../common/const/systeminfo.js'

const {
  platform,
  titleHeight,
  navigationBarHeight,
  navBottomFloat,
  footHeight,
  screenHeight,
  getContentHeightPx
} = getApp().appWindow;

const {
  TabBarType,
  SENSORS_PAGE
} = enums

const {
  SET_FULL_OPACITY,
  SET_LISTENER_TAB_BAR,
  SET_PULL_BAR,
  FULL_PAGE_SCROLL_LISTENER,
  CHANGE_POCUDT_CARE,
  CHANGE_PRODUCT_START
} = eventName

const {
  rpx2px,
  getSelectList
} = util

const {
  properties
} = TabBarType || {}

const LOADING_HEIGHT = 136


/**
 * 1：首页类型 => 顶部到头，无导航栏，tabBar，有logo 【适用于有沉浸式banner的首页】
 * 2. 普通类型 => 顶部不到头，有白色导航栏，tabBar，有logo【适用于正常卡片的tab首页】
 * @type {{"1": string, "2": string}}
 */
const PAGE_TYPE = {
  'HOME': 1,
  'NORMAL_CONTENT': 2,
}

Component({
  options: {
    addGlobalClass: true,
    multipleSlots: true // 在组件定义时的选项中启用多 slot 支持
  },

  properties: {
    // container背景色
    containerBgColor: {
      type: String,
      value: '#f7f7f7' // 默认 #f7f7f7
    },
    pageType: {
      type: Number,
      value: PAGE_TYPE['HOME'], // 默认HOME
      observer: 'onPageListener'
    },
    // navigation是否展示页面logo
    showPageLogo: {
      type: Boolean,
      value: true
    },
    // 是否展示tabBar
    showTabBar: {
      type: Boolean,
      value: true
    },
    tabBarType: {
      type: Number,
      value: 0
    },
    loginTipsColor: {
      type: String,
      value: '#8A8A8A'
    },
    formTab: {
      type: String,
      value: ''
    }
  },
  data: {
    /**
     * 1.devtools-android 与开发模式都使用 模式 安卓 开发板通过
     * 2.模式 ios独立使用，因为ios的反弹，重做
     */
    modePlat: platform === "ios",

    tBView: 'full-view',

    logoIcon: './images/<EMAIL>',
    titleHeight,
    navigationBarHeight,
    logoHeightSize: 30,
    navBottomFloat,
    contentHeight: rpx2px(screenHeight - footHeight),

    loadingHeight: 0,

    ic_arrow: '../full-scroll-view/images/arrow-i.png',
    ic_loading: '../full-scroll-view/images/loading-i.png',
    ic_finish: '../full-scroll-view/images/finish-i.png',

    refreshFinished: false,
    loading: false,
    pull: false,
    refreshingText: "下拉刷新",
    showTabBar: true,
    showModule: false,
    showModal: false,
    selectList: [],
    showAttention:false
  },

  attached() {
    // 展示弹框
    getApp().event.on('PRODUCT_SELECT_SHOW', (e) => {
      if (this.properties.formTab == e.fromTab) {
        this.setData({ showModal: !this.data.showModal, selectList: getSelectList(e) })
      }
    })
    getApp().event.on(CHANGE_PRODUCT_START,(e)=>{
      if(this.properties.formTab == e.fromTab){
        this.changeListCare(e)
      }
    })

    // console.log('======= FULL_PAGE attached >>>>', this.properties)
    // TODO 滑动顶部泛白
    getApp().event.on(SET_FULL_OPACITY, (value) => {
      // console.log('====== attached setOpacity SET_FULL_OPACITY >>>', value.fullType)
      if (value.fullType === PAGE_TYPE['NORMAL_CONTENT']) {
        this.setData({
          tBView: 'content-view',
          logoIcon: './images/<EMAIL>'
        })
      } else {
        this.setData({
          tBView: 'full-view',
          logoIcon: './images/<EMAIL>'
        })
      }
    });

    getApp().event.on(SET_PULL_BAR, params => {
      const { type, value } = params || {}
      // console.log('====== SET_PULL_BAR type,value >>>', type, value)

      switch (type) {
        case 'pull': {
          if (value <= LOADING_HEIGHT) {
            this.setData({
              refreshFinished: false,
              loadingHeight: value,
              refreshingText: '下拉刷新',
              pull: true,
              loading: false,
            })
          } else {
            this.setData({
              refreshFinished: false,
              loadingHeight: Math.min(value, LOADING_HEIGHT * 1.25),
              refreshingText: '释放立即刷新',
              pull: false,
              loading: false,
            })
          }
          return
        }

        case 'refresh': {
          this.setData({
            refreshFinished: false,
            loadingHeight: LOADING_HEIGHT,
            refreshingText: '正在刷新...',
            loading: true,
            pull: false,
          })
          return
        }

        case 'abort': {
          this.setData({
            refreshFinished: false,
            loadingHeight: 0,
            refreshingText: '下拉刷新',
            pull: false,
            loading: false,
          }, () => this.setRefreshType({ type: 'refreshStore', value: 0 }))
          return
        }

        case 'store': {
          this.setData({
            refreshFinished: true,
            loadingHeight: 0,
            refreshingText: '下拉刷新',
            pull: false,
            loading: false,
          })
          return
        }

        case 'finished': {
          this.setData({
            refreshFinished: true,
            loadingHeight: LOADING_HEIGHT,
            refreshingText: '刷新完成',
            pull: false,
            loading: false,
          })
          this.triggerEvent('pulldownrefresh');
          return
        }

        default:
          break
      }
    })

    const { pageType } = this.properties || {}
    let contentHeight = getContentHeightPx(false, true, false)

    let tBView = 'full-view'
    let logoIcon = './images/<EMAIL>'
    if (pageType === PAGE_TYPE['NORMAL_CONTENT']) {
      tBView = 'content-view'
      logoIcon = './images/<EMAIL>'
    }

    this.setData({
      contentHeight,
      tBView,
      logoIcon
    })
  },

  ready() {

  },

  methods: {
    selectCallBack() {
      this.setData({ showModal: !this.data.showModal })
    },



  //更改产品列表关注状态
  async changeListCare(e){
    let hasAttentionModel = storage.getStorage(global.STORAGE_GLOBAL_ATTENTION_MODEL)
    const params = {
      fundCode:e.fundCode,
      isCare: !e.isCare,
    }
    const {success, msg,} = await setStatus(params)
    if(success){
      if(!hasAttentionModel && !e.isCare){
        this.setData({
          showAttention:true,
          startStatus: e
        },()=>{
          this.sensorsTrack(e)
          storage.setStorage(global.STORAGE_GLOBAL_ATTENTION_MODEL,true)
        })
      } else if(e.isCare){
        interaction.showToast('取消关注成功',1500)
      }else{
        if(hasAttentionModel){
          this.sensorsTrack(e)
          interaction.showToast('关注成功',1500)
        }
      }
      getApp()?.event?.emit(CHANGE_POCUDT_CARE,e);
    }else{
      interaction.showToast(msg)
    }
  },

  sensorsTrack(e){
     // 神策埋点业务逻辑
     let path = properties[this.properties.tabBarType].path.slice(1)
     console.log("SENSORS_PAGE[path]====",SENSORS_PAGE[path])
    getApp()?.sensors?.track('userClick', {
      pageType: SENSORS_PAGE[path],
      pageContent:  e?.cardName || 'tab名称_null',
      pageNum: e?.fundCode || '产品id_null',  
      export:'noExport' 
    })
  },

    chooseItem(e) {
      getApp().event.emit('PRODUCT_SELECT_SELECT', e.detail.item)
      this.setData({ showModal: !this.data.showModal })
    },

    onTabChange(e) {
      // console.log('====== FULL_PAGE onTabChange e >>>>', e)
      const {
        tabbar: {
          type = 0,
          tabName = '',
        }
      } = e.detail || {}
      let _path = '/pages/home/<USER>'
      if (tabName) {
        // if(tabName =="NEWS" || tabName == "MINE"){
        //   getApp().event.emit(SET_FULL_OPACITY, {fullType:2,scrollTop:0})
        // }else{
        //   getApp().event.emit(SET_FULL_OPACITY, {fullType:1,scrollTop:0})
        //   getApp().event.emit("TAB_CHANGE")
        // }
        getApp().event.emit(SET_FULL_OPACITY, { fullType: 1, scrollTop: 0 })
        getApp().event.emit("TAB_CHANGE")
        _path = properties[type * 1].path;
      }
      console.log('====== path >>>', _path)
      getApp().event.emit(SET_LISTENER_TAB_BAR, type)
      getApp().event.emit("PERMISSIONMODAL")
      wx.switchTab({
        url: `${_path}`
      })
    },

    onPageListener() {
      // console.log('====== onPageListener newVal, oldVal >>>', newVal, oldVal)

    },

    setRefreshType(params = {}) {
      const { type, value } = params || {}
      getApp().event.emit(FULL_PAGE_SCROLL_LISTENER, { type, value })
    },
  }
});
