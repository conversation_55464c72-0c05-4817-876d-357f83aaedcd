<view class='container' style='background-color:{{$state.themeColor}};'>
  <block wx:for='{{tabs}}' wx:key='index'>
    <view class='tab' style='width:{{100 / tabs.length}}%;' bindtap='clickTab' data-index='{{index}}'>
      <view class='text' style='font-weight:{{index == currentIndex ? "bold" : "normal"}};'>{{item.value}}</view>
      <view class='indicator' style='background-color:{{currentIndex == index ? "#fff" : ""}}'></view>
    </view>
  </block>
</view>
