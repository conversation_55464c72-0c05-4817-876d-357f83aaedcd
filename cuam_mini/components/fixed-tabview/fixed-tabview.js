Component({
  properties: {
    tabs: {
      type: Object,
      value: []
    },
    styles: {
      type: Object,
      value: {}
    },
    enabled: {
      type: Boolean,
      value: true
    },
    currentIndex: {
      type: Number,
      value: 0
    }
  },
  data: {
    // currentIndex: 0
  },
  methods: {
    clickTab(e) {
      if (this.data.enabled) {
        const currentIndex = e.currentTarget.dataset.index
        this.setData({ currentIndex })
        this.triggerEvent('fixedtabbarclick', { currentIndex }, {})
      }
    }
  }
})