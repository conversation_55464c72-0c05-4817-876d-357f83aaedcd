// import {
//   titleHeight,
//   statusBarHeight,
//   titleHeightPx,
//   statusBarHeightPx
// } from '../../common/const/systeminfo.js'
import {eventName} from "../../common/index.js";

const {
  BREAK_IN_INIT_SUCCESS
} = eventName

const {
  titleHeight,
  statusBarHeight,
  titleHeightPx,
  statusBarHeightPx
} = getApp().appWindow;

const {event}= getApp()

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    //  决定navback是否有效
    enable: {
      type: Boolean,
      value: 'true'
    },
    //  可传入改变navbar样式
    bgStyle: {
      type: String,
      // value: getApp().store.$state.config.color.primary
      color: '#fff'
    },
    //  可传入改变navbar title样式
    titleStyle: {
      type: String,
      value: 'color: black;',
    },
    title: {
      type: String,
      value: '',
      observer: '_changeTitle',
    },
    //  可传入改变nav back页面数
    delta: {
      type: Number,
      value: 1
    },
    //  决定是否显示loading
    showLoading: {
      type: Boolean,
      value: false
    },
    textStyle: {
      type: String,
      value: 'black',
      observer: '_changeTextStyle'
    },
    showBack: {
      type: Boolean,
      value: false
    },
    showHomeIcon: {
      type: Boolean,
      value: false
    },
    showLeftCustomIcon: {
      type: Boolean,
      value: false
    },
    customLeftIconUrl: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    statusBarHeight: statusBarHeightPx,
    navHeight: titleHeightPx,
    navIconUrl: '/imgs/nav_icon_white.png',
    navIconHome: '/imgs/nav_icon_home.png',
    navNormalIconUrl: '/imgs/nav_icon_black.png',
    // navTitleStyle: 'color: white;',
    navTitleStyle: 'color: black;',
    navBgStyle: 'background-color:#000;',
    model: {}
  },

  attached() {
    // console.log('===== navigation this.props >>>', this.properties)
  },

  methods: {
    // 点击home图标，返回首页
    async _backToHome() {
      // this.clearShareData()
      this.reRouteStartUp()
    },

    //  title监听函数
    _changeTitle() {
      if (this.data.title === ''){
        this.setData({
          title: ''
        })
      }
    },
    _changeTextStyle() {
      // console.log('======= _changeTextStyle >>>>',this.data.textStyle)
      if (this.data.textStyle === 'black'){
        wx.setNavigationBarColor({
          frontColor: '#000000',
        })
        this.setData({
          navIconUrl: '/imgs/nav_icon_black.png',
          navTitleStyle: 'color: black;',
          navBgStyle: 'background-color:#fff;',
        })
      } else {
        wx.setNavigationBarColor({
          frontColor: '#ffffff',
        })
        this.setData({
          navIconUrl: '/imgs/nav_icon_white.png',
          navTitleStyle: 'color: white;',
          navBgStyle: 'background-color:#000;',
        })
      }
    },

    //  navback监听函数
    async _onPressBack() {
      this.triggerEvent('back', {})
      if (this.data.enable){
        let pages = getCurrentPages()
        if (pages.length < 2){
          return this.goToHome()
        } else {
          wx.navigateBack({
            delta: this.data.delta
          })
        }
      }
    },
    _onPressNavLeft() {
      this.triggerEvent('onPressNavLeft', {})
    },

    async reRouteStartUp() {
      // let _path = await getApp().getFirstTabPath(true)
      // if (!_path){
      //   _path = `/pages/home/<USER>
      // }

      // if (!`${_path}`.startsWith('/')){
      //   _path = `/${_path}`
      // }
      // return wx.switchTab({
      //   url: `${_path}`
      // })
      event.emit(BREAK_IN_INIT_SUCCESS)
      return wx.reLaunch({
        url: '/pages/loginAndRegist/startUp/index',
      })
    },

    goToHome() {
      console.log('======= goToHome ')
      return wx.switchTab({
        url: '/pages/home/<USER>'
      })
    },
  },

  lifetimes: {
    attached() {
      var model = wx.getMenuButtonBoundingClientRect()
      this.setData({
        model: model
      })
    }
  },

})
