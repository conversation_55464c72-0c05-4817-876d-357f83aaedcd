.tool{
  position: fixed;
  z-index: 999999;
}
.nav{
  right: -6rpx;
  top: 136rpx;
}
.showHideCardbg{
  width: 250rpx;
}
.show-image{
  width: 42rpx;
  height: 100rpx;
  margin-left: 212rpx;
  /* margin-right: -8rpx; */
}
.hide-card{
  position: absolute;
  top: 0;
  right: 16rpx;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
}
.hide-image{
  width: 137rpx;
  position: relative;
  /* margin-top: -10rpx; */
  margin-left: 108rpx;
  /* top: -20rpx; */
  /* left:  108rpx; */
  /* right: 0rpx; */
}
.card-box{
  width: 129rpx;
  /* border-radius: 30rpx 0rpx 0rpx 30rpx; */
}
.card-name{
  margin-top: 18rpx;
  width: 88rpx;
  margin-left: 54rpx;
  font-size: 24rpx;
  color: #5B5B5B;
}
.setting-text{
  width: 78rpx;
  height: 78rpx;
  margin-left: 50rpx;
  margin-top:24rpx;
}
.card-grade{
  margin-top: 4rpx;
  margin-left: 50rpx;
  font-size: 20rpx;
  color: #999999;
}
.line{
  width: 118rpx;
  height: 1.34rpx;
  opacity: 0.65;
  background: linear-gradient(90deg,#ffffff, #999999, #ffffff);
  margin-left: 12rpx;
}
.phone{
  width: 50rpx;
  height: 50rpx;
  margin-left: 64rpx;
}
.bottom{
  left: 246rpx;
  bottom: 198rpx;
}
.bottom-bg{
  width: 258rpx;
  height: 102rpx;
  position: relative;
  top: 0;
  left: 0;
}
.bottom-button{
  position: absolute;
  top: 0rpx;
  left: 0rpx;
  width: 258rpx;
  height: 102rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  /* border-radius: 40rpx; */
  /* background: linear-gradient(90deg,#E81C1C, #CB1717); */
  /* background: #E81C1C; */
}
.bottom-text{
  font-size: 32rpx;
  color: #ffffff;
}
.setting{
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  z-index: 9999999;
}
.setting-box{
  margin-top: 353rpx;
  margin-left: 75rpx;
  width: 600rpx;
  border-radius: 18rpx 18rpx 18rpx 18rpx;
  background: #f3f4f5;
}
.setting-card-title{
  font-size: 34rpx;
  color: #212224;
  font-weight: 600;
  margin-left: 165rpx;
  margin-top: 40rpx;
}
.setting-card-close{
  width: 23rpx;
  height: 23rpx;
  position: fixed;
  top: 383rpx;
  right: 104rpx;
}
.setting-card-subtitle{
  font-size: 20rpx;
  color: #666666;
  margin-left: 41rpx;
  margin-top: 29rpx;
}
.setting-card-select-box{
  width: 540rpx;
  height: 164.81rpx;
  border-radius: 14rpx 14rpx 14rpx 14rpx;
  background: #ffffff;
  margin-top: 40rpx;
  margin-left: 30rpx;
}
.setting-card-select-title-box{
  height: 80rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  padding-left: 50rpx;
  padding-right: 50rpx;
}
.setting-card-select-title{
  font-size: 28rpx;
  color: #666666;
}
.setting-card-select-line{
  margin-left: 40rpx;
  width: 459rpx;
  height: 1rpx;
  opacity: 0.84;
  background: #eeeeee;
}
.setting-card-border{
  width: 37rpx;
  height: 37rpx;
}
.setting-card-example{
  width: 540rpx;
  margin-left: 30rpx;
}
.setting-card-btn{
  width: 540rpx;
  height: 68rpx;
  border-radius: 34rpx 34rpx 34rpx 34rpx;
  background: #e81c1c;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-left: 30rpx;
}
.setting-card-btn-text{
  font-size: 30rpx;
  color: #ffffff;
}
.link-model{
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99999999;
  background: rgba(0, 0, 0, 0.6);
}
.share-link-model{
  width: 100%;
  height: 100%;
}
.phone-box{
  left: 0;
  top: 0;
  width: 100%;
  height: 118rpx;
}
.image-bg{
  width: 100%;
  height: 118rpx;
  position: relative;
  top: 0;
  left: 0;
}
.phone-box-bg{
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  align-items: center;
  padding: 15rpx 35rpx;
}
.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 40rpx;
  overflow: hidden;
}
.image-all {
  width: 40px;
  height: 40px;
  border-radius: 20px;
}
.user {
  color: #ffffff;
  padding-top: 7rpx;
  padding-bottom: 10rpx;
  margin-left: 24rpx;
  width: calc(56% - 30rpx);
  display: flex;
  flex-direction: column;
  justify-content: center;
}
.user-name {
  font-weight: 500;
  font-size: 30rpx;
}
.user-position {
  font-weight: 400;
  font-size: 24rpx;
  margin-top: 12rpx;
  margin-right: 12rpx;
}
.phone-btn {
  width: 191rpx;
  height: 61rpx;
  margin-top: 9rpx;
  margin-right: 10rpx;
}
.btn-image {
  width: 191rpx;
  height: 61rpx;
}
.line-box{
  display: flex;
  flex-direction: row;
  align-items: center;
}
.card-style{
  width: 81rpx;
  height: 30rpx;
  margin-left: 12rpx;
  /* padding: 4rpx 11rpx; */
}
.triangle{
  width: 81rpx;
  height: 30rpx;
}
.partition{
  width: 1rpx;
  height: 19rpx;
  opacity: 0.65;
  background-color: #ffffff;
  margin-right: 12rpx;
  margin-top: 12rpx;
}
.line-top{
  margin-top: 8rpx;
}