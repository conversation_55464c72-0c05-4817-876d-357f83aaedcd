import { getMobileDTOById } from '../../common/nb/home'
import { enums, util, jumpTarget, storage, global, api, action, nbHome, wbs, eventName, qs, userStorage} from '../../common/index'
const { FLOATING_TYPE, LOGIN_VISITOR, PAGE_INFO_REALM, showUserCard} = enums
const { getQueryParams, parseBool } = util
const { getStaffInfo } = api
const { checkIsHtfPerson, toBussinessCard } = action
const { setShareConfig } = nbHome
const { SEND_WEBVIEW_OPTIONS } = eventName
const { getUser, setWebPageShowWay } = userStorage
import newConfig from "../../config/index.js";

const { env, } = newConfig;

const newsListTag = [
  {categoryIds:'2c93809287088e0a01870d36d0c9007d,2c93808b8996a35301899a2cab4e00c7,2c93808b899a3af00189aed5178a0078,2c93809287088e0a01870c68a1ba0025,2c93808f86c00ac40186c037111a0006',id:'3286202589392429'},
  {},
  {categoryIds:"2c928084835516bd01836338a61d0002,2c958084836fd0e701837076a43e0014,2c958084836fd0e70183707d71e10018",id:"3419624104464719"},
  {categoryIds:"2c928084835516bd01836338a61d0002,2c958084836fd0e701837076a43e0014,2c958084836fd0e70183707d71e10018",id:"3419624104464719"},
]
const {
  titleHeight,
  platform,
} = getApp().appWindow;

Component({
  properties: {
    showNav:{
      type:Boolean,
      value:true
    },
    userId:{
      type:String,
      value:''
    },
    visitingCard:{ 
      type: Number,
      value: showUserCard.show
    },
    showNormalCard:{
      type:Boolean,
      value:false
    },
    showXueQiuCard:{
      type:Boolean,
      value:false
    },
    showVisiting:{
      type:Boolean,
      value:true
    }
  },
  data: {
    hasLogin:true,
    showModel:false,
    showLink:false,
    navHeight: titleHeight,
    showUserCard,
    isHtfPerson:false,
    showAllCard:true,
    showHideCard:false,
    isIOS: platform == 'ios',
    showVisitingCard:true,
    showUserId:true,
    showWXCard:true
  },
  methods: {
    clickShowCard(){
      let changeCardTimer = setTimeout(()=>{
        clearTimeout(changeCardTimer)
        this.setData({
          showAllCard:true,
          showHideCard:false
        })
      },500)
    },
    clickHideCard(){
      let hideCardTimer = setTimeout(()=>{
        clearTimeout(hideCardTimer)
        this.setData({
          showAllCard:false,
          showHideCard:true
        })
      },500)
    },
    clickButtom(){
      const { hasLogin } = this.data
      const { showNormalCard, showXueQiuCard} = this.properties
      if(!hasLogin){
        return wx.navigateTo({
          url: '/pages/loginAndRegist/login/login',
        })
      }
      if(showNormalCard){
        let params = {
          type: 'ARTICLE',
          categoryIds:newsListTag[env.serviceTag].categoryIds,
          name:'内部投资策略',
          cardType:"SingleContent",
          cardName:"内部投资策略",
          isShareCome:false
        }
        return wx.navigateTo({
          url: `/pages/home/<USER>/list?${qs.stringify(params,{encode:false})}` ,
        });
      }
      if(showXueQiuCard){
        let params = {
          id:newsListTag[env.serviceTag].id,
          name:'市场热点追踪'
        }
        return wx.navigateTo({
          url: `/package-activity/pages/marketnewsList/index?${qs.stringify(params,{encode:false})}`,
        });
      }
    },
    clickLink(){
      this.setData({
        showLink:true,
        showModel:false
      })
      let timer = setTimeout(()=>{
        clearTimeout(timer)
        this.setData({showLink:false})
      },2000)
    },
    closeModel(){
      this.setData({
        showLink:false
      })
    },
    async changeVisitingCard(event){
      const { name } = event.currentTarget.dataset;
      await setShareConfig({useCard: name});
      this.setData({
        visitingCard:name
      },()=> this.triggerEvent('changeVisitingCard',name))
    },
    controlModel(){
      let showModel = this.data.showModel
      let showModelTime = setTimeout(()=>{
        clearTimeout(showModelTime)
        this.setData({
          showModel:!showModel
        })
      },500)
 
    },
    //获取人员信息
    async getStaff() {
      const { code, data } = await getStaffInfo({
        userId: this.properties.userId,
      });
      console.log("**********", code, data);

      if (code == 0) {
        this.setData({ phoneDetail: data }, () => {
          this.checkIsHtfPerson();
        });
      }
    },

    //判断是否为汇添富员工
    async checkIsHtfPerson(){
      const { phoneDetail}  = this.data
      const res = await checkIsHtfPerson(phoneDetail.phone)
      if(res.returnMsg == 'yes'){
        this.setData({
          isHtfPerson: true
        })
      }
    },

      // 拨打电话
    makeCall() {
      const { phoneDetail } = this.data
      let userInfo = getUser();
      if(userInfo?.userId == this.properties.userId){
        return this.toMyCard()
      }
      console.log('userInfo===',userInfo.userId)
      wx.makePhoneCall({
        phoneNumber: phoneDetail.phone,
      });
    },

    // 跳转个人名片
    toMyCard() {
      const { phoneDetail}  = this.data
      if(this.data.isHtfPerson){
        return toBussinessCard(phoneDetail.phone)
      }
      let params = {
        pageType: "shareCard",
        perfix: `${wbs.gfH5}/share/shareCard`,
        title: "智能名片",
        faId: this.properties.userId,
      };

      return wx.navigateTo({
        url: "/pages/common/webview/webPage",
        success(res) {
          res.eventChannel.emit(SEND_WEBVIEW_OPTIONS, params);
          setWebPageShowWay(1);
        },
      });
    },
  },
  created: function () {},
  attached: function () {
    console.log('env=====',env.serviceTag)
    let userRole = storage.getStorage(global.STORAGE_GLOBAL_USER_ROLE);
    let hasLogin = !LOGIN_VISITOR.includes(userRole * 1)
    const { visitingCard, userId , showVisiting} = this.properties
    console.log('showUserId', userId != 'false')
    let userInfo = getUser();
    if((userInfo?.userId != this.properties.userId) && !showVisiting){
      this.setData({
        showVisitingCard:false
      })
    }
    this.setData({
      hasLogin,
      visitingCard,
      showUserId: userId != 'false',
    },()=>{
      this.getStaff(); // 展示名片
    })
  },
  ready: function () {},
  moved: function () {},
  detached: function () {},
});
