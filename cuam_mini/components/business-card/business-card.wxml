<view>
  <!-- 非资讯外链名片隐藏 -->
  <cover-view class="tool nav showHideCardbg" bindtap="clickShowCard"  wx:if="{{showNormalCard && showHideCard && showVisitingCard && showWXCard}}">
    <cover-image src='/imgs/card/show-card.png' mode="aspectFill" class="show-image"/>
  </cover-view>
   <!-- 非资讯外链名片展示 -->
  <cover-view class="tool nav showHideCardbg"  bindtap="clickHideCard"  wx:if="{{showNormalCard && showAllCard && showVisitingCard && showWXCard}}">
      <cover-image 
        src="{{hasLogin?'/imgs/card/card-big.png':'/imgs/card/card-small.png'}}" 
        mode="aspectFill" class="hide-image"
        style="height:{{hasLogin?216:128}}rpx"
      />
      <cover-view class='hide-card'>
        <cover-view  class="card-box">
          <cover-view catchtap="toMyCard" class='card-name' style="margin-left:{{phoneDetail.name.length>2?54:66}}rpx">{{phoneDetail.name}}</cover-view>
          <!-- <cover-view bindtap="toMyCard" class='card-grade'>{{isHtfPerson?"汇添富基金":"客户经理"}}</cover-view> -->
          <cover-image catchtap="toMyCard" src='/imgs/card/user-card.png' mode="aspectFill" class="phone" style="margin-top:{{hasLogin?6:20}}rpx"/>
          <cover-image wx:if="{{hasLogin}}" catchtap="controlModel" src='/imgs/card/setting-text.png'  class="setting-text"/>
          <!-- <cover-view wx:if="{{hasLogin}}" class='card-grade' bindtap="controlModel" style="color:#A7A7A7">分享设置</cover-view> -->
        </cover-view>
      </cover-view>
  </cover-view>

  <!-- 分享引导浮窗 -->
  <cover-view class="tool setting" wx:if="{{showModel}}" bindtap="controlModel">
    <cover-view class="setting-box" style="height: {{visitingCard == showUserCard.show?890:519}}rpx;">
      <!-- <cover-view></cover-view> -->
      <cover-view class="setting-card-title">是否使用名片转发</cover-view>
      <cover-image catchtap="controlModel" src='/imgs/card/icon-close.png' mode="aspectFill" class="setting-card-close"/>
      <cover-view class="setting-card-subtitle">这将决定用户打开您分享的内容后，是否看到您的名片信息</cover-view>
      <cover-view class="setting-card-select-box">
        <cover-view class="setting-card-select-title-box" catchtap="changeVisitingCard" data-name="{{showUserCard.show}}">
          <cover-view class='setting-card-select-title'>用我的名片转发</cover-view>
          <cover-image src="{{visitingCard == showUserCard.show?'/imgs/card/red-border.png' :'/imgs/card/border.png' }}" mode="aspectFill" class="setting-card-border"/>
        </cover-view>
        <cover-view class="setting-card-select-line"></cover-view>
        <cover-view class="setting-card-select-title-box" catchtap="changeVisitingCard" data-name="{{showUserCard.hide}}">
          <cover-view class='setting-card-select-title'>不展示名片信息转发</cover-view>
          <cover-image src="{{visitingCard != showUserCard.show?'/imgs/card/red-border.png' :'/imgs/card/border.png' }}"  mode="aspectFill" class="setting-card-border"/>
        </cover-view>
      </cover-view>
      <cover-image src='https://pic-aim-htffund.oss-cn-shanghai.aliyuncs.com/image/course/2023-11-06/582a6bd9-7358-4f13-bf4c-4be4f275ed19.png' mode="aspectFill" class="setting-card-example" style="height:{{visitingCard == showUserCard.show?369:0}}rpx;margin-top: {{visitingCard == showUserCard.show?38:0}}rpx;"/>
      <cover-view catchtap="clickLink" class='setting-card-btn' style="margin-top:{{visitingCard == showUserCard.show?44:77}}rpx">
        <cover-view class='setting-card-btn-text'>继续分享</cover-view>
      </cover-view>
    </cover-view> 
  </cover-view>

  <!-- 引导蒙层 -->
  <cover-view class="tool link-model" wx:if="{{showLink}}" catchtap="closeModel">
    <cover-image  src='/imgs/card/share-link.png' mode="widthFix" class="share-link-model"/>
  </cover-view>


  <!-- 底部按钮 -->
  <cover-view wx:if="{{showXueQiuCard || !hasLogin}}" class="tool bottom" bindtap="clickButtom" style="bottom:{{isIOS?198:260}}rpx;height:{{isIOS && showXueQiuCard?130:102}}rpx;">
    <cover-image class="bottom-bg" src="/imgs/card/more.png" style="height:{{isIOS && showXueQiuCard?130:102}}rpx"/>
    <cover-view class="bottom-button" style="height:{{isIOS && showXueQiuCard?130:102}}rpx">
      <cover-view class="bottom-text">{{hasLogin?"查看更多":"立即注册"}}</cover-view>
    </cover-view>
  </cover-view>

  <!-- 雪球名片 -->
  <cover-view class="tool phone-box" wx:if="{{showXueQiuCard && showUserId}}">
    <cover-image class="image-bg" src="/imgs/card/card-bg.png"/>
    <cover-view class='phone-box-bg'>
      <cover-view class="avatar">
        <cover-image
          class="image-all"
          src="{{phoneDetail.avatar}}"
        />
      </cover-view>
      <cover-view class="user">
        <cover-view class='line-box line-top'>
          <cover-view class="user-name">{{phoneDetail.name}}</cover-view>
          <cover-view class="card-style" bind:tap="toMyCard">
            <cover-image src="/imgs/card/card-bg.jpg" class="triangle"/>
          </cover-view>
        </cover-view>
        <cover-view class='line-box'>
          <cover-view class="user-position">{{isHtfPerson? '汇添富基金': phoneDetail.companyName}}</cover-view>
          <cover-view class="partition" wx:if="{{!isHtfPerson}}"></cover-view>
          <cover-view class="user-position" wx:if="{{!isHtfPerson}}">客户经理</cover-view>
        </cover-view>
      </cover-view>
      <cover-view class="phone-btn" catchtap="makeCall">
        <cover-image class="btn-image" src="/imgs/card/phone-bg.jpg" />
      </cover-view>
    </cover-view>
  </cover-view>
</view>
