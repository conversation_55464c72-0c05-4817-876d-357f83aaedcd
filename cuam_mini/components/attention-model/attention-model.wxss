.modal {
  position: fixed;
  top: 0;
  left: 0;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  width: 100%;
  height: 100%;
  z-index: 99999;
  background: rgba(0, 0, 0, 0.3);
}
.status-box{
  display: flex;
  align-items: flex-end;
  justify-content: center;
  height: 100%;
}
.content-box{
  display: flex;
  flex: 1;
  flex-direction: column;
}
.header-box-content{
  margin-bottom: -110rpx;
  position: relative;
}
.header-text{
  position:absolute;
  top: 28rpx;
  left: 273rpx;
  font-size: 34rpx;
  font-weight: 500;
  color: #fff;
}
.header-box{
  width: 100%;
  height: 202rpx;
}
.block{
  width: 100%;
  background-color: #fff;
  border-radius: 20rpx 20rpx 0 0;
  z-index: 12;
}
.intro-img{
  margin: 30rpx;
  margin-top: 34rpx;
  margin-bottom: 20rpx;
  width: 690rpx;
  height: 570rpx;
}
.block-content{
  margin:0 30rpx;
  background-color: #FF8888;
  width: 690rpx;
  height: 249rpx;
  background: #FFF6F5;
  border: 1rpx solid #FF8888;
  border-radius: 16rpx;
}
.block-content-title{
  font-size: 30rpx;
  font-weight: 500;
  color: #333;
  margin: 26rpx;
}
.block-content-line{
  margin-left: 26rpx;
  margin-top: -40rpx;
  width: 187rpx;
  height: 10rpx;
  background: linear-gradient(270deg, #ffe7e700 4%, #FF8989 81%);
  border-radius: 8rpx;
  border-radius: 5rpx;
}
.block-content-subtitle{
  margin-top: 20rpx;
  margin-left: 26rpx;
  font-size: 24rpx;
  color: #333;
  font-weight: 400;
}
.block-content-btn{
  margin: 30rpx 26rpx;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
}
.block-content-btn-item{
  width: 299rpx;
  height: 68rpx;
  border: 1rpx solid #E81C1C;
  border-radius: 100rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  color: #E32833;
}