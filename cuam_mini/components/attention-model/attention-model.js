import { enums, util, jumpTarget, storage, global, wbs, userStorage, qs} from '../../common/index'
const {
  geStorageUnionId,
  getOpenId,
  getToken,
  getWechatInfoId,
  setWebPageShowWay
} = userStorage
Component({
  properties: {
    showStatus: {
      type: Boolean,
      value: false
    },
    startStatus:{
      type: Object,
      value: {}
    }
  },
  data: {
    showStatus:false
  },
  attached: function () {
    console.log("properties====",this.properties)
  },
  methods: {
    closeStatus(){
      this.setData({showStatus:false})
    },
    jumpSetting(e){
      const { startStatus } = this.properties
      this.setData({showStatus:false})
      let params = {
        unionid:geStorageUnionId(),
        openid: getOpenId(),
        token: getToken(),
        wechatInfoId: getWechatInfoId(),
        fundName: encodeURIComponent(startStatus.fundName),
        fundCode: startStatus.fundCode,
        fromMyCard: startStatus.rangeNum?true:false
      }
      let url = `${wbs.gfH5}/channel_h5/fundreminder/#/?${qs.stringify(params,{encode:false})}`
      wx.navigateTo({
        url: `/pages/common/webview/webPage?url=${encodeURIComponent(url)}`,
        success(res) {
          setWebPageShowWay(1);
        },
      });
    },
  }
})